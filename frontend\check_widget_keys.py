#!/usr/bin/env python3
"""
檢查Streamlit widget key的腳本
確保所有按鈕都有唯一的key，避免DuplicateWidgetID錯誤
"""

import os
import re
from pathlib import Path

def check_widget_keys():
    """檢查所有Streamlit widget的key"""
    print("🔍 檢查Streamlit Widget Keys")
    print("=" * 50)
    
    # 要檢查的檔案
    files_to_check = [
        "app.py",
        "pages/dashboard.py",
        "pages/login.py",
        "pages/rooms.py", 
        "pages/residents.py",
        "pages/utilities.py",
        "pages/reports.py"
    ]
    
    button_texts = {}  # 儲存按鈕文字和其位置
    button_keys = {}   # 儲存按鈕key和其位置
    issues_found = []
    
    for file_path in files_to_check:
        full_path = Path(f"frontend/{file_path}")
        if not full_path.exists():
            print(f"⚠️  檔案不存在: {file_path}")
            continue
            
        print(f"\n📄 檢查檔案: {file_path}")
        
        with open(full_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
        
        # 查找所有st.button呼叫
        button_pattern = r'st\.button\s*\(\s*["\']([^"\']+)["\'](?:.*?key\s*=\s*["\']([^"\']+)["\'])?'
        
        for line_num, line in enumerate(lines, 1):
            matches = re.finditer(button_pattern, line)
            
            for match in matches:
                button_text = match.group(1)
                button_key = match.group(2) if match.group(2) else None
                
                location = f"{file_path}:{line_num}"
                
                # 檢查按鈕文字重複
                if button_text in button_texts:
                    if button_key is None:
                        issues_found.append({
                            "type": "missing_key",
                            "text": button_text,
                            "location": location,
                            "previous": button_texts[button_text]
                        })
                    else:
                        # 有key但文字重複，檢查key是否唯一
                        if button_key in button_keys:
                            issues_found.append({
                                "type": "duplicate_key", 
                                "key": button_key,
                                "location": location,
                                "previous": button_keys[button_key]
                            })
                else:
                    button_texts[button_text] = location
                
                # 記錄key
                if button_key:
                    if button_key in button_keys:
                        issues_found.append({
                            "type": "duplicate_key",
                            "key": button_key, 
                            "location": location,
                            "previous": button_keys[button_key]
                        })
                    else:
                        button_keys[button_key] = location
                        print(f"  ✅ {button_text} (key: {button_key})")
                else:
                    print(f"  ⚠️  {button_text} (無key)")
    
    # 報告結果
    print("\n" + "=" * 50)
    print("📋 檢查結果")
    
    if not issues_found:
        print("🎉 所有按鈕都有唯一的key！")
    else:
        print("❌ 發現問題:")
        
        for issue in issues_found:
            if issue["type"] == "missing_key":
                print(f"  - 缺少key: '{issue['text']}' 在 {issue['location']}")
                print(f"    與 {issue['previous']} 重複")
            elif issue["type"] == "duplicate_key":
                print(f"  - 重複key: '{issue['key']}' 在 {issue['location']}")
                print(f"    與 {issue['previous']} 重複")
    
    # 統計資訊
    print(f"\n📊 統計:")
    print(f"  - 總按鈕數: {len(button_texts)}")
    print(f"  - 有key的按鈕: {len(button_keys)}")
    print(f"  - 無key的按鈕: {len(button_texts) - len(button_keys)}")
    print(f"  - 發現問題: {len(issues_found)}")
    
    return len(issues_found) == 0

if __name__ == "__main__":
    success = check_widget_keys()
    exit(0 if success else 1)
