import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    """前端配置管理"""
    
    # API設定
    API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8080")
    
    # 應用程式設定
    APP_TITLE = "租房管理系統"
    APP_ICON = "🏠"
    
    # 頁面設定
    PAGE_CONFIG = {
        "page_title": APP_TITLE,
        "page_icon": APP_ICON,
        "layout": "wide",
        "initial_sidebar_state": "expanded"
    }
    
    # 狀態預設值
    DEFAULT_ELECTRICITY_RATE = 5.5
    DEFAULT_WATER_FEE = 200.0

config = Config()
