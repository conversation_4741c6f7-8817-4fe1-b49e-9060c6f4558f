#!/usr/bin/env python3
"""
檢查admin用戶
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.app.database import SessionLocal
from backend.app.models import User
from backend.app.auth import verify_password, get_password_hash

def check_admin_user():
    """檢查admin用戶"""
    db = SessionLocal()
    try:
        admin_user = db.query(User).filter(User.username == "admin").first()
        
        if admin_user:
            print(f"✅ 找到admin用戶:")
            print(f"  ID: {admin_user.id}")
            print(f"  用戶名: {admin_user.username}")
            print(f"  角色: {admin_user.role}")
            print(f"  啟用: {admin_user.is_active}")
            print(f"  電子郵件: {admin_user.email}")
            
            # 測試密碼
            test_password = "admin123"
            is_valid = verify_password(test_password, admin_user.password_hash)
            print(f"  密碼驗證 (admin123): {'✅ 正確' if is_valid else '❌ 錯誤'}")
            
            if not is_valid:
                print("🔧 重新設置admin密碼...")
                admin_user.password_hash = get_password_hash("admin123")
                db.commit()
                print("✅ admin密碼已重新設置")
                
        else:
            print("❌ 沒有找到admin用戶")
            print("🔧 創建admin用戶...")
            admin_user = User(
                username="admin",
                password_hash=get_password_hash("admin123"),
                email="<EMAIL>",
                role="admin",
                is_active=True
            )
            db.add(admin_user)
            db.commit()
            print("✅ admin用戶創建成功")
            
    except Exception as e:
        print(f"❌ 錯誤: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    check_admin_user()
