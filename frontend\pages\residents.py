import streamlit as st
import pandas as pd
from datetime import datetime, date, timedelta
from api_client import api_client
from utils import (format_currency, format_date, show_success_message,
                   show_error_message, show_warning_message, show_info_message,
                   validate_id_number, validate_phone_number)
import sys
import os
# 添加父目錄到路徑以便導入date_utils
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from frontend.date_utils import format_date_roc, roc_date_input, roc_calendar_input, format_date_for_api, create_roc_date_display

def show_residents_page():
    """顯示住戶管理頁面"""
    st.title("👥 住戶管理")

    # 處理租期展期確認對話框
    if st.session_state.get('confirm_extension'):
        show_lease_extension_confirmation()

    # 頁籤設計
    tab1, tab2, tab3 = st.tabs(["住戶列表", "新增住戶", "住戶統計"])

    with tab1:
        show_residents_list()

    with tab2:
        show_create_resident_form()

    with tab3:
        show_residents_statistics()

def show_residents_list():
    """顯示住戶列表"""
    st.subheader("住戶列表")
    
    # 搜尋和篩選
    col1, col2, col3 = st.columns([2, 1, 1])
    
    with col1:
        search_term = st.text_input("🔍 搜尋住戶", placeholder="輸入姓名或身份證號")
    
    with col2:
        status_filter = st.selectbox("狀態篩選", ["在住", "全部", "已退房"])
    
    with col3:
        if st.button("🔄 刷新數據", key="residents_refresh"):
            st.rerun()
    
    # 獲取住戶數據
    if status_filter == "在住":
        residents = api_client.get_residents(active_only=True)
    elif status_filter == "已退房":
        residents = api_client.get_residents(active_only=False)
        # 只保留已退房的住戶
        residents = [r for r in residents if not r['is_active']] if residents else []
    else:  # 全部
        residents = api_client.get_residents(active_only=False)

    if not residents:
        show_info_message("暫無住戶資料")
        return

    # 數據篩選
    filtered_residents = residents

    if search_term:
        filtered_residents = [r for r in filtered_residents
                            if search_term.lower() in r['name'].lower()
                            or search_term.lower() in r['id_number'].lower()]
    
    # 數據表格
    if filtered_residents:
        # 準備顯示數據
        display_data = []
        for resident in filtered_residents:
            room_info = resident.get('room', {})
            display_data.append({
                "姓名": resident['name'],
                "身份證號": resident['id_number'],
                "電話": resident['phone'] or 'N/A',
                "房間": room_info.get('room_number', 'N/A'),
                "入住日期": format_date_roc(resident['move_in_date'], "short"),
                "租約到期": format_date_roc(resident['lease_end_date'], "short") if resident['lease_end_date'] else 'N/A',
                "退房日期": format_date_roc(resident['move_out_date'], "short") if resident['move_out_date'] else 'N/A',
                "押金": format_currency(resident['deposit']),
                "狀態": "在住" if resident['is_active'] else "已退房"
            })
        
        df = pd.DataFrame(display_data)
        st.dataframe(df, use_container_width=True)
        
        # 住戶操作
        st.subheader("住戶操作")
        
        selected_resident_idx = st.selectbox(
            "選擇住戶",
            options=range(len(filtered_residents)),
            format_func=lambda x: f"{x+1}. {filtered_residents[x]['name']} ({filtered_residents[x]['id_number']})"
        )
        
        if selected_resident_idx is not None:
            selected_resident = filtered_residents[selected_resident_idx]
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                if st.button("📋 查看詳情", key="residents_view_details", use_container_width=True):
                    show_resident_details(selected_resident)

            with col2:
                if st.button("✏️ 編輯住戶", key="residents_edit", use_container_width=True):
                    st.session_state.editing_resident = selected_resident
                    st.session_state.show_edit_resident_form = True
                    st.rerun()

            with col3:
                if selected_resident['is_active'] and st.button("🚪 辦理退房", key="residents_move_out", use_container_width=True):
                    st.session_state.move_out_resident = selected_resident
                    st.session_state.show_move_out_form = True
                    st.rerun()
    else:
        show_info_message("沒有符合條件的住戶")

    # 顯示編輯表單
    if st.session_state.get('show_edit_resident_form', False) and st.session_state.get('editing_resident'):
        st.markdown("---")
        show_edit_resident_form(st.session_state.editing_resident)

    # 顯示退房表單
    if st.session_state.get('show_move_out_form', False) and st.session_state.get('move_out_resident'):
        st.markdown("---")
        show_move_out_form(st.session_state.move_out_resident)

def show_create_resident_form():
    """顯示新增住戶表單"""
    st.subheader("新增住戶")
    
    # 獲取可用房間
    available_rooms = api_client.get_available_rooms()
    
    if not available_rooms:
        show_warning_message("目前沒有可用房間，請先新增房間或檢查房間狀態")
        return
    
    with st.form("create_resident_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            name = st.text_input("姓名*", placeholder="請輸入住戶姓名")
            id_number = st.text_input("身份證號*", placeholder="例如: A123456789")
            phone = st.text_input("聯絡電話", placeholder="例如: 0912345678")
        
        with col2:
            room_options = {f"{room['room_number']} (可住{room['max_occupants'] - room['current_occupants']}人)": room['id'] 
                          for room in available_rooms}
            selected_room = st.selectbox("選擇房間*", options=list(room_options.keys()))
            
            move_in_date = roc_calendar_input("入住日期*", value=date.today(), help="請選擇住戶入住日期", key="create_resident_move_in")
            lease_end_date = roc_calendar_input("租約到期日", value=None, help="請選擇租約到期日期（可選）", key="create_resident_lease_end")
            deposit = st.number_input("押金", min_value=0.0, value=0.0, step=100.0)
        
        # 緊急聯絡人資訊
        st.subheader("緊急聯絡人")
        col1, col2 = st.columns(2)
        
        with col1:
            emergency_contact = st.text_input("緊急聯絡人姓名", placeholder="請輸入緊急聯絡人")
        
        with col2:
            emergency_phone = st.text_input("緊急聯絡人電話", placeholder="例如: 0912345678")
        
        submitted = st.form_submit_button("👥 新增住戶", use_container_width=True)
        
        if submitted:
            # 驗證輸入
            if not name:
                show_error_message("請輸入住戶姓名")
            elif not id_number:
                show_error_message("請輸入身份證號")
            elif not validate_id_number(id_number):
                show_error_message("身份證號格式不正確")
            elif phone and not validate_phone_number(phone):
                show_error_message("電話號碼格式不正確")
            elif emergency_phone and not validate_phone_number(emergency_phone):
                show_error_message("緊急聯絡人電話格式不正確")
            else:
                room_id = room_options[selected_room]
                
                resident_data = {
                    "name": name,
                    "id_number": id_number,
                    "phone": phone if phone else None,
                    "room_id": room_id,
                    "move_in_date": format_date_for_api(move_in_date),
                    "lease_end_date": format_date_for_api(lease_end_date) if lease_end_date else None,
                    "deposit": deposit,
                    "emergency_contact": emergency_contact if emergency_contact else None,
                    "emergency_phone": emergency_phone if emergency_phone else None
                }
                
                with st.spinner("新增中..."):
                    result = api_client.create_resident(resident_data)
                    
                    if result:
                        show_success_message(f"住戶 {name} 新增成功！")
                        st.rerun()

def show_resident_details(resident_data):
    """顯示住戶詳情"""
    st.subheader(f"住戶 {resident_data['name']} 詳情")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.metric("姓名", resident_data['name'])
        st.metric("身份證號", resident_data['id_number'])
        st.metric("聯絡電話", resident_data['phone'] or 'N/A')
        st.metric("押金", format_currency(resident_data['deposit']))
    
    with col2:
        room_info = resident_data.get('room', {})
        st.metric("房間號", room_info.get('room_number', 'N/A'))
        st.metric("入住日期", format_date_roc(resident_data['move_in_date'], "full"))
        st.metric("租約到期日", format_date_roc(resident_data['lease_end_date'], "full") if resident_data['lease_end_date'] else 'N/A')
        st.metric("退房日期", format_date_roc(resident_data['move_out_date'], "full") if resident_data['move_out_date'] else 'N/A')
        st.metric("狀態", "在住" if resident_data['is_active'] else "已退房")
    
    # 緊急聯絡人資訊
    if resident_data.get('emergency_contact'):
        st.subheader("緊急聯絡人")
        col1, col2 = st.columns(2)

        with col1:
            st.metric("緊急聯絡人", resident_data['emergency_contact'])

        with col2:
            st.metric("緊急聯絡電話", resident_data['emergency_phone'] or 'N/A')

    # 租期展期功能（僅對活躍住戶顯示）
    if resident_data.get('is_active'):
        st.markdown("---")
        st.subheader("🔄 租期展期")

        # 獲取租約資訊
        lease_info = api_client.get_resident_lease_info(resident_data['id'])

        if lease_info:
            # 顯示租約狀態
            col1, col2, col3 = st.columns(3)

            with col1:
                if lease_info.get('lease_end_date'):
                    st.metric("當前租約到期日", format_date_roc(lease_info['lease_end_date'], "full"))
                else:
                    st.metric("當前租約到期日", "未設定")

            with col2:
                if lease_info.get('days_until_expiry') is not None:
                    days = lease_info['days_until_expiry']
                    if days < 0:
                        st.metric("租約狀態", f"已過期 {abs(days)} 天", delta="過期")
                    elif days <= 30:
                        st.metric("租約狀態", f"即將到期 ({days} 天)", delta="注意")
                    else:
                        st.metric("租約狀態", f"正常 ({days} 天)", delta="正常")
                else:
                    st.metric("租約狀態", "未設定到期日")

            with col3:
                if st.button("📅 租期展期", key=f"extend_lease_{resident_data['id']}", use_container_width=True):
                    st.session_state.show_lease_extension_form = True
                    st.session_state.extension_resident = resident_data
                    st.rerun()

        # 顯示展期表單
        if st.session_state.get('show_lease_extension_form') and st.session_state.get('extension_resident', {}).get('id') == resident_data['id']:
            show_lease_extension_form(resident_data)

def show_move_out_form(resident_data):
    """顯示退房表單"""
    st.subheader(f"辦理退房 - {resident_data['name']}")
    
    with st.form("move_out_form"):
        move_out_date = roc_calendar_input("退房日期*", value=date.today(), help="請選擇住戶退房日期", key="move_out_date")
        
        show_warning_message("確認辦理退房後，住戶狀態將變更為已退房，且無法復原")

        col1, col2 = st.columns(2)

        with col1:
            submitted = st.form_submit_button("確認退房", use_container_width=True)

        with col2:
            if st.form_submit_button("❌ 取消", use_container_width=True):
                st.session_state.show_move_out_form = False
                st.session_state.move_out_resident = None
                st.rerun()
        
        if submitted:
            with st.spinner("處理中..."):
                result = api_client.move_out_resident(
                    resident_data['id'], 
                    format_date_for_api(move_out_date)
                )
                
                if result:
                    show_success_message(f"住戶 {resident_data['name']} 退房成功！")
                    # 清除可能的快取並重新載入頁面
                    if 'residents_cache' in st.session_state:
                        del st.session_state['residents_cache']
                    st.session_state.show_move_out_form = False
                    st.session_state.move_out_resident = None
                    st.rerun()

def show_residents_statistics():
    """顯示住戶統計"""
    st.subheader("住戶統計")
    
    # 獲取所有住戶（包括已退房）
    all_residents = api_client.get_residents(active_only=False)
    active_residents = api_client.get_residents(active_only=True)
    
    if not all_residents:
        show_info_message("暫無統計資料")
        return
    
    # 統計指標
    total_residents = len(all_residents)
    current_residents = len(active_residents)
    moved_out_residents = total_residents - current_residents
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("總住戶數", total_residents)
    
    with col2:
        st.metric("在住住戶", current_residents)
    
    with col3:
        st.metric("已退房住戶", moved_out_residents)
    
    # 圖表展示
    import plotly.express as px
    
    # 住戶狀態分布
    if total_residents > 0:
        fig = px.pie(
            values=[current_residents, moved_out_residents],
            names=['在住', '已退房'],
            title="住戶狀態分布"
        )
        st.plotly_chart(fig, use_container_width=True)
    
    # 房間住戶分布
    if active_residents:
        room_counts = {}
        for resident in active_residents:
            room_number = resident.get('room', {}).get('room_number', '未知')
            room_counts[room_number] = room_counts.get(room_number, 0) + 1
        
        if room_counts:
            fig = px.bar(
                x=list(room_counts.keys()),
                y=list(room_counts.values()),
                title="各房間住戶數量",
                labels={'x': '房間號', 'y': '住戶數量'}
            )
            st.plotly_chart(fig, use_container_width=True)

def show_edit_resident_form(resident_data):
    """顯示編輯住戶表單"""
    st.subheader(f"編輯住戶 - {resident_data['name']}")

    with st.form("edit_resident_form"):
        col1, col2 = st.columns(2)

        with col1:
            name = st.text_input("姓名*", value=resident_data['name'])
            phone = st.text_input("電話", value=resident_data.get('phone', ''))
            # 身份證號不可編輯，僅顯示
            st.text_input("身份證號", value=resident_data['id_number'], disabled=True, help="身份證號不可修改")

        with col2:
            emergency_contact = st.text_input("緊急聯絡人", value=resident_data.get('emergency_contact', ''))
            emergency_phone = st.text_input("緊急聯絡電話", value=resident_data.get('emergency_phone', ''))
            deposit = st.number_input("押金", value=resident_data.get('deposit', 0.0), min_value=0.0, step=100.0)

        col1, col2 = st.columns(2)

        with col1:
            submitted = st.form_submit_button("💾 更新住戶", use_container_width=True)

        with col2:
            if st.form_submit_button("❌ 取消", use_container_width=True):
                st.session_state.show_edit_resident_form = False
                st.session_state.editing_resident = None
                st.rerun()

        if submitted:
            if not name:
                show_error_message("請填寫姓名")
            elif phone and not validate_phone_number(phone):
                show_error_message("電話號碼格式不正確")
            elif emergency_phone and not validate_phone_number(emergency_phone):
                show_error_message("緊急聯絡電話格式不正確")
            else:
                update_data = {
                    "name": name,
                    "phone": phone if phone else None,
                    "emergency_contact": emergency_contact if emergency_contact else None,
                    "emergency_phone": emergency_phone if emergency_phone else None,
                    "deposit": deposit
                }

                with st.spinner("更新中..."):
                    result = api_client.update_resident(resident_data['id'], update_data)

                    if result:
                        show_success_message(f"住戶 {name} 更新成功！")
                        st.session_state.show_edit_resident_form = False
                        st.session_state.editing_resident = None
                        st.rerun()

def show_lease_extension_form(resident_data):
    """顯示租期展期表單"""
    st.markdown("---")
    st.subheader(f"📅 租期展期 - {resident_data['name']}")

    # 獲取當前租約資訊
    current_lease_end = resident_data.get('lease_end_date')

    with st.form("lease_extension_form"):
        st.write("**當前租約資訊：**")
        if current_lease_end:
            st.info(f"目前租約到期日：{format_date_roc(current_lease_end, 'full')}")
        else:
            st.warning("目前未設定租約到期日")

        # 快速展期選項
        st.write("**快速展期選項：**")
        col1, col2, col3, col4 = st.columns(4)

        quick_options = []
        base_date = datetime.strptime(current_lease_end, '%Y-%m-%d').date() if current_lease_end else date.today()

        with col1:
            if st.form_submit_button("+ 3個月", use_container_width=True):
                quick_options.append(3)

        with col2:
            if st.form_submit_button("+ 6個月", use_container_width=True):
                quick_options.append(6)

        with col3:
            if st.form_submit_button("+ 1年", use_container_width=True):
                quick_options.append(12)

        with col4:
            if st.form_submit_button("+ 2年", use_container_width=True):
                quick_options.append(24)

        # 處理快速選項
        if quick_options:
            months_to_add = quick_options[0]
            if base_date.month + months_to_add <= 12:
                new_date = base_date.replace(month=base_date.month + months_to_add)
            else:
                years_to_add = (base_date.month + months_to_add - 1) // 12
                new_month = (base_date.month + months_to_add - 1) % 12 + 1
                new_date = base_date.replace(year=base_date.year + years_to_add, month=new_month)

            st.session_state.quick_extension_date = new_date

        # 自定義日期選擇
        st.write("**或選擇自定義日期：**")

        # 使用快速選項的日期作為預設值，否則使用當前日期
        default_date = st.session_state.get('quick_extension_date',
                                          base_date + timedelta(days=365) if base_date else date.today() + timedelta(days=365))

        new_lease_end_date = roc_calendar_input(
            "新租約到期日*",
            value=default_date,
            help="請選擇新的租約到期日期",
            key=f"extension_date_{resident_data['id']}"
        )

        # 展期原因
        extension_reason = st.text_area(
            "展期原因",
            placeholder="請輸入展期原因（選填）",
            help="記錄展期的原因，便於日後查詢"
        )

        # 顯示展期詳情
        if new_lease_end_date and current_lease_end:
            original_date = datetime.strptime(current_lease_end, '%Y-%m-%d').date()
            if new_lease_end_date > original_date:
                extension_days = (new_lease_end_date - original_date).days
                st.success(f"✅ 展期 {extension_days} 天")
                st.write(f"**展期前：** {format_date_roc(current_lease_end, 'full')}")
                st.write(f"**展期後：** {format_date_roc(new_lease_end_date.isoformat(), 'full')}")
            else:
                st.error("❌ 新租約到期日必須晚於原到期日")

        # 表單按鈕
        col1, col2 = st.columns(2)

        with col1:
            submitted = st.form_submit_button("✅ 確認展期", use_container_width=True)

        with col2:
            if st.form_submit_button("❌ 取消", use_container_width=True):
                st.session_state.show_lease_extension_form = False
                st.session_state.extension_resident = None
                if 'quick_extension_date' in st.session_state:
                    del st.session_state.quick_extension_date
                st.rerun()

        # 處理表單提交
        if submitted:
            if not new_lease_end_date:
                show_error_message("請選擇新的租約到期日")
            elif current_lease_end and new_lease_end_date <= datetime.strptime(current_lease_end, '%Y-%m-%d').date():
                show_error_message("新租約到期日必須晚於原到期日")
            elif new_lease_end_date <= date.today():
                show_error_message("新租約到期日必須晚於今天")
            else:
                # 顯示確認對話框
                st.session_state.confirm_extension = {
                    'resident_id': resident_data['id'],
                    'resident_name': resident_data['name'],
                    'original_date': current_lease_end,
                    'new_date': new_lease_end_date.isoformat(),
                    'extension_reason': extension_reason,
                    'extension_days': (new_lease_end_date - datetime.strptime(current_lease_end, '%Y-%m-%d').date()).days if current_lease_end else None
                }
                st.rerun()

def show_lease_extension_confirmation():
    """顯示租期展期確認對話框"""
    if not st.session_state.get('confirm_extension'):
        return

    extension_info = st.session_state.confirm_extension

    st.markdown("---")
    st.warning("⚠️ 確認租期展期")

    st.write(f"**住戶：** {extension_info['resident_name']}")
    if extension_info['original_date']:
        st.write(f"**原租約到期日：** {format_date_roc(extension_info['original_date'], 'full')}")
    st.write(f"**新租約到期日：** {format_date_roc(extension_info['new_date'], 'full')}")
    if extension_info['extension_days']:
        st.write(f"**展期天數：** {extension_info['extension_days']} 天")
    if extension_info['extension_reason']:
        st.write(f"**展期原因：** {extension_info['extension_reason']}")

    st.write("**此操作將更新住戶的租約到期日，請確認資訊無誤。**")

    col1, col2 = st.columns(2)

    with col1:
        if st.button("✅ 確認展期", key="confirm_extension_yes", use_container_width=True):
            with st.spinner("處理中..."):
                result = api_client.extend_resident_lease(
                    extension_info['resident_id'],
                    extension_info['new_date'],
                    extension_info['extension_reason']
                )

                if result:
                    show_success_message(f"住戶 {extension_info['resident_name']} 租期展期成功！")
                    # 清除狀態
                    st.session_state.confirm_extension = None
                    st.session_state.show_lease_extension_form = False
                    st.session_state.extension_resident = None
                    if 'quick_extension_date' in st.session_state:
                        del st.session_state.quick_extension_date
                    st.rerun()

    with col2:
        if st.button("❌ 取消", key="confirm_extension_no", use_container_width=True):
            st.session_state.confirm_extension = None
            st.rerun()
