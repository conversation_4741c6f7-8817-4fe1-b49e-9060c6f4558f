#!/usr/bin/env python3
"""測試房間刪除功能"""

import requests

def test_room_deletion():
    """測試房間軟刪除功能"""
    base_url = 'http://localhost:8080'
    login_data = {
        'username': 'admin',
        'password': 'admin5813'
    }

    try:
        # 1. 登入獲取 token
        print('🔐 登入系統...')
        response = requests.post(f'{base_url}/auth/login', data=login_data)
        
        if response.status_code != 200:
            print(f'❌ 登入失敗: {response.status_code}')
            return
            
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        print('✅ 登入成功')
        
        # 2. 獲取房間列表
        print('\n🏠 獲取房間列表...')
        rooms_response = requests.get(f'{base_url}/rooms/', headers=headers)
        
        if rooms_response.status_code != 200:
            print(f'❌ 獲取房間列表失敗: {rooms_response.status_code}')
            return
            
        rooms = rooms_response.json()
        print(f'✅ 找到 {len(rooms)} 個房間')
        
        # 3. 找一個沒有住戶的房間來測試刪除
        empty_room = None
        occupied_room = None
        
        for room in rooms:
            current_occupants = room.get('current_occupants', 0)
            if current_occupants == 0 and not empty_room:
                empty_room = room
            elif current_occupants > 0 and not occupied_room:
                occupied_room = room
                
        # 4. 測試刪除有住戶的房間（應該失敗）
        if occupied_room:
            print(f'\n🚫 測試刪除有住戶的房間: {occupied_room["room_number"]} (住戶數: {occupied_room.get("current_occupants", 0)})')
            delete_response = requests.delete(f'{base_url}/rooms/{occupied_room["id"]}', headers=headers)
            print(f'刪除響應狀態碼: {delete_response.status_code}')
            
            if delete_response.status_code == 400:
                print('✅ 正確阻止刪除有住戶的房間')
                print(f'錯誤訊息: {delete_response.json().get("detail", "未知錯誤")}')
            else:
                print(f'⚠️  預期狀態碼 400，實際得到 {delete_response.status_code}')
                print(f'響應內容: {delete_response.text}')
        
        # 5. 測試刪除空房間（應該成功）
        if empty_room:
            print(f'\n✅ 測試刪除空房間: {empty_room["room_number"]} (住戶數: {empty_room.get("current_occupants", 0)})')
            delete_response = requests.delete(f'{base_url}/rooms/{empty_room["id"]}', headers=headers)
            print(f'刪除響應狀態碼: {delete_response.status_code}')
            
            if delete_response.status_code == 200:
                print('✅ 成功刪除空房間')
                result = delete_response.json()
                print(f'成功訊息: {result.get("message", "房間已刪除")}')
                
                # 驗證房間是否被軟刪除
                print('\n🔍 驗證房間是否被軟刪除...')
                rooms_after_response = requests.get(f'{base_url}/rooms/', headers=headers)
                if rooms_after_response.status_code == 200:
                    rooms_after = rooms_after_response.json()
                    deleted_room_still_exists = any(r['id'] == empty_room['id'] for r in rooms_after)
                    
                    if not deleted_room_still_exists:
                        print('✅ 房間已從活躍列表中移除（軟刪除成功）')
                    else:
                        print('⚠️  房間仍在活躍列表中')
                        
            else:
                print(f'❌ 刪除空房間失敗: {delete_response.status_code}')
                print(f'錯誤詳情: {delete_response.text}')
        
        if not empty_room and not occupied_room:
            print('\n⚠️  沒有找到合適的房間進行測試')
        elif not empty_room:
            print('\n⚠️  沒有找到空房間進行刪除測試')
        elif not occupied_room:
            print('\n⚠️  沒有找到有住戶的房間進行阻止刪除測試')
            
    except Exception as e:
        print(f'❌ 測試過程中發生錯誤: {e}')

if __name__ == "__main__":
    print("🧪 測試房間刪除功能")
    print("=" * 50)
    test_room_deletion()
    print("\n" + "=" * 50)
    print("🏁 測試完成")
