#!/usr/bin/env python3
"""測試水費計算邏輯修正"""

import requests
import sqlite3
import sys
import os
from datetime import date, datetime, timed<PERSON><PERSON>

def get_test_rooms():
    """獲取測試房間（1人房和2人房）"""
    print("🏠 獲取測試房間...")
    
    base_url = 'http://localhost:8080'
    login_data = {
        'username': 'admin',
        'password': 'admin5813'
    }
    
    try:
        # 登入
        response = requests.post(f'{base_url}/auth/login', data=login_data)
        if response.status_code != 200:
            print(f'❌ 登入失敗: {response.status_code}')
            return None
            
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        # 獲取房間列表
        response = requests.get(f'{base_url}/rooms/', headers=headers)
        if response.status_code != 200:
            print("❌ 無法獲取房間列表")
            return None
            
        rooms = response.json()
        if not rooms:
            print("❌ 沒有房間")
            return None
        
        # 找到1人房和2人房
        one_person_room = None
        two_person_room = None
        
        for room in rooms:
            if room.get('current_occupants') == 1 and not one_person_room:
                one_person_room = room
            elif room.get('current_occupants') == 2 and not two_person_room:
                two_person_room = room
        
        print(f"✅ 找到測試房間")
        if one_person_room:
            print(f"   1人房: {one_person_room['room_number']} (住戶數: {one_person_room['current_occupants']})")
        if two_person_room:
            print(f"   2人房: {two_person_room['room_number']} (住戶數: {two_person_room['current_occupants']})")
        
        return token, one_person_room, two_person_room
            
    except Exception as e:
        print(f"❌ 獲取測試房間錯誤: {e}")
        return None

def test_water_fee_calculation(token, room, room_type):
    """測試水費計算"""
    print(f"\n💧 測試{room_type}水費計算...")
    
    if not room:
        print(f"   ⚠️  沒有可用的{room_type}")
        return None
    
    base_url = 'http://localhost:8080'
    headers = {'Authorization': f'Bearer {token}'}
    
    try:
        # 獲取當前年月
        now = datetime.now()
        year = now.year
        month = now.month
        
        # 模擬電表讀數
        reading_data = {
            "room_id": room['id'],
            "billing_year": year,
            "billing_month": month,
            "current_electricity_reading": 1000.0  # 假設讀數
        }
        
        # 創建電表抄錄記錄
        response = requests.post(f'{base_url}/utilities/readings', 
                               json=reading_data, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            water_fee = result.get('water_fee', 0)
            
            print(f"   房間: {room['room_number']}")
            print(f"   住戶數: {room['current_occupants']}")
            print(f"   水費: ${water_fee:.2f}")
            print(f"   電費: ${result.get('electricity_cost', 0):.2f}")
            print(f"   總費用: ${result.get('total_amount', 0):.2f}")
            
            # 驗證水費是否為100元
            if abs(water_fee - 100.0) < 0.01:
                print(f"   ✅ 水費正確：固定100元")
                return True
            else:
                print(f"   ❌ 水費錯誤：應為100元，實際為{water_fee:.2f}元")
                return False
        else:
            print(f"   ❌ 電表抄錄失敗: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ {room_type}水費計算測試錯誤: {e}")
        return False

def test_current_rate_display():
    """測試當前費率顯示"""
    print(f"\n💰 測試當前費率顯示...")
    
    base_url = 'http://localhost:8080'
    login_data = {
        'username': 'admin',
        'password': 'admin5813'
    }
    
    try:
        # 登入
        response = requests.post(f'{base_url}/auth/login', data=login_data)
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        # 獲取當前費率
        response = requests.get(f'{base_url}/utilities/rates/current', headers=headers)
        
        if response.status_code == 200:
            rate = response.json()
            monthly_water_fee = rate.get('monthly_water_fee', 0)
            
            print(f"   當前電費費率: ${rate.get('electricity_rate', 0):.2f}/度")
            print(f"   當前水費: ${monthly_water_fee:.2f}/月")
            
            # 檢查水費是否為100元
            if abs(monthly_water_fee - 100.0) < 0.01:
                print(f"   ✅ 費率設定正確：水費100元/月")
                return True
            else:
                print(f"   ⚠️  費率設定：水費{monthly_water_fee:.2f}元/月（建議設為100元）")
                return True  # 不算錯誤，只是建議
        else:
            print(f"   ❌ 獲取當前費率失敗: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 當前費率顯示測試錯誤: {e}")
        return False

def test_create_new_rate():
    """測試創建新費率"""
    print(f"\n⚙️ 測試創建新費率...")
    
    base_url = 'http://localhost:8080'
    login_data = {
        'username': 'admin',
        'password': 'admin5813'
    }
    
    try:
        # 登入
        response = requests.post(f'{base_url}/auth/login', data=login_data)
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        # 創建新費率（水費100元）
        rate_data = {
            "electricity_rate": 5.5,
            "monthly_water_fee": 100.0,
            "effective_date": date.today().isoformat(),
            "notes": "測試水費固定100元"
        }
        
        response = requests.post(f'{base_url}/utilities/rates', 
                               json=rate_data, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 新費率創建成功")
            print(f"      電費: ${rate_data['electricity_rate']:.2f}/度")
            print(f"      水費: ${rate_data['monthly_water_fee']:.2f}/月")
            return True
        else:
            print(f"   ❌ 新費率創建失敗: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 創建新費率測試錯誤: {e}")
        return False

def test_frontend_code_changes():
    """測試前端代碼修改"""
    print(f"\n🖥️ 測試前端代碼修改...")
    
    try:
        # 檢查前端文件是否正確修改
        utilities_file = 'frontend/pages/utilities.py'
        
        if os.path.exists(utilities_file):
            with open(utilities_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"   檢查 {utilities_file}:")
            
            # 檢查是否包含正確的說明文字
            checks = [
                ('不論房間住戶人數，統一收費100元', '費率設定說明'),
                ('$100/月（固定費用）', '費率顯示'),
                ('value=100.0', '預設水費值')
            ]
            
            for check_text, description in checks:
                if check_text in content:
                    print(f"      ✅ {description}: 已更新")
                else:
                    print(f"      ❌ {description}: 未找到")
                    return False
            
            return True
        else:
            print(f"   ❌ {utilities_file} 不存在")
            return False
            
    except Exception as e:
        print(f"❌ 前端代碼修改測試錯誤: {e}")
        return False

def test_backend_code_changes():
    """測試後端代碼修改"""
    print(f"\n🔧 測試後端代碼修改...")
    
    try:
        # 檢查後端文件是否正確修改
        services_file = 'backend/app/services.py'
        
        if os.path.exists(services_file):
            with open(services_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"   檢查 {services_file}:")
            
            # 檢查是否包含固定水費邏輯
            if 'water_fee = 100.0  # 固定水費100元' in content:
                print(f"      ✅ 水費計算邏輯: 已修正為固定100元")
            else:
                print(f"      ❌ 水費計算邏輯: 未找到固定100元邏輯")
                return False
            
            # 檢查是否移除了住戶數量分攤邏輯
            if 'rate.monthly_water_fee / room.current_occupants' not in content:
                print(f"      ✅ 住戶數量分攤邏輯: 已移除")
            else:
                print(f"      ❌ 住戶數量分攤邏輯: 仍存在")
                return False
            
            return True
        else:
            print(f"   ❌ {services_file} 不存在")
            return False
            
    except Exception as e:
        print(f"❌ 後端代碼修改測試錯誤: {e}")
        return False

if __name__ == "__main__":
    print("🧪 水費計算邏輯修正測試")
    print("=" * 60)
    
    # 1. 獲取測試房間
    rooms_result = get_test_rooms()
    
    if rooms_result:
        token, one_person_room, two_person_room = rooms_result
        
        # 2. 測試1人房水費計算
        one_person_test = test_water_fee_calculation(token, one_person_room, "1人房")
        
        # 3. 測試2人房水費計算
        two_person_test = test_water_fee_calculation(token, two_person_room, "2人房")
        
        # 4. 測試當前費率顯示
        rate_display_test = test_current_rate_display()
        
        # 5. 測試創建新費率
        create_rate_test = test_create_new_rate()
        
    else:
        one_person_test = False
        two_person_test = False
        rate_display_test = False
        create_rate_test = False
    
    # 6. 測試前端代碼修改
    frontend_test = test_frontend_code_changes()
    
    # 7. 測試後端代碼修改
    backend_test = test_backend_code_changes()
    
    print("\n" + "=" * 60)
    print("📝 測試結果總結:")
    print("1. ✅ 1人房水費計算" if one_person_test else "1. ❌ 1人房水費計算")
    print("2. ✅ 2人房水費計算" if two_person_test else "2. ❌ 2人房水費計算")
    print("3. ✅ 當前費率顯示" if rate_display_test else "3. ❌ 當前費率顯示")
    print("4. ✅ 創建新費率" if create_rate_test else "4. ❌ 創建新費率")
    print("5. ✅ 前端代碼修改" if frontend_test else "5. ❌ 前端代碼修改")
    print("6. ✅ 後端代碼修改" if backend_test else "6. ❌ 後端代碼修改")
    
    # 計算水費相關測試結果
    water_fee_tests = [one_person_test, two_person_test] if one_person_test is not None and two_person_test is not None else []
    water_fee_passed = all(water_fee_tests) if water_fee_tests else False
    
    all_passed = all([
        water_fee_passed or (one_person_test is None and two_person_test is None),  # 如果沒有合適房間則跳過
        rate_display_test, 
        create_rate_test, 
        frontend_test, 
        backend_test
    ])
    
    print("\n💡 修正內容:")
    print("🔧 後端修正:")
    print("   - 修改 UtilityService.calculate_monthly_bill() 方法")
    print("   - 水費計算改為固定100元，不再根據住戶數量分攤")
    print("   - 移除 rate.monthly_water_fee / room.current_occupants 邏輯")
    print("   - 更新預設費率中的水費為100元")
    
    print("\n🖥️ 前端修正:")
    print("   - 更新費率設定界面說明文字")
    print("   - 預設水費值改為100元")
    print("   - 費率顯示改為固定費用說明")
    print("   - 移除基於住戶數量的費用計算提示")
    
    print("\n🎯 修正效果:")
    print("   - 1人房和2人房水費均為100元")
    print("   - 費率設定頁面正確顯示水費為固定費用")
    print("   - 帳單生成時水費始終為100元")
    print("   - 費率說明文字明確標示水費為固定月費")
    
    print(f"\n🏁 測試完成 - {'全部通過' if all_passed else '部分失敗'}")
    
    if all_passed:
        print("\n🎉 水費計算邏輯修正已成功實作並測試通過！")
        print("   現在水費為固定100元，不論房間住戶人數。")
