from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.docs import get_swagger_ui_html, get_redoc_html
from fastapi.responses import HTMLResponse
from .database import create_tables
from .routers import auth, rooms, residents, utilities, reports, rent, comprehensive_bills
from .config import settings
from .auth import get_current_user
from .models import User

# 創建FastAPI應用程式（禁用預設文檔）
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="一個簡潔易維護的租房管理系統",
    docs_url=None,  # 禁用預設的 /docs
    redoc_url=None  # 禁用預設的 /redoc
)

# CORS設定
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:8501", "http://127.0.0.1:8501"],  # Streamlit前端
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 註冊路由
app.include_router(auth.router)
app.include_router(rooms.router)
app.include_router(residents.router)
app.include_router(utilities.router)
app.include_router(rent.router)
app.include_router(reports.router)
app.include_router(comprehensive_bills.router)

@app.on_event("startup")
async def startup_event():
    """應用程式啟動時執行"""
    # 創建資料庫表格
    create_tables()

@app.get("/")
async def root():
    """根路徑"""
    return {
        "message": f"歡迎使用{settings.app_name}",
        "version": settings.app_version,
        "docs": "/docs",
        "redoc": "/redoc"
    }

@app.get("/health")
async def health_check():
    """健康檢查"""
    return {"status": "healthy", "timestamp": "2024-01-01T00:00:00Z"}

# 需要管理員權限的API文檔
def require_admin(current_user: User = Depends(get_current_user)):
    """檢查管理員權限"""
    if current_user.role != 'admin':
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理員權限才能訪問API文檔"
        )
    return current_user

@app.get("/docs", response_class=HTMLResponse, include_in_schema=False)
async def get_documentation(admin_user: User = Depends(require_admin)):
    """獲取API文檔（僅限管理員）"""
    return get_swagger_ui_html(
        openapi_url="/openapi.json",
        title=f"{settings.app_name} - API文檔",
        swagger_js_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js",
        swagger_css_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css",
    )

@app.get("/redoc", response_class=HTMLResponse, include_in_schema=False)
async def get_redoc_documentation(admin_user: User = Depends(require_admin)):
    """獲取ReDoc文檔（僅限管理員）"""
    return get_redoc_html(
        openapi_url="/openapi.json",
        title=f"{settings.app_name} - API文檔",
        redoc_js_url="https://cdn.jsdelivr.net/npm/redoc@2.0.0/bundles/redoc.standalone.js",
    )

@app.get("/openapi.json", include_in_schema=False)
async def get_openapi(admin_user: User = Depends(require_admin)):
    """獲取OpenAPI規範（僅限管理員）"""
    return app.openapi()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=settings.port,
        reload=settings.debug
    )
