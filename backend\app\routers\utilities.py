from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel, validator
from typing import List, Optional, Union
from datetime import datetime, date
from ..database import get_db
from ..auth import get_current_user
from ..services import UtilityService
from ..models import User, UtilityRate, UtilityRecord

router = APIRouter(prefix="/utilities", tags=["費用管理"])

class UtilityRateCreate(BaseModel):
    electricity_rate: float
    monthly_water_fee: float
    effective_date: Union[date, str]
    notes: Optional[str] = None

    @validator('effective_date', pre=True)
    def parse_effective_date(cls, v):
        """解析日期格式，支援多種輸入格式"""
        if isinstance(v, str):
            try:
                # 嘗試解析 YYYY-MM-DD 格式
                return datetime.strptime(v, '%Y-%m-%d').date()
            except ValueError:
                try:
                    # 嘗試解析 ISO 格式
                    return datetime.fromisoformat(v.replace('Z', '+00:00')).date()
                except ValueError:
                    raise ValueError('日期格式錯誤，請使用 YYYY-MM-DD 格式')
        elif isinstance(v, datetime):
            return v.date()
        elif isinstance(v, date):
            return v
        else:
            raise ValueError('日期格式錯誤，請使用 YYYY-MM-DD 格式')

    @validator('electricity_rate')
    def validate_electricity_rate(cls, v):
        if v <= 0:
            raise ValueError('電費費率必須大於0')
        return v

    @validator('monthly_water_fee')
    def validate_water_fee(cls, v):
        if v <= 0:
            raise ValueError('水費必須大於0')
        return v

class MeterReadingCreate(BaseModel):
    room_id: int
    billing_year: int
    billing_month: int
    current_electricity_reading: float

class UtilityRateUpdate(BaseModel):
    electricity_rate: Optional[float] = None
    monthly_water_fee: Optional[float] = None
    notes: Optional[str] = None
    is_active: Optional[bool] = None

class PaymentUpdate(BaseModel):
    payment_status: str
    payment_date: Optional[datetime] = None

class UtilityRateResponse(BaseModel):
    id: int
    electricity_rate: float
    monthly_water_fee: float
    effective_date: str
    notes: Optional[str]
    is_active: bool

class UtilityRecordResponse(BaseModel):
    id: int
    room_id: int
    billing_year: int
    billing_month: int
    previous_electricity_reading: float
    current_electricity_reading: float
    electricity_usage: float
    electricity_rate: float
    electricity_cost: float
    water_fee: float
    total_amount: float
    payment_status: str
    payment_date: Optional[str]
    room: Optional[dict]

@router.get("/rates", response_model=List[UtilityRateResponse])
async def get_utility_rates(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """獲取所有費率"""
    rates = UtilityService.get_all_utility_rates(db)
    return [rate.to_dict() for rate in rates]

@router.get("/rates/current", response_model=UtilityRateResponse)
async def get_current_utility_rate(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """獲取當前費率"""
    rate = UtilityService.get_current_utility_rate(db)
    if not rate:
        raise HTTPException(status_code=404, detail="費率未設定")
    return rate.to_dict()

@router.post("/rates", response_model=UtilityRateResponse)
async def create_utility_rate(
    rate: UtilityRateCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """創建新費率"""
    try:
        rate_data = rate.dict()

        # 確保 effective_date 轉換為 datetime 格式
        if isinstance(rate_data['effective_date'], date):
            rate_data['effective_date'] = datetime.combine(rate_data['effective_date'], datetime.min.time())

        # 暫時禁用重複日期檢查，確保基本功能正常
        # existing_rate = UtilityService.get_rate_by_date(db, rate_data['effective_date'])
        # if existing_rate:
        #     raise HTTPException(
        #         status_code=status.HTTP_400_BAD_REQUEST,
        #         detail="該日期已有費率設定"
        #     )

        new_rate = UtilityService.create_utility_rate(db, rate_data)
        return new_rate.to_dict()
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"創建費率失敗: {str(e)}"
        )

@router.get("/rates/{rate_id}", response_model=UtilityRateResponse)
async def get_utility_rate(
    rate_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """獲取特定費率"""
    rate = UtilityService.get_utility_rate_by_id(db, rate_id)
    if not rate:
        raise HTTPException(status_code=404, detail="費率不存在")
    return rate.to_dict()

@router.put("/rates/{rate_id}", response_model=UtilityRateResponse)
async def update_utility_rate(
    rate_id: int,
    rate_update: UtilityRateUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新費率"""
    try:
        rate = UtilityService.get_utility_rate_by_id(db, rate_id)
        if not rate:
            raise HTTPException(status_code=404, detail="費率不存在")

        # 驗證更新資料
        update_data = rate_update.dict(exclude_unset=True)

        if 'electricity_rate' in update_data and update_data['electricity_rate'] <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="電費費率必須大於0"
            )

        if 'monthly_water_fee' in update_data and update_data['monthly_water_fee'] <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="水費必須大於0"
            )

        # 更新費率資料
        for field, value in update_data.items():
            setattr(rate, field, value)

        db.commit()
        db.refresh(rate)
        return rate.to_dict()

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新費率失敗: {str(e)}"
        )

@router.delete("/rates/clear-all")
async def clear_all_utility_rates(
    keep_current: bool = True,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """清除所有費率記錄"""
    try:
        from app.models import UtilityRate
        from datetime import datetime

        if keep_current:
            # 保留當前活躍費率，刪除其他所有費率
            current_rate = UtilityService.get_current_utility_rate(db)
            if current_rate:
                deleted_count = db.query(UtilityRate).filter(UtilityRate.id != current_rate.id).count()
                db.query(UtilityRate).filter(UtilityRate.id != current_rate.id).delete()
            else:
                # 如果沒有當前費率，創建一個預設費率
                deleted_count = db.query(UtilityRate).count()
                db.query(UtilityRate).delete()

                default_rate = UtilityRate(
                    electricity_rate=5.5,
                    monthly_water_fee=100.0,
                    effective_date=datetime.utcnow()
                )
                db.add(default_rate)
        else:
            # 刪除所有費率並創建預設費率
            deleted_count = db.query(UtilityRate).count()
            db.query(UtilityRate).delete()

            # 創建預設費率
            default_rate = UtilityRate(
                electricity_rate=5.5,
                monthly_water_fee=100.0,
                effective_date=datetime.utcnow()
            )
            db.add(default_rate)

        db.commit()
        return {
            "message": f"成功清除 {deleted_count} 筆費率記錄",
            "deleted_count": deleted_count
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=f"清除費率記錄失敗: {str(e)}")

@router.delete("/rates/{rate_id}")
async def delete_utility_rate(
    rate_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """刪除費率"""
    rate = UtilityService.get_utility_rate_by_id(db, rate_id)
    if not rate:
        raise HTTPException(status_code=404, detail="費率不存在")

    # 檢查是否為當前使用的費率
    if rate.is_active:
        raise HTTPException(status_code=400, detail="無法刪除當前使用的費率")

    db.delete(rate)
    db.commit()

    return {"message": "費率已刪除"}

@router.delete("/bills/clear-all")
async def clear_all_utility_bills(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """清除所有帳單記錄"""
    try:
        from app.models import UtilityRecord

        deleted_count = db.query(UtilityRecord).count()
        db.query(UtilityRecord).delete()
        db.commit()

        return {
            "message": f"成功清除 {deleted_count} 筆帳單記錄",
            "deleted_count": deleted_count
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=f"清除帳單記錄失敗: {str(e)}")

@router.delete("/bills/{bill_id}")
async def delete_utility_bill(
    bill_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """刪除個別帳單"""
    try:
        from app.models import UtilityRecord

        bill = db.query(UtilityRecord).filter(UtilityRecord.id == bill_id).first()
        if not bill:
            raise HTTPException(status_code=404, detail="帳單不存在")

        db.delete(bill)
        db.commit()
        return {"message": "帳單刪除成功"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=f"刪除帳單失敗: {str(e)}")

@router.post("/readings", response_model=UtilityRecordResponse)
async def create_meter_reading(
    reading: MeterReadingCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """創建電表抄錄記錄"""
    try:
        record = UtilityService.calculate_monthly_bill(
            db,
            reading.room_id,
            reading.billing_year,
            reading.billing_month,
            reading.current_electricity_reading
        )
        return record.to_dict()
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/bills", response_model=List[UtilityRecordResponse])
async def get_utility_bills(
    year: int,
    month: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """獲取月度帳單"""
    bills = UtilityService.get_monthly_bills(db, year, month)
    return [bill.to_dict() for bill in bills]

@router.put("/bills/{bill_id}/payment", response_model=UtilityRecordResponse)
async def update_payment_status(
    bill_id: int,
    payment_update: PaymentUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新付款狀態"""
    bill = UtilityService.update_payment_status(
        db,
        bill_id,
        payment_update.payment_status,
        payment_update.payment_date
    )
    if not bill:
        raise HTTPException(status_code=404, detail="帳單不存在")
    return bill.to_dict()
