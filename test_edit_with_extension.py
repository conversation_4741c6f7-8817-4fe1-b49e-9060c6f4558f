#!/usr/bin/env python3
"""測試編輯住戶時的租期展期功能"""

import requests
import sys
import os
from datetime import date, datetime, timedelta

def get_test_resident():
    """獲取測試住戶"""
    print("🏠 獲取測試住戶...")
    
    base_url = 'http://localhost:8080'
    login_data = {
        'username': 'admin',
        'password': 'admin5813'
    }
    
    try:
        # 登入
        response = requests.post(f'{base_url}/auth/login', data=login_data)
        if response.status_code != 200:
            print(f'❌ 登入失敗: {response.status_code}')
            return None
            
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        # 獲取活躍住戶
        response = requests.get(f'{base_url}/residents?active_only=true', headers=headers)
        if response.status_code != 200:
            print("❌ 無法獲取住戶列表")
            return None
            
        residents = response.json()
        if not residents:
            print("❌ 沒有活躍住戶")
            return None
        
        # 使用第一個住戶
        test_resident = residents[0]
        print(f"✅ 使用住戶進行測試")
        print(f"   姓名: {test_resident['name']}")
        print(f"   ID: {test_resident['id']}")
        print(f"   當前租約到期日: {test_resident.get('lease_end_date', 'N/A')}")
        
        return test_resident['id'], token, test_resident
            
    except Exception as e:
        print(f"❌ 獲取測試住戶錯誤: {e}")
        return None

def test_basic_update_only(resident_id, token, original_resident):
    """測試僅更新基本資訊（不展期）"""
    print(f"\n📝 測試基本資訊更新 (住戶 ID: {resident_id})...")
    
    base_url = 'http://localhost:8080'
    headers = {'Authorization': f'Bearer {token}'}
    
    try:
        # 記錄原始資訊
        original_name = original_resident['name']
        original_phone = original_resident.get('phone', '')
        original_lease_end = original_resident.get('lease_end_date')
        
        # 更新基本資訊
        update_data = {
            "name": f"{original_name}_測試更新",
            "phone": "0912345678",
            "emergency_contact": "測試緊急聯絡人",
            "emergency_phone": "0987654321",
            "deposit": 15000.0
        }
        
        response = requests.put(f'{base_url}/residents/{resident_id}', 
                              json=update_data, headers=headers)
        
        if response.status_code == 200:
            updated_resident = response.json()
            print(f"   ✅ 基本資訊更新成功")
            print(f"      姓名: {original_name} → {updated_resident['name']}")
            print(f"      電話: {original_phone} → {updated_resident.get('phone', 'N/A')}")
            print(f"      租約到期日: {original_lease_end} → {updated_resident.get('lease_end_date', 'N/A')}")
            
            # 驗證租約到期日沒有改變
            if updated_resident.get('lease_end_date') == original_lease_end:
                print(f"   ✅ 租約到期日保持不變（符合預期）")
            else:
                print(f"   ❌ 租約到期日意外改變")
                return False
            
            # 恢復原始資訊
            restore_data = {
                "name": original_name,
                "phone": original_phone,
                "emergency_contact": original_resident.get('emergency_contact'),
                "emergency_phone": original_resident.get('emergency_phone'),
                "deposit": original_resident.get('deposit', 0.0)
            }
            
            requests.put(f'{base_url}/residents/{resident_id}', 
                        json=restore_data, headers=headers)
            
            return True
        else:
            print(f"   ❌ 基本資訊更新失敗: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 基本資訊更新測試錯誤: {e}")
        return False

def test_update_with_extension(resident_id, token, original_resident):
    """測試更新資訊同時展期"""
    print(f"\n🔄 測試更新資訊同時展期 (住戶 ID: {resident_id})...")
    
    base_url = 'http://localhost:8080'
    headers = {'Authorization': f'Bearer {token}'}
    
    try:
        # 記錄原始資訊
        original_name = original_resident['name']
        original_phone = original_resident.get('phone', '')
        original_lease_end = original_resident.get('lease_end_date')
        
        print(f"   原始租約到期日: {original_lease_end}")
        
        # 1. 先更新基本資訊
        update_data = {
            "name": f"{original_name}_展期測試",
            "phone": "0911111111",
            "emergency_contact": "展期測試聯絡人",
            "emergency_phone": "0922222222",
            "deposit": 20000.0
        }
        
        response = requests.put(f'{base_url}/residents/{resident_id}', 
                              json=update_data, headers=headers)
        
        if response.status_code != 200:
            print(f"   ❌ 基本資訊更新失敗: {response.status_code}")
            return False
        
        print(f"   ✅ 基本資訊更新成功")
        
        # 2. 執行租期展期
        if original_lease_end:
            if 'T' in original_lease_end:
                # 處理 ISO 格式 (YYYY-MM-DDTHH:MM:SS)
                base_date = datetime.fromisoformat(original_lease_end.replace('T', ' ')).date()
            else:
                # 處理簡單日期格式 (YYYY-MM-DD)
                base_date = datetime.strptime(original_lease_end, '%Y-%m-%d').date()
        else:
            base_date = date.today()
        
        new_lease_end_date = (base_date + timedelta(days=180)).isoformat()
        extension_data = {
            "new_lease_end_date": new_lease_end_date,
            "extension_reason": "編輯住戶時同時展期測試"
        }
        
        response = requests.post(f'{base_url}/residents/{resident_id}/extend-lease', 
                               json=extension_data, headers=headers)
        
        if response.status_code == 200:
            extension_result = response.json()
            extension_details = extension_result.get('extension_details', {})
            
            print(f"   ✅ 租期展期成功")
            print(f"      原到期日: {extension_details.get('original_lease_end_date', 'N/A')}")
            print(f"      新到期日: {extension_details.get('new_lease_end_date')}")
            print(f"      展期天數: {extension_details.get('extended_by_days', 'N/A')}")
            
            # 3. 驗證最終結果
            final_response = requests.get(f'{base_url}/residents/{resident_id}', headers=headers)
            if final_response.status_code == 200:
                final_resident = final_response.json()
                
                print(f"   📊 最終驗證:")
                print(f"      姓名: {original_name} → {final_resident['name']}")
                print(f"      電話: {original_phone} → {final_resident.get('phone', 'N/A')}")
                print(f"      租約到期日: {original_lease_end} → {final_resident.get('lease_end_date')}")
                
                # 恢復原始資訊
                restore_data = {
                    "name": original_name,
                    "phone": original_phone,
                    "emergency_contact": original_resident.get('emergency_contact'),
                    "emergency_phone": original_resident.get('emergency_phone'),
                    "deposit": original_resident.get('deposit', 0.0),
                    "lease_end_date": original_lease_end
                }
                
                requests.put(f'{base_url}/residents/{resident_id}', 
                            json=restore_data, headers=headers)
                
                print(f"   ✅ 已恢復原始資訊")
                return True
            else:
                print(f"   ❌ 無法獲取最終結果進行驗證")
                return False
        else:
            print(f"   ❌ 租期展期失敗: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 更新同時展期測試錯誤: {e}")
        return False

def test_frontend_edit_form_features():
    """測試前端編輯表單功能"""
    print(f"\n🖥️ 測試前端編輯表單功能...")
    
    try:
        # 檢查前端文件是否正確修改
        residents_file = 'frontend/pages/residents.py'
        
        if os.path.exists(residents_file):
            with open(residents_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"   檢查 {residents_file}:")
            
            # 檢查編輯表單是否添加了展期功能
            features_to_check = [
                '同時進行租期展期',
                'enable_lease_extension',
                'perform_update_with_extension',
                'perform_basic_update',
                'extension_type',
                'quick_extension',
                'edit_extension_reason'
            ]
            
            for feature in features_to_check:
                if feature in content:
                    print(f"      ✅ {feature} 功能已添加")
                else:
                    print(f"      ❌ {feature} 功能缺失")
                    return False
            
            return True
        else:
            print(f"   ❌ {residents_file} 不存在")
            return False
            
    except Exception as e:
        print(f"❌ 前端編輯表單功能測試錯誤: {e}")
        return False

if __name__ == "__main__":
    print("🧪 編輯住戶時租期展期功能測試")
    print("=" * 60)
    
    # 1. 獲取測試住戶
    test_result = get_test_resident()
    
    if test_result:
        resident_id, token, original_resident = test_result
        
        # 2. 測試基本資訊更新（不展期）
        basic_update_test = test_basic_update_only(resident_id, token, original_resident)
        
        # 3. 測試更新資訊同時展期
        update_with_extension_test = test_update_with_extension(resident_id, token, original_resident)
        
    else:
        basic_update_test = False
        update_with_extension_test = False
    
    # 4. 測試前端編輯表單功能
    frontend_test = test_frontend_edit_form_features()
    
    print("\n" + "=" * 60)
    print("📝 測試結果總結:")
    print("1. ✅ 基本資訊更新" if basic_update_test else "1. ❌ 基本資訊更新")
    print("2. ✅ 更新同時展期" if update_with_extension_test else "2. ❌ 更新同時展期")
    print("3. ✅ 前端編輯表單功能" if frontend_test else "3. ❌ 前端編輯表單功能")
    
    all_passed = all([basic_update_test, update_with_extension_test, frontend_test])
    
    print("\n💡 新功能特點:")
    print("📝 編輯住戶時租期展期:")
    print("   - ✅ 在編輯住戶表單中添加租期展期選項")
    print("   - ✅ 可選擇是否同時進行租期展期")
    print("   - ✅ 提供快速展期選項（3個月、6個月、1年、2年）")
    print("   - ✅ 支援自定義展期日期")
    print("   - ✅ 顯示展期前後對比")
    print("   - ✅ 一次操作完成資訊更新和租期展期")
    
    print("\n🔒 安全機制:")
    print("   - ✅ 僅對活躍住戶顯示展期選項")
    print("   - ✅ 驗證新租約到期日必須晚於原到期日")
    print("   - ✅ 驗證新租約到期日必須晚於今天")
    print("   - ✅ 分步驟執行：先更新基本資訊，再展期")
    print("   - ✅ 詳細的成功/失敗訊息")
    
    print("\n🎯 使用方式:")
    print("1. 前往「住戶管理」→「住戶列表」")
    print("2. 點擊住戶的「編輯」按鈕")
    print("3. 修改基本資訊（姓名、電話等）")
    print("4. 勾選「同時進行租期展期」選項")
    print("5. 選擇快速展期或自定義日期")
    print("6. 填寫展期原因（選填）")
    print("7. 點擊「更新住戶」完成操作")
    
    print(f"\n🏁 測試完成 - {'全部通過' if all_passed else '部分失敗'}")
    
    if all_passed:
        print("\n🎉 編輯住戶時租期展期功能已成功實作並測試通過！")
        print("   現在可以在編輯住戶時同時進行租期展期。")
