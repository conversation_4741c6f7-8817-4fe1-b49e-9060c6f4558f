#!/usr/bin/env python3
"""
資料庫初始化腳本
"""

from app.database import create_tables, get_db
from app.models import User, Room, UtilityRate
from app.auth import get_password_hash
from datetime import datetime

def init_database():
    """初始化資料庫"""
    print("正在初始化資料庫...")
    
    # 創建資料庫表格
    create_tables()
    print("✓ 資料庫表格創建完成")
    
    # 獲取資料庫會話
    db = next(get_db())
    
    try:
        # 檢查是否已有管理員用戶
        admin_user = db.query(User).filter(User.username == "admin").first()
        if not admin_user:
            # 創建預設管理員帳號
            hashed_password = get_password_hash("admin123")
            admin_user = User(
                username="admin",
                password_hash=hashed_password,
                role="admin",
                email="<EMAIL>"
            )
            db.add(admin_user)
            print("✓ 創建預設管理員帳號: admin/admin123")
        else:
            print("✓ 管理員帳號已存在")
        
        # 檢查是否已有費率設定
        utility_rate = db.query(UtilityRate).first()
        if not utility_rate:
            # 創建預設費率
            default_rate = UtilityRate(
                electricity_rate=5.5,
                monthly_water_fee=200.0,
                effective_date=datetime.utcnow(),
                notes="系統預設費率"
            )
            db.add(default_rate)
            print("✓ 創建預設費率設定")
        else:
            print("✓ 費率設定已存在")
        
        # 檢查是否已有房間資料
        room_count = db.query(Room).count()
        if room_count == 0:
            # 創建一些示例房間
            sample_rooms = [
                Room(room_number="A101", floor=1, rent_single=8000, rent_double=12000, description="雅房，有窗戶"),
                Room(room_number="A102", floor=1, rent_single=9000, rent_double=13000, description="套房，有獨立衛浴"),
                Room(room_number="A201", floor=2, rent_single=8500, rent_double=12500, description="雅房，採光良好"),
                Room(room_number="A202", floor=2, rent_single=10000, rent_double=15000, description="大套房，有陽台"),
                Room(room_number="B101", floor=1, rent_single=7500, rent_double=11000, description="小雅房"),
            ]
            
            for room in sample_rooms:
                db.add(room)
            print("✓ 創建示例房間資料")
        else:
            print(f"✓ 房間資料已存在 ({room_count} 間房間)")
        
        # 提交所有變更
        db.commit()
        print("✓ 資料庫初始化完成")
        
        # 顯示登入資訊
        print("\n" + "="*50)
        print("🎉 系統初始化成功！")
        print("="*50)
        print("預設管理員帳號：")
        print("  用戶名：admin")
        print("  密碼：admin123")
        print("="*50)
        
    except Exception as e:
        db.rollback()
        print(f"❌ 資料庫初始化失敗: {e}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    init_database()
