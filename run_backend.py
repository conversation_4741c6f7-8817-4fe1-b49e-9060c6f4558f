#!/usr/bin/env python3
"""
後端服務啟動腳本
"""

import subprocess
import sys
import os
import time

def check_dependencies():
    """檢查後端依賴"""
    print("🔍 檢查後端依賴...")

    try:
        import fastapi
        import uvicorn
        import sqlalchemy
        import pydantic
        import passlib
        import jose
        print("✅ 後端依賴檢查通過")
        return True
    except ImportError as e:
        print(f"❌ 缺少依賴: {e}")
        print("請執行: uv sync")
        return False

def start_backend():
    """啟動後端服務"""
    print("🚀 啟動後端服務...")
    
    # 切換到後端目錄
    backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
    
    try:
        # 啟動FastAPI服務
        cmd = [
            ".venv\\Scripts\\python", "-m", "uvicorn",
            "app.main:app",
            "--host", "0.0.0.0",
            "--port", "8080",
            "--reload"
        ]
        
        print(f"執行命令: {' '.join(cmd)}")
        print("後端服務將在 http://localhost:8080 啟動")
        print("API文檔將在 http://localhost:8080/docs 提供")
        print("按 Ctrl+C 停止服務")
        print("-" * 50)
        
        # 在後端目錄中執行
        subprocess.run(cmd, cwd=backend_dir)
        
    except KeyboardInterrupt:
        print("\n🛑 後端服務已停止")
    except Exception as e:
        print(f"❌ 啟動後端服務失敗: {e}")
        return False
    
    return True

def main():
    """主程式"""
    print("🏠 租房管理系統 - 後端服務")
    print("=" * 50)
    
    # 檢查依賴
    if not check_dependencies():
        sys.exit(1)
    
    # 檢查是否已初始化
    db_file = "rental_management.db"
    if not os.path.exists(db_file):
        print("⚠️  數據庫文件不存在，請先執行初始化:")
        print("   python init_system.py")
        sys.exit(1)
    
    # 啟動服務
    start_backend()

if __name__ == "__main__":
    main()
