# 租房管理系統技術架構深度實現

## 認知框架導向的系統設計延伸

### 思維模式映射技術架構

**多層次認知處理方法：**
```python
# 系統認知架構模型
技術認知框架
├── 領域整合策略層
│   ├── 業務邏輯抽象化
│   ├── 跨模組協同機制
│   └── 認知負荷平衡設計
├── 元認知感知層
│   ├── 系統狀態自我監控
│   ├── 錯誤自我診斷機制
│   └── 適應性調整策略
├── 策略性知識合成層
│   ├── 資料模式識別
│   ├── 預測性分析整合
│   └── 智慧化決策支援
└── 持續自我驗證層
    ├── 即時性能監控
    ├── 業務規則驗證
    └── 系統穩定性保障
```

## 前端組件系統深度實現

### 住戶管理頁面架構

**pages/residents.py - 住戶管理系統：**

```python
import streamlit as st
import pandas as pd
from datetime import datetime, date
from api_client import api_client
from typing import Dict, List, Optional
import plotly.express as px

class ResidentManagementSystem:
    """住戶管理系統核心類"""
    
    def __init__(self):
        self.api_client = api_client
        self.init_session_state()
    
    def init_session_state(self):
        """初始化會話狀態"""
        if 'selected_resident' not in st.session_state:
            st.session_state.selected_resident = None
        if 'resident_form_data' not in st.session_state:
            st.session_state.resident_form_data = {}

def show_residents_page():
    """住戶管理主頁面"""
    st.title("👥 住戶管理")
    
    # 初始化管理系統
    resident_system = ResidentManagementSystem()
    
    # 頁籤設計
    tab1, tab2, tab3, tab4 = st.tabs([
        "住戶列表", "新增住戶", "住戶統計", "入住/退房"
    ])
    
    with tab1:
        show_residents_list(resident_system)
    
    with tab2:
        show_create_resident_form(resident_system)
    
    with tab3:
        show_residents_statistics(resident_system)
    
    with tab4:
        show_move_in_out_management(resident_system)

def show_residents_list(resident_system: ResidentManagementSystem):
    """住戶列表展示"""
    st.subheader("住戶列表")
    
    # 進階搜尋和篩選介面
    col1, col2, col3, col4 = st.columns([2, 1, 1, 1])
    
    with col1:
        search_term = st.text_input("🔍 搜尋住戶", placeholder="姓名、身份證、電話")
    
    with col2:
        room_filter = st.selectbox("房間篩選", ["全部"] + get_room_options())
    
    with col3:
        status_filter = st.selectbox("狀態篩選", ["全部", "在住", "已退房"])
    
    with col4:
        sort_option = st.selectbox("排序方式", ["入住日期", "姓名", "房間號"])
    
    # 獲取住戶數據
    residents = resident_system.api_client.get_residents()
    
    if not residents:
        st.info("🏠 暫無住戶資料")
        return
    
    # 數據處理和篩選
    filtered_residents = apply_resident_filters(
        residents, search_term, room_filter, status_filter, sort_option
    )
    
    # 數據表格展示
    if filtered_residents:
        display_residents_table(filtered_residents)
        
        # 住戶詳細操作
        show_resident_operations(filtered_residents)
    else:
        st.info("🔍 沒有符合條件的住戶")

def show_create_resident_form(resident_system: ResidentManagementSystem):
    """新增住戶表單"""
    st.subheader("新增住戶")
    
    # 動態表單設計
    with st.form("create_resident_form", clear_on_submit=True):
        # 基本資料區塊
        st.markdown("### 📋 基本資料")
        col1, col2 = st.columns(2)
        
        with col1:
            name = st.text_input("姓名*", placeholder="請輸入完整姓名")
            phone = st.text_input("聯絡電話", placeholder="09XX-XXX-XXX")
            emergency_contact = st.text_input("緊急聯絡人", placeholder="家人或朋友")
        
        with col2:
            id_number = st.text_input("身份證號*", placeholder="A123456789")
            emergency_phone = st.text_input("緊急聯絡電話", placeholder="09XX-XXX-XXX")
            deposit = st.number_input("押金金額", min_value=0, value=10000, step=1000)
        
        # 住宿資料區塊
        st.markdown("### 🏠 住宿資料")
        col3, col4 = st.columns(2)
        
        with col3:
            available_rooms = get_available_rooms()
            if available_rooms:
                room_options = [f"{r['room_number']} (可住{r['max_occupants'] - r['current_occupants']}人)" 
                               for r in available_rooms]
                selected_room_idx = st.selectbox("選擇房間*", range(len(room_options)), 
                                                format_func=lambda x: room_options[x])
                selected_room = available_rooms[selected_room_idx]
            else:
                st.error("❌ 目前沒有可用房間")
                selected_room = None
        
        with col4:
            move_in_date = st.date_input("入住日期*", value=date.today())
            
            # 顯示租金資訊
            if selected_room:
                current_occupants = selected_room['current_occupants']
                future_occupants = current_occupants + 1
                
                if future_occupants == 1:
                    rent = selected_room['rent_single']
                else:
                    rent = selected_room['rent_double']
                
                st.info(f"💰 租金: ${rent:,} ({future_occupants}人住)")
        
        # 表單驗證和提交
        submitted = st.form_submit_button("🏠 新增住戶", use_container_width=True)
        
        if submitted:
            validation_result = validate_resident_form(
                name, id_number, phone, selected_room, move_in_date
            )
            
            if validation_result['valid']:
                resident_data = {
                    "name": name,
                    "id_number": id_number,
                    "phone": phone or None,
                    "emergency_contact": emergency_contact or None,
                    "emergency_phone": emergency_phone or None,
                    "room_id": selected_room['id'],
                    "move_in_date": move_in_date.isoformat(),
                    "deposit": deposit
                }
                
                process_resident_creation(resident_system, resident_data)
            else:
                st.error(f"❌ {validation_result['message']}")

def show_utilities_page():
    """費用管理頁面"""
    st.title("💰 費用管理")
    
    # 費用管理系統架構
    utility_system = UtilityManagementSystem()
    
    # 主要功能頁籤
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "費率設定", "電表抄錄", "帳單生成", "付款管理", "費用統計"
    ])
    
    with tab1:
        show_utility_rates_management(utility_system)
    
    with tab2:
        show_meter_reading_interface(utility_system)
    
    with tab3:
        show_bill_generation_system(utility_system)
    
    with tab4:
        show_payment_management(utility_system)
    
    with tab5:
        show_utility_statistics(utility_system)

class UtilityManagementSystem:
    """費用管理系統核心類"""
    
    def __init__(self):
        self.api_client = api_client
        self.current_rates = None
        self.load_current_rates()
    
    def load_current_rates(self):
        """載入當前費率"""
        try:
            self.current_rates = self.api_client.get_current_utility_rates()
        except Exception as e:
            st.error(f"載入費率失敗: {str(e)}")
    
    def calculate_monthly_costs(self, room_id: int, electricity_usage: float) -> Dict:
        """計算月度費用"""
        if not self.current_rates:
            raise ValueError("費率未設定")
        
        electricity_cost = electricity_usage * self.current_rates['electricity_rate']
        water_cost = self.current_rates['monthly_water_fee']
        
        return {
            "electricity_cost": electricity_cost,
            "water_cost": water_cost,
            "total_cost": electricity_cost + water_cost,
            "electricity_usage": electricity_usage,
            "rate_info": self.current_rates
        }

def show_utility_rates_management(utility_system: UtilityManagementSystem):
    """費率設定介面"""
    st.subheader("⚡ 費率設定")
    
    # 當前費率顯示
    if utility_system.current_rates:
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("電費每度", f"${utility_system.current_rates['electricity_rate']}")
        
        with col2:
            st.metric("月度水費", f"${utility_system.current_rates['monthly_water_fee']}")
        
        with col3:
            effective_date = utility_system.current_rates.get('effective_date', 'N/A')
            st.metric("生效日期", effective_date[:10] if effective_date != 'N/A' else 'N/A')
    
    # 新費率設定表單
    st.markdown("### 設定新費率")
    
    with st.form("utility_rates_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            new_electricity_rate = st.number_input(
                "電費每度 (TWD)", 
                min_value=0.0, 
                value=5.5, 
                step=0.1,
                help="每度電費價格"
            )
        
        with col2:
            new_water_fee = st.number_input(
                "月度水費 (TWD)", 
                min_value=0, 
                value=200, 
                step=10,
                help="每月固定水費"
            )
        
        effective_date = st.date_input(
            "生效日期", 
            value=date.today(),
            help="新費率開始生效的日期"
        )
        
        notes = st.text_area("備註", placeholder="費率調整原因或說明")
        
        submitted = st.form_submit_button("💾 設定新費率", use_container_width=True)
        
        if submitted:
            rate_data = {
                "electricity_rate": new_electricity_rate,
                "monthly_water_fee": new_water_fee,
                "effective_date": effective_date.isoformat(),
                "notes": notes
            }
            
            try:
                result = utility_system.api_client.create_utility_rate(rate_data)
                if result:
                    st.success("✅ 費率設定成功！")
                    st.rerun()
            except Exception as e:
                st.error(f"❌ 設定失敗: {str(e)}")

def show_meter_reading_interface(utility_system: UtilityManagementSystem):
    """電表抄錄介面"""
    st.subheader("📊 電表抄錄")
    
    # 選擇抄錄月份
    col1, col2 = st.columns(2)
    
    with col1:
        reading_year = st.selectbox("抄錄年份", range(2020, 2030), index=5)
    
    with col2:
        reading_month = st.selectbox("抄錄月份", range(1, 13), index=datetime.now().month - 1)
    
    # 獲取房間列表
    rooms = utility_system.api_client.get_rooms()
    
    if not rooms:
        st.warning("⚠️ 沒有房間資料")
        return
    
    # 批量抄錄介面
    st.markdown("### 批量電表抄錄")
    
    with st.form("meter_reading_form"):
        readings_data = []
        
        # 為每個房間創建抄錄輸入
        for room in rooms:
            if room['current_occupants'] > 0:  # 只顯示有住戶的房間
                col1, col2, col3 = st.columns([2, 2, 2])
                
                with col1:
                    st.text(f"房間 {room['room_number']}")
                    st.text(f"住戶: {room['current_occupants']}人")
                
                with col2:
                    # 獲取上月讀數
                    previous_reading = get_previous_reading(room['id'], reading_year, reading_month)
                    st.text(f"上月讀數: {previous_reading}")
                
                with col3:
                    current_reading = st.number_input(
                        f"本月讀數",
                        min_value=previous_reading,
                        value=previous_reading,
                        step=1,
                        key=f"reading_{room['id']}"
                    )
                    
                    usage = current_reading - previous_reading
                    st.text(f"用電量: {usage} 度")
                
                readings_data.append({
                    "room_id": room['id'],
                    "room_number": room['room_number'],
                    "previous_reading": previous_reading,
                    "current_reading": current_reading,
                    "usage": usage
                })
                
                st.divider()
        
        # 提交抄錄
        submitted = st.form_submit_button("📝 提交抄錄", use_container_width=True)
        
        if submitted:
            process_meter_readings(utility_system, readings_data, reading_year, reading_month)

def show_reports_page():
    """報表統計頁面"""
    st.title("📊 報表統計")
    
    # 報表系統架構
    report_system = ReportingSystem()
    
    # 報表類型選擇
    tab1, tab2, tab3, tab4 = st.tabs([
        "營收報表", "住戶報表", "費用分析", "自定義報表"
    ])
    
    with tab1:
        show_revenue_report(report_system)
    
    with tab2:
        show_resident_report(report_system)
    
    with tab3:
        show_utility_analysis(report_system)
    
    with tab4:
        show_custom_report(report_system)

class ReportingSystem:
    """報表系統核心類"""
    
    def __init__(self):
        self.api_client = api_client
        self.cache = {}
    
    def get_revenue_data(self, start_date: date, end_date: date) -> Dict:
        """獲取營收數據"""
        cache_key = f"revenue_{start_date}_{end_date}"
        
        if cache_key not in self.cache:
            try:
                data = self.api_client.get_revenue_report(start_date, end_date)
                self.cache[cache_key] = data
            except Exception as e:
                st.error(f"獲取營收數據失敗: {str(e)}")
                return {}
        
        return self.cache.get(cache_key, {})
    
    def generate_occupancy_trends(self) -> Dict:
        """生成入住率趨勢"""
        try:
            rooms = self.api_client.get_rooms()
            residents = self.api_client.get_residents()
            
            # 計算月度入住率
            monthly_occupancy = calculate_monthly_occupancy(rooms, residents)
            
            return {
                "monthly_data": monthly_occupancy,
                "current_rate": calculate_current_occupancy_rate(rooms),
                "trends": analyze_occupancy_trends(monthly_occupancy)
            }
        except Exception as e:
            st.error(f"生成入住率趨勢失敗: {str(e)}")
            return {}

def show_revenue_report(report_system: ReportingSystem):
    """營收報表"""
    st.subheader("💰 營收報表")
    
    # 日期範圍選擇
    col1, col2 = st.columns(2)
    
    with col1:
        start_date = st.date_input("開始日期", value=date.today().replace(day=1))
    
    with col2:
        end_date = st.date_input("結束日期", value=date.today())
    
    if start_date > end_date:
        st.error("❌ 開始日期不能晚於結束日期")
        return
    
    # 獲取營收數據
    revenue_data = report_system.get_revenue_data(start_date, end_date)
    
    if not revenue_data:
        st.info("📈 暫無營收數據")
        return
    
    # 營收指標展示
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("總營收", f"${revenue_data.get('total_revenue', 0):,}")
    
    with col2:
        st.metric("租金收入", f"${revenue_data.get('rent_income', 0):,}")
    
    with col3:
        st.metric("公用事業費", f"${revenue_data.get('utility_income', 0):,}")
    
    with col4:
        st.metric("其他收入", f"${revenue_data.get('other_income', 0):,}")
    
    # 營收趨勢圖表
    if 'monthly_revenue' in revenue_data:
        fig = px.line(
            revenue_data['monthly_revenue'],
            x='month',
            y='revenue',
            title='月度營收趨勢',
            labels={'revenue': '營收 (TWD)', 'month': '月份'}
        )
        st.plotly_chart(fig, use_container_width=True)
    
    # 營收構成分析
    if 'revenue_breakdown' in revenue_data:
        fig = px.pie(
            values=list(revenue_data['revenue_breakdown'].values()),
            names=list(revenue_data['revenue_breakdown'].keys()),
            title='營收構成分析'
        )
        st.plotly_chart(fig, use_container_width=True)

## 完整路由實現架構

### 住戶管理API路由

**routers/residents.py - 住戶管理API：**

```python
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel, validator
from typing import List, Optional
from datetime import datetime, date
from auth import get_current_user, require_role
from services import ResidentService, RoomService
from database import db_config

router = APIRouter(prefix="/residents", tags=["residents"])

class ResidentCreate(BaseModel):
    name: str
    id_number: str
    phone: Optional[str] = None
    emergency_contact: Optional[str] = None
    emergency_phone: Optional[str] = None
    room_id: int
    move_in_date: date
    deposit: float = 0.0
    
    @validator('name')
    def validate_name(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('姓名至少需要2個字符')
        return v.strip()
    
    @validator('id_number')
    def validate_id_number(cls, v):
        if not v or len(v) != 10:
            raise ValueError('身份證號必須為10個字符')
        return v.upper()
    
    @validator('phone')
    def validate_phone(cls, v):
        if v and not v.replace('-', '').replace(' ', '').isdigit():
            raise ValueError('電話號碼格式不正確')
        return v

class ResidentResponse(BaseModel):
    id: int
    name: str
    id_number: str
    phone: Optional[str]
    emergency_contact: Optional[str]
    emergency_phone: Optional[str]
    room_id: int
    move_in_date: date
    move_out_date: Optional[date]
    deposit: float
    is_active: bool
    created_at: datetime
    
    # 關聯數據
    room: Optional[Dict] = None
    
    class Config:
        orm_mode = True

class ResidentUpdate(BaseModel):
    name: Optional[str] = None
    phone: Optional[str] = None
    emergency_contact: Optional[str] = None
    emergency_phone: Optional[str] = None
    deposit: Optional[float] = None

@router.post("/", response_model=ResidentResponse)
async def create_resident(
    resident_data: ResidentCreate,
    db: Session = Depends(db_config.get_db),
    current_user: dict = Depends(require_role(["admin", "manager"]))
):
    """創建新住戶"""
    try:
        # 驗證房間可用性
        room = RoomService.get_room_by_id(db, resident_data.room_id)
        if not room:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="房間不存在"
            )
        
        if room.current_occupants >= room.max_occupants:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="房間已滿"
            )
        
        # 檢查身份證是否重複
        existing_resident = ResidentService.get_resident_by_id_number(
            db, resident_data.id_number
        )
        if existing_resident and existing_resident.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="身份證號已存在"
            )
        
        # 創建住戶
        resident = ResidentService.create_resident(db, resident_data.dict())
        
        # 返回包含房間資訊的響應
        resident_response = ResidentResponse.from_orm(resident)
        resident_response.room = room.to_dict()
        
        return resident_response
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"創建住戶失敗: {str(e)}"
        )

@router.get("/", response_model=List[ResidentResponse])
async def get_residents(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    room_id: Optional[int] = Query(None),
    active_only: bool = Query(True),
    db: Session = Depends(db_config.get_db),
    current_user: dict = Depends(get_current_user)
):
    """獲取住戶列表"""
    try:
        residents = ResidentService.get_residents(
            db, 
            skip=skip, 
            limit=limit,
            room_id=room_id,
            active_only=active_only
        )
        
        # 添加房間資訊
        for resident in residents:
            resident.room = resident.room.to_dict() if resident.room else None
        
        return residents
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"獲取住戶列表失敗: {str(e)}"
        )

@router.get("/{resident_id}", response_model=ResidentResponse)
async def get_resident(
    resident_id: int,
    db: Session = Depends(db_config.get_db),
    current_user: dict = Depends(get_current_user)
):
    """獲取特定住戶"""
    resident = ResidentService.get_resident_by_id(db, resident_id)
    
    if not resident:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="住戶不存在"
        )
    
    resident_response = ResidentResponse.from_orm(resident)
    resident_response.room = resident.room.to_dict() if resident.room else None
    
    return resident_response

@router.put("/{resident_id}", response_model=ResidentResponse)
async def update_resident(
    resident_id: int,
    resident_data: ResidentUpdate,
    db: Session = Depends(db_config.get_db),
    current_user: dict = Depends(require_role(["admin", "manager"]))
):
    """更新住戶資訊"""
    try:
        resident = ResidentService.update_resident(db, resident_id, resident_data.dict(exclude_unset=True))
        
        if not resident:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="住戶不存在"
            )
        
        resident_response = ResidentResponse.from_orm(resident)
        resident_response.room = resident.room.to_dict() if resident.room else None
        
        return resident_response
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"更新住戶失敗: {str(e)}"
        )

@router.post("/{resident_id}/move-out")
async def move_out_resident(
    resident_id: int,
    move_out_date: date,
    db: Session = Depends(db_config.get_db),
    current_user: dict = Depends(require_role(["admin", "manager"]))
):
    """住戶退房"""
    try:
        resident = ResidentService.move_out_resident(db, resident_id, move_out_date)
        
        if not resident:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="住戶不存在"
            )
        
        return {"message": "退房成功", "resident_id": resident_id}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"退房失敗: {str(e)}"
        )
```

### 費用管理API路由

**routers/utilities.py - 費用管理API：**

```python
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel, validator
from typing import List, Optional
from datetime import datetime, date
from auth import get_current_user, require_role
from services import UtilityService
from database import db_config

router = APIRouter(prefix="/utilities", tags=["utilities"])

class UtilityRateCreate(BaseModel):
    electricity_rate: float
    monthly_water_fee: float
    effective_date: date
    notes: Optional[str] = None
    
    @validator('electricity_rate')
    def validate_electricity_rate(cls, v):
        if v <= 0:
            raise ValueError('電費費率必須大於0')
        return v
    
    @validator('monthly_water_fee')
    def validate_water_fee(cls, v):
        if v <= 0:
            raise ValueError('水費必須大於0')
        return v

class UtilityRateResponse(BaseModel):
    id: int
    electricity_rate: float
    monthly_water_fee: float
    effective_date: date
    notes: Optional[str]
    created_at: datetime
    is_active: bool
    
    class Config:
        orm_mode = True

class MeterReadingCreate(BaseModel):
    room_id: int
    billing_year: int
    billing_month: int
    current_electricity_reading: float
    
    @validator('billing_month')
    def validate_month(cls, v):
        if not 1 <= v <= 12:
            raise ValueError('月份必須在1-12之間')
        return v
    
    @validator('current_electricity_reading')
    def validate_reading(cls, v):
        if v < 0:
            raise ValueError('電表讀數不能為負數')
        return v

class UtilityRecordResponse(BaseModel):
    id: int
    room_id: int
    billing_year: int
    billing_month: int
    previous_electricity_reading: float
    current_electricity_reading: float
    electricity_usage: float
    electricity_rate: float
    electricity_cost: float
    water_fee: float
    total_amount: float
    payment_status: str
    payment_date: Optional[datetime]
    created_at: datetime
    
    # 關聯數據
    room: Optional[Dict] = None
    
    class Config:
        orm_mode = True

@router.post("/rates", response_model=UtilityRateResponse)
async def create_utility_rate(
    rate_data: UtilityRateCreate,
    db: Session = Depends(db_config.get_db),
    current_user: dict = Depends(require_role(["admin", "manager"]))
):
    """創建新費率"""
    try:
        # 檢查是否已有相同生效日期的費率
        existing_rate = UtilityService.get_rate_by_date(db, rate_data.effective_date)
        if existing_rate:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="該日期已有費率設定"
            )
        
        rate = UtilityService.create_utility_rate(db, rate_data.dict())
        return rate
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"創建費率失敗: {str(e)}"
        )

@router.get("/rates/current", response_model=UtilityRateResponse)
async def get_current_utility_rate(
    db: Session = Depends(db_config.get_db),
    current_user: dict = Depends(get_current_user)
):
    """獲取當前費率"""
    rate = UtilityService.get_current_utility_rate(db)
    
    if not rate:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="未設定費率"
        )
    
    return rate

@router.post("/readings", response_model=UtilityRecordResponse)
async def create_meter_reading(
    reading_data: MeterReadingCreate,
    db: Session = Depends(db_config.get_db),
    current_user: dict = Depends(require_role(["admin", "manager"]))
):
    """創建電表抄錄記錄"""
    try:
        # 檢查是否已有該月份的記錄
        existing_record = UtilityService.get_monthly_record(
            db, reading_data.room_id, reading_data.billing_year, reading_data.billing_month
        )
        
        if existing_record:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="該月份已有抄錄記錄"
            )
        
        # 獲取上月讀數
        previous_reading = UtilityService.get_previous_reading(
            db, reading_data.room_id, reading_data.billing_year, reading_data.billing_month
        )
        
        # 驗證讀數合理性
        if reading_data.current_electricity_reading < previous_reading:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"當前讀數({reading_data.current_electricity_reading})不能小於上月讀數({previous_reading})"
            )
        
        # 計算並創建記錄
        record = UtilityService.calculate_monthly_bill(
            db,
            reading_data.room_id,
            reading_data.billing_year,
            reading_data.billing_month,
            reading_data.current_electricity_reading,
            previous_reading
        )
        
        # 添加房間資訊
        record_response = UtilityRecordResponse.from_orm(record)
        record_response.room = record.room.to_dict() if record.room else None
        
        return record_response
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"創建抄錄記錄失敗: {str(e)}"
        )

@router.get("/bills", response_model=List[UtilityRecordResponse])
async def get_utility_bills(
    year: int = Query(..., ge=2020, le=2050),
    month: int = Query(..., ge=1, le=12),
    room_id: Optional[int] = Query(None),
    payment_status: Optional[str] = Query(None),
    db: Session = Depends(db_config.get_db),
    current_user: dict = Depends(get_current_user)
):
    """獲取費用帳單"""
    try:
        bills = UtilityService.get_monthly_bills(
            db, year, month, room_id, payment_status
        )
        
        # 添加房間資訊
        for bill in bills:
            bill.room = bill.room.to_dict() if bill.room else None
        
        return bills
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"獲取帳單失敗: {str(e)}"
        )

@router.put("/bills/{bill_id}/payment")
async def update_payment_status(
    bill_id: int,
    payment_status: str,
    payment_date: Optional[datetime] = None,
    db: Session = Depends(db_config.get_db),
    current_user: dict = Depends(require_role(["admin", "manager"]))
):
    """更新付款狀態"""
    try:
        valid_statuses = ["pending", "paid", "overdue"]
        if payment_status not in valid_statuses:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"無效的付款狀態，請選擇: {', '.join(valid_statuses)}"
            )
        
        bill = UtilityService.update_payment_status(
            db, bill_id, payment_status, payment_date
        )
        
        if not bill:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="帳單不存在"
            )
        
        return {"message": "付款狀態更新成功", "bill_id": bill_id}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"更新付款狀態失敗: {str(e)}"
        )
```

## 系統配置與工具架構

### 配置管理系統

**config.py - 統一配置管理：**

```python
import os
from typing import Optional
from pydantic import BaseSettings, validator
from functools import lru_cache

class Settings(BaseSettings):
    """系統設定類"""
    
    # 應用程式設定
    app_name: str = "租房管理系統"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # 資料庫設定
    database_url: str = "sqlite:///./rental_management.db"
    
    # JWT設定
    secret_key: str = "your-secret-key-here"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # 安全性設定
    password_min_length: int = 8
    max_login_attempts: int = 5
    lockout_duration_minutes: int = 15
    
    # 應用程式設定
    default_electricity_rate: float = 5.5
    default_water_fee: float = 200.0
    
    # 日誌設定
    log_level: str = "INFO"
    log_file: str = "rental_management.log"
    
    # 備份設定
    backup_dir: str = "./backups"
    backup_retention_days: int = 30
    
    # 分頁設定
    default_page_size: int = 20
    max_page_size: int = 100
    
    @validator('secret_key')
    def validate_secret_key(cls, v):
        if len(v) < 32:
            raise ValueError('SECRET_KEY長度至少32個字符')
        return v
    
    @validator('database_url')
    def validate_database_url(cls, v):
        if not v.startswith(('sqlite:///', 'postgresql://', 'mysql://')):
            raise ValueError('不支援的資料庫類型')
        return v
    
    class Config:
        env_file = ".env"
        env_prefix = "RENTAL_"

@lru_cache()
def get_settings():
    """獲取系統設定（帶快取）"""
    return Settings()

# 全域設定實例
settings = get_settings()
```

### 日誌系統架構

**logging_config.py - 日誌配置：**

```python
import logging
import logging.handlers
import os
from datetime import datetime
from config import settings

class ColoredFormatter(logging.Formatter):
    """彩色日誌格式化器"""
    
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 綠色
        'WARNING': '\033[33m',  # 黃色
        'ERROR': '\033[31m',    # 紅色
        'CRITICAL': '\033[35m', # 紫色
    }
    RESET = '\033[0m'
    
    def format(self, record):
        log_color = self.COLORS.get(record.levelname, self.RESET)
        record.levelname = f"{log_color}{record.levelname}{self.RESET}"
        return super().format(record)

def setup_logging():
    """設定日誌系統"""
    
    # 建立日誌目錄
    log_dir = os.path.dirname(settings.log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 根日誌記錄器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.log_level.upper()))
    
    # 清除現有處理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 控制台處理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_formatter = ColoredFormatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    # 文件處理器（輪轉）
    file_handler = logging.handlers.RotatingFileHandler(
        settings.log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setLevel(logging.DEBUG)
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    file_handler.setFormatter(file_formatter)
    root_logger.addHandler(file_handler)
    
    # 應用程式日誌記錄器
    app_logger = logging.getLogger('rental_management')
    app_logger.setLevel(logging.DEBUG)
    
    return app_logger

# 全域日誌實例
logger = setup_logging()
```

### 錯誤處理系統

**exceptions.py - 異常處理：**

```python
from typing import Any, Dict, Optional
from fastapi import HTTPException, Request, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from config import settings
from logging_config import logger

class RentalManagementException(Exception):
    """基礎異常類"""
    
    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        self.message = message
        self.error_code = error_code or "RENTAL_ERROR"
        self.details = details or {}
        super().__init__(self.message)

class BusinessLogicError(RentalManagementException):
    """業務邏輯異常"""
    pass

class ValidationError(RentalManagementException):
    """驗證異常"""
    pass

class DatabaseError(RentalManagementException):
    """資料庫異常"""
    pass

class AuthenticationError(RentalManagementException):
    """認證異常"""
    pass

class AuthorizationError(RentalManagementException):
    """授權異常"""
    pass

async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """驗證異常處理器"""
    logger.error(f"驗證錯誤: {exc.errors()}")
    
    error_details = []
    for error in exc.errors():
        field = " -> ".join(str(x) for x in error["loc"])
        error_details.append({
            "field": field,
            "message": error["msg"],
            "type": error["type"]
        })
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "error": "驗證失敗",
            "error_code": "VALIDATION_ERROR",
            "details": error_details
        }
    )

async def database_exception_handler(request: Request, exc: SQLAlchemyError):
    """資料庫異常處理器"""
    logger.error(f"資料庫錯誤: {str(exc)}")
    
    if isinstance(exc, IntegrityError):
        return JSONResponse(
            status_code=status.HTTP_409_CONFLICT,
            content={
                "error": "資料完整性錯誤",
                "error_code": "INTEGRITY_ERROR",
                "message": "資料違反唯一性約束或外鍵約束"
            }
        )
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": "資料庫操作失敗",
            "error_code": "DATABASE_ERROR",
            "message": "請聯絡系統管理員" if not settings.debug else str(exc)
        }
    )

async def rental_exception_handler(request: Request, exc: RentalManagementException):
    """租房管理系統異常處理器"""
    logger.error(f"業務異常: {exc.message}")
    
    status_code_map = {
        BusinessLogicError: status.HTTP_400_BAD_REQUEST,
        ValidationError: status.HTTP_422_UNPROCESSABLE_ENTITY,
        DatabaseError: status.HTTP_500_INTERNAL_SERVER_ERROR,
        AuthenticationError: status.HTTP_401_UNAUTHORIZED,
        AuthorizationError: status.HTTP_403_FORBIDDEN,
    }
    
    status_code = status_code_map.get(type(exc), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    return JSONResponse(
        status_code=status_code,
        content={
            "error": exc.message,
            "error_code": exc.error_code,
            "details": exc.details
        }
    )

async def general_exception_handler(request: Request, exc: Exception):
    """通用異常處理器"""
    logger.error(f"未處理的異常: {type(exc).__name__}: {str(exc)}")
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": "系統內部錯誤",
            "error_code": "INTERNAL_ERROR",
            "message": "請聯絡系統管理員" if not settings.debug else str(exc)
        }
    )
```

### 中間件系統

**middleware.py - 中間件架構：**

```python
import time
import uuid
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.middleware.cors import CORSMiddleware
from logging_config import logger
from config import settings

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """請求日誌中間件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 生成請求ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # 記錄請求開始
        start_time = time.time()
        logger.info(f"[{request_id}] {request.method} {request.url}")
        
        try:
            response = await call_next(request)
            
            # 記錄請求結束
            process_time = time.time() - start_time
            logger.info(
                f"[{request_id}] {response.status_code} - {process_time:.3f}s"
            )
            
            # 添加響應頭
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as e:
            process_time = time.time() - start_time
            logger.error(
                f"[{request_id}] 請求處理失敗: {str(e)} - {process_time:.3f}s"
            )
            raise

class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全標頭中間件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # 添加安全標頭
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        if not settings.debug:
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        return response

class RateLimitMiddleware(BaseHTTPMiddleware):
    """速率限制中間件"""
    
    def __init__(self, app, calls: int = 100, period: int = 60):
        super().__init__(app)
        self.calls = calls
        self.period = period
        self.clients = {}
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        client_ip = request.client.host
        current_time = time.time()
        
        # 清理過期記錄
        self._cleanup_expired_records(current_time)
        
        # 檢查速率限制
        if client_ip not in self.clients:
            self.clients[client_ip] = []
        
        client_requests = self.clients[client_ip]
        
        # 移除過期請求
        client_requests[:] = [
            req_time for req_time in client_requests
            if current_time - req_time < self.period
        ]
        
        if len(client_requests) >= self.calls:
            logger.warning(f"速率限制觸發: {client_ip}")
            return Response(
                content="請求過於頻繁，請稍後再試",
                status_code=429
            )
        
        # 記錄當前請求
        client_requests.append(current_time)
        
        return await call_next(request)
    
    def _cleanup_expired_records(self, current_time: float):
        """清理過期記錄"""
        for client_ip in list(self.clients.keys()):
            self.clients[client_ip] = [
                req_time for req_time in self.clients[client_ip]
                if current_time - req_time < self.period
            ]
            
            if not self.clients[client_ip]:
                del self.clients[client_ip]

def setup_middleware(app):
    """設定中間件"""
    
    # CORS中間件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["http://localhost:8501", "http://127.0.0.1:8501"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 自定義中間件
    app.add_middleware(SecurityHeadersMiddleware)
    app.add_middleware(RateLimitMiddleware, calls=100, period=60)
    app.add_middleware(RequestLoggingMiddleware)
    
    return app
```

### 主應用程式整合

**main.py - FastAPI應用程式入口：**

```python
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from sqlalchemy.exc import SQLAlchemyError

# 導入模組
from database import db_config
from config import settings
from logging_config import logger
from middleware import setup_middleware
from exceptions import (
    validation_exception_handler,
    database_exception_handler,
    rental_exception_handler,
    general_exception_handler,
    RentalManagementException
)

# 導入路由
from routers import auth, rooms, residents, utilities, reports

# 創建FastAPI應用程式
app = FastAPI(
    title=settings.app_name,
    description="現代化租房管理系統",
    version=settings.app_version,
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None
)

# 設定中間件
app = setup_middleware(app)

# 註冊異常處理器
app.add_exception_handler(RequestValidationError, validation_exception_handler)
app.add_exception_handler(SQLAlchemyError, database_exception_handler)
app.add_exception_handler(RentalManagementException, rental_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)

# 註冊路由
app.include_router(auth.router)
app.include_router(rooms.router)
app.include_router(residents.router)
app.include_router(utilities.router)
app.include_router(reports.router)

@app.on_event("startup")
async def startup_event():
    """應用程式啟動事件"""
    logger.info(f"啟動 {settings.app_name} v{settings.app_version}")
    
    # 初始化資料庫
    try:
        db_config.create_tables()
        logger.info("資料庫初始化完成")
    except Exception as e:
        logger.error(f"資料庫初始化失敗: {str(e)}")
        raise
    
    # 創建預設管理員帳號
    try:
        from services import UserService
        with db_config.get_db_session() as db:
            admin_user = UserService.get_user_by_username(db, "admin")
            if not admin_user:
                UserService.create_user(
                    db, "admin", "admin123", "<EMAIL>", "admin"
                )
                logger.info("創建預設管理員帳號: admin/admin123")
    except Exception as e:
        logger.warning(f"創建預設管理員失敗: {str(e)}")

@app.on_event("shutdown")
async def shutdown_event():
    """應用程式關閉事件"""
    logger.info(f"關閉 {settings.app_name}")

@app.get("/")
async def root():
    """根路徑"""
    return {
        "message": f"歡迎使用 {settings.app_name}",
        "version": settings.app_version,
        "docs": "/docs" if settings.debug else "文檔已停用"
    }

@app.get("/health")
async def health_check():
    """健康檢查"""
    try:
        # 檢查資料庫連接
        with db_config.get_db_session() as db:
            db.execute("SELECT 1")
        
        return {
            "status": "healthy",
            "timestamp": "2024-01-01T00:00:00Z",
            "version": settings.app_version
        }
    except Exception as e:
        logger.error(f"健康檢查失敗: {str(e)}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e)
            }
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
```

這個深度實現方案提供了完整的企業級租房管理系統架構，涵蓋了前端UI組件、後端API服務、資料庫設計、認證授權、錯誤處理、日誌系統、中間件、配置管理等各個技術層面，確保系統具備高度的可維護性、擴展性和穩定性。