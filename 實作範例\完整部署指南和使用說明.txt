# 🏠 租房管理系統 - 完整部署指南

## 📋 目錄
1. [系統概述](#系統概述)
2. [技術架構](#技術架構)
3. [快速開始](#快速開始)
4. [詳細安裝](#詳細安裝)
5. [系統配置](#系統配置)
6. [功能使用指南](#功能使用指南)
7. [故障排除](#故障排除)
8. [API文檔](#api文檔)
9. [開發指南](#開發指南)
10. [維護與監控](#維護與監控)

---

## 🎯 系統概述

### 功能特點
- **🏠 房間管理**: 完整的房間資訊維護與狀態追蹤
- **👥 住戶管理**: 入住、退房、資料管理一站式服務
- **💰 費用管理**: 自動化電費水費計算與帳單生成
- **📊 統計報表**: 豐富的數據分析與可視化圖表
- **🔐 安全認證**: JWT令牌認證與多角色權限管理
- **📱 響應式設計**: 支援桌面端與移動端訪問

### 技術特色
- **🚀 現代化技術棧**: FastAPI + Streamlit + SQLite
- **⚡ 高性能**: 異步API設計，快速響應
- **🛠 易於部署**: 零配置資料庫，一鍵啟動
- **🔧 易於維護**: 模組化架構，代碼結構清晰
- **📈 擴展性強**: 支援水平擴展與功能擴展

---

## 🏗 技術架構

### 整體架構圖
```
┌─────────────────┐    HTTP/JSON    ┌─────────────────┐
│   Streamlit     │ ◄──────────────► │    FastAPI      │
│   前端界面      │                 │    後端API      │
│                 │                 │                 │
│ • 用戶界面      │                 │ • RESTful API   │
│ • 狀態管理      │                 │ • JWT認證       │
│ • 圖表展示      │                 │ • 業務邏輯      │
└─────────────────┘                 └─────────────────┘
                                             │
                                             │ SQLAlchemy ORM
                                             ▼
                                    ┌─────────────────┐
                                    │     SQLite      │
                                    │    資料庫       │
                                    │                 │
                                    │ • 用戶資料      │
                                    │ • 房間資料      │
                                    │ • 費用記錄      │
                                    └─────────────────┘
```

### 技術棧詳情

#### 後端技術
| 組件 | 技術 | 版本 | 用途 |
|------|------|------|------|
| 框架 | FastAPI | 0.104.1 | Web框架與API |
| ORM | SQLAlchemy | 2.0.23 | 資料庫操作 |
| 資料庫 | SQLite | 內建 | 數據存儲 |
| 認證 | python-jose | 3.3.0 | JWT處理 |
| 密碼 | passlib | 1.7.4 | 密碼加密 |
| 驗證 | Pydantic | 2.4.2 | 數據驗證 |
| 服務器 | Uvicorn | 0.24.0 | ASGI服務器 |

#### 前端技術
| 組件 | 技術 | 版本 | 用途 |
|------|------|------|------|
| 框架 | Streamlit | 1.28.1 | Web界面 |
| HTTP | Requests | 2.31.0 | API調用 |
| 數據 | Pandas | 2.1.1 | 數據處理 |
| 圖表 | Plotly | 5.17.0 | 數據可視化 |
| 配置 | python-dotenv | 1.0.0 | 環境配置 |

---

## 🚀 快速開始

### 方法一：一鍵啟動（推薦）
```bash
# 1. 下載系統
git clone <repository-url>
cd rental-management-system

# 2. 運行一鍵啟動
python start_rental_system.py
```

### 方法二：快速安裝
```bash
# 1. 運行安裝腳本
python install_system.py

# 2. 啟動系統
python start_rental_system.py
```

### 方法三：開發模式
```bash
# 快速開發啟動
python quick_start.py
```

### 🌐 訪問系統
安裝完成後，打開瀏覽器訪問：
- **前端界面**: http://localhost:8501
- **API文檔**: http://localhost:8000/docs
- **健康檢查**: http://localhost:8000/health

### 🔑 預設登入
- **用戶名**: `admin`
- **密碼**: `admin123`
- **角色**: 系統管理員

---

## 📦 詳細安裝

### 系統要求
- **作業系統**: Windows 10/11, macOS 10.14+, Ubuntu 18.04+
- **Python**: 3.9 或更高版本
- **記憶體**: 最少 2GB RAM
- **硬碟**: 最少 1GB 可用空間
- **網路**: 用於下載依賴套件

### 環境檢查
```bash
# 檢查Python版本
python --version

# 檢查pip
pip --version

# 檢查環境完整性
python check_environment.py
```

### 手動安裝步驟

#### 1. 克隆項目
```bash
git clone <repository-url>
cd rental-management-system
```

#### 2. 創建虛擬環境（可選但推薦）
```bash
# 創建虛擬環境
python -m venv rental_env

# 啟用虛擬環境
# Windows
rental_env\Scripts\activate
# Linux/macOS
source rental_env/bin/activate
```

#### 3. 安裝後端依賴
```bash
cd backend
pip install -r requirements.txt
```

#### 4. 安裝前端依賴
```bash
cd ../frontend
pip install -r requirements.txt
```

#### 5. 配置環境
```bash
cd ../backend
cp .env.example .env
# 編輯 .env 文件設定配置
```

#### 6. 初始化資料庫
```bash
python init_db.py
```

#### 7. 啟動服務
```bash
# 啟動後端（新終端）
cd backend
python run_server.py

# 啟動前端（新終端）
cd frontend
python run_frontend.py
```

---

## ⚙️ 系統配置

### 後端配置 (.env)
```env
# 資料庫設定
DATABASE_URL=sqlite:///./rental_management.db

# JWT設定
SECRET_KEY=your-super-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 業務設定
DEFAULT_ELECTRICITY_RATE=5.5
DEFAULT_WATER_FEE=200.0

# 系統設定
DEBUG=True
LOG_LEVEL=INFO
```

### 進階配置選項

#### 資料庫配置
```python
# 使用PostgreSQL（可選）
DATABASE_URL=postgresql://user:password@localhost:5432/rental_db

# 使用MySQL（可選）
DATABASE_URL=mysql://user:password@localhost:3306/rental_db
```

#### 安全配置
```env
# 強密碼策略
PASSWORD_MIN_LENGTH=8
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=15

# CORS設定
ALLOWED_ORIGINS=http://localhost:8501,http://127.0.0.1:8501
```

#### 業務配置
```env
# 預設費率
DEFAULT_ELECTRICITY_RATE=5.5
DEFAULT_WATER_FEE=200.0
DEFAULT_DEPOSIT_AMOUNT=10000

# 系統限制
MAX_OCCUPANTS_PER_ROOM=2
BILLING_DAY_OF_MONTH=1
```

---

## 📖 功能使用指南

### 1. 用戶管理

#### 登入系統
1. 打開 http://localhost:8501
2. 輸入用戶名和密碼
3. 點擊「登入」按鈕

#### 用戶角色
- **系統管理員（admin）**: 擁有所有權限
- **物業管理員（manager）**: 可管理房間和住戶
- **一般用戶（user）**: 僅可查看資訊

### 2. 房間管理

#### 新增房間
1. 進入「房間管理」頁面
2. 點擊「新增房間」頁籤
3. 填寫房間資訊：
   - 房間號（必填）
   - 樓層
   - 坪數
   - 單人租金（必填）
   - 雙人租金（必填）
   - 房間描述
4. 點擊「創建房間」

#### 管理房間
- **查看房間列表**: 顯示所有房間及狀態
- **搜尋房間**: 使用房間號快速搜尋
- **篩選房間**: 按狀態篩選（可用/已滿/維護中）
- **編輯房間**: 修改房間資訊
- **查看統計**: 檢視房間使用率和收益分析

### 3. 住戶管理

#### 住戶入住
1. 進入「住戶管理」頁面
2. 點擊「新增住戶」頁籤
3. 填寫住戶資料：
   - 姓名（必填）
   - 身份證號（必填）
   - 聯絡電話
   - 緊急聯絡人資訊
   - 選擇房間（必填）
   - 入住日期（必填）
   - 押金金額
4. 點擊「新增住戶」

#### 住戶退房
1. 在住戶列表中選擇住戶
2. 點擊「辦理退房」
3. 選擇退房日期
4. 確認退房

#### 住戶資訊管理
- **查看住戶列表**: 顯示所有住戶資訊
- **搜尋住戶**: 按姓名、身份證或電話搜尋
- **編輯資料**: 修改住戶聯絡資訊
- **查看統計**: 住戶流動分析

### 4. 費用管理

#### 費率設定
1. 進入「費用管理」頁面
2. 點擊「費率設定」頁籤
3. 設定費率：
   - 電費每度價格
   - 月度水費
   - 生效日期
   - 備註說明
4. 點擊「設定新費率」

#### 電表抄錄
1. 選擇抄錄年月
2. 為每個有住戶的房間輸入電表讀數
3. 系統自動計算用電量和費用
4. 批量提交抄錄記錄

#### 帳單管理
- **查看帳單**: 按月份查看所有帳單
- **付款管理**: 更新付款狀態
- **費用統計**: 收入分析和趨勢圖表
- **匯出資料**: 下載Excel或CSV格式報表

### 5. 報表統計

#### 營收報表
- 月度營收概覽
- 收入構成分析（電費/水費）
- 收款狀況統計
- 房間費用排行

#### 住戶分析
- 住戶基本統計
- 分布分析
- 流動趨勢
- 押金統計

#### 房間分析
- 使用率分析
- 租金分布
- 收益分析
- 狀態統計

#### 趨勢分析
- 收入趨勢圖
- 入住率變化
- 住戶流動趨勢
- 自定義時間範圍分析

---

## 🔧 故障排除

### 常見問題

#### 1. 系統無法啟動
**問題**: 運行 `python start_rental_system.py` 後出現錯誤

**解決方案**:
```bash
# 檢查Python版本
python --version  # 需要3.9+

# 檢查依賴
python check_environment.py

# 重新安裝依賴
pip install -r backend/requirements.txt
pip install -r frontend/requirements.txt
```

#### 2. 端口被占用
**問題**: 提示端口8000或8501已被使用

**解決方案**:
```bash
# 檢查端口使用情況
# Windows
netstat -an | findstr :8000
netstat -an | findstr :8501

# Linux/macOS
lsof -i :8000
lsof -i :8501

# 終止占用端口的進程
# Windows
taskkill /PID <PID> /F

# Linux/macOS
kill -9 <PID>
```

#### 3. 資料庫錯誤
**問題**: 資料庫連接失敗或數據異常

**解決方案**:
```bash
# 重新初始化資料庫
cd backend
rm rental_management.db  # 刪除舊資料庫
python init_db.py         # 重新初始化
```

#### 4. 登入失敗
**問題**: 無法使用預設帳號登入

**解決方案**:
```bash
# 重置管理員帳號
cd backend
python -c "
from app.database import get_db_session
from app.services import UserService
db = next(get_db_session())
admin = UserService.get_user_by_username(db, 'admin')
if admin: db.delete(admin)
UserService.create_user(db, 'admin', 'admin123', '<EMAIL>', 'admin')
print('管理員帳號已重置')
"
```

#### 5. 前端頁面空白
**問題**: 前端界面載入後顯示空白頁面

**解決方案**:
```bash
# 檢查後端API連接
curl http://localhost:8000/health

# 檢查瀏覽器控制台錯誤
# F12 -> Console 查看錯誤訊息

# 清除瀏覽器緩存
# Ctrl+Shift+Delete
```

### 日誌分析

#### 後端日誌位置
- 控制台輸出：直接顯示在命令行
- 日誌文件：`backend/rental_management.log`（如果配置）

#### 前端日誌位置
- Streamlit日誌：前端啟動的命令行窗口
- 瀏覽器控制台：F12 -> Console

#### 關鍵日誌關鍵字
- `ERROR`: 系統錯誤
- `WARNING`: 警告訊息
- `INFO`: 一般資訊
- `DEBUG`: 調試資訊

---

## 📚 API文檔

### API概覽
系統提供完整的RESTful API，支持所有核心功能操作。

#### 基本資訊
- **Base URL**: `http://localhost:8000`
- **認證方式**: JWT Bearer Token
- **數據格式**: JSON
- **文檔地址**: http://localhost:8000/docs

### 認證API

#### 用戶登入
```http
POST /auth/login
Content-Type: application/x-www-form-urlencoded

username=admin&password=admin123
```

**回應**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer"
}
```

#### 獲取當前用戶
```http
GET /auth/me
Authorization: Bearer <token>
```

### 房間API

#### 獲取房間列表
```http
GET /rooms/
Authorization: Bearer <token>
```

#### 創建房間
```http
POST /rooms/
Authorization: Bearer <token>
Content-Type: application/json

{
  "room_number": "A101",
  "floor": 1,
  "area": 20.5,
  "rent_single": 10000,
  "rent_double": 15000,
  "description": "舒適雅房"
}
```

### 住戶API

#### 創建住戶
```http
POST /residents/
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "張三",
  "id_number": "A123456789",
  "phone": "0912345678",
  "room_id": 1,
  "move_in_date": "2024-01-01",
  "deposit": 10000
}
```

#### 住戶退房
```http
POST /residents/{resident_id}/move-out
Authorization: Bearer <token>
Content-Type: application/json

{
  "move_out_date": "2024-12-31"
}
```

### 費用API

#### 創建費率
```http
POST /utilities/rates
Authorization: Bearer <token>
Content-Type: application/json

{
  "electricity_rate": 5.5,
  "monthly_water_fee": 200,
  "effective_date": "2024-01-01",
  "notes": "新年度費率"
}
```

#### 電表抄錄
```http
POST /utilities/readings
Authorization: Bearer <token>
Content-Type: application/json

{
  "room_id": 1,
  "billing_year": 2024,
  "billing_month": 1,
  "current_electricity_reading": 1000.5
}
```

### 報表API

#### 獲取儀表板統計
```http
GET /reports/dashboard
Authorization: Bearer <token>
```

#### 獲取收入統計
```http
GET /reports/income-summary?year=2024&month=1
Authorization: Bearer <token>
```

---

## 👨‍💻 開發指南

### 開發環境設置

#### 1. Fork項目
```bash
git clone <your-fork-url>
cd rental-management-system
git remote add upstream <original-repo-url>
```

#### 2. 創建開發分支
```bash
git checkout -b feature/your-feature-name
```

#### 3. 安裝開發依賴
```bash
pip install -r backend/requirements.txt
pip install -r frontend/requirements.txt
pip install pytest pytest-cov black pylint
```

### 程式碼結構

#### 後端結構
```
backend/
├── app/
│   ├── __init__.py
│   ├── main.py          # FastAPI應用主程式
│   ├── config.py        # 配置管理
│   ├── database.py      # 資料庫連接
│   ├── models.py        # 資料模型
│   ├── auth.py          # 認證系統
│   ├── services.py      # 業務邏輯
│   └── routers/         # API路由
│       ├── auth.py
│       ├── rooms.py
│       ├── residents.py
│       ├── utilities.py
│       └── reports.py
├── tests/               # 測試文件
├── requirements.txt     # 依賴列表
├── init_db.py          # 資料庫初始化
└── run_server.py       # 服務器啟動
```

#### 前端結構
```
frontend/
├── app.py              # 主應用程式
├── config.py           # 前端配置
├── api_client.py       # API客戶端
├── utils.py            # 工具函數
├── pages/              # 頁面組件
│   ├── login.py
│   ├── dashboard.py
│   ├── rooms.py
│   ├── residents.py
│   ├── utilities.py
│   └── reports.py
├── components/         # UI組件
│   ├── forms.py
│   ├── tables.py
│   ├── charts.py
│   └── navigation.py
└── requirements.txt    # 前端依賴
```

### 開發規範

#### 程式碼風格
- 使用Python PEP 8標準
- 函數和變數使用snake_case
- 類名使用PascalCase
- 常數使用UPPER_CASE

#### 提交規範
```bash
# 功能添加
git commit -m "feat: 添加住戶退房功能"

# 問題修復
git commit -m "fix: 修復登入驗證問題"

# 文檔更新
git commit -m "docs: 更新API文檔"

# 重構
git commit -m "refactor: 重構房間管理服務"
```

### 測試

#### 運行測試
```bash
# 後端測試
cd backend
python -m pytest tests/ -v --cov=app

# 前端功能測試
cd frontend
streamlit run app.py --server.headless=true
```

#### 測試覆蓋率
```bash
# 生成覆蓋率報告
python -m pytest tests/ --cov=app --cov-report=html
```

### 新功能開發

#### 1. 添加新的API端點
1. 在`models.py`中定義數據模型
2. 在`services.py`中實現業務邏輯
3. 在`routers/`中創建API路由
4. 在`tests/`中添加測試
5. 更新API文檔

#### 2. 添加新的前端頁面
1. 在`pages/`中創建頁面文件
2. 在`app.py`中添加路由
3. 在`api_client.py`中添加API調用
4. 測試頁面功能

---

## 🔍 維護與監控

### 日常維護

#### 備份管理
```bash
# 創建備份
python scripts/backup.py create

# 列出備份
python scripts/backup.py list

# 恢復備份
python scripts/backup.py restore --backup backup_20241201_120000.db

# 清理舊備份
python scripts/backup.py clean --keep-days 30
```

#### 健康檢查
```bash
# 運行系統健康檢查
python scripts/health_check.py
```

#### 日誌管理
```bash
# 查看系統日誌
tail -f backend/rental_management.log

# 清理舊日誌
find . -name "*.log" -mtime +30 -delete
```

### 性能監控

#### 系統資源監控
- CPU使用率
- 記憶體使用量
- 硬碟空間
- 網路流量

#### 應用程式監控
- API響應時間
- 錯誤率
- 並發用戶數
- 資料庫查詢性能

### 安全維護

#### 定期安全檢查
```bash
# 更新依賴套件
pip list --outdated
pip install --upgrade package_name

# 檢查安全漏洞
pip install safety
safety check
```

#### 訪問控制
- 定期更換JWT秘鑰
- 檢查用戶權限
- 監控異常登入
- 定期密碼策略檢查

---

## 📞 技術支援

### 聯絡方式
- **電子郵件**: <EMAIL>
- **問題回報**: GitHub Issues
- **功能建議**: GitHub Discussions

### 社群資源
- **官方文檔**: https://docs.rental-system.com
- **視頻教程**: https://youtube.com/rental-system
- **用戶論壇**: https://community.rental-system.com

### 常用連結
- **項目主頁**: https://github.com/rental-system
- **發布說明**: https://github.com/rental-system/releases
- **路線圖**: https://github.com/rental-system/roadmap

---

## 📄 授權資訊

本項目採用 MIT 授權條款，詳見 [LICENSE](LICENSE) 文件。

### 第三方套件授權
- FastAPI: MIT License
- Streamlit: Apache 2.0 License
- SQLAlchemy: MIT License
- Plotly: MIT License

---

## 🚀 版本歷史

### v1.0.0 (2024-01-01)
- ✨ 初始版本發布
- 🏠 完整的房間管理功能
- 👥 住戶管理系統
- 💰 費用計算與帳單管理
- 📊 統計報表功能
- 🔐 JWT認證系統
- 📱 響應式UI設計

### 未來規劃
- 📱 移動端應用
- 🔔 消息推送系統
- 📧 郵件通知功能
- 💳 線上付款整合
- 📋 合約管理
- 🏢 多物業支援

---

**感謝使用租房管理系統！** 🏠✨