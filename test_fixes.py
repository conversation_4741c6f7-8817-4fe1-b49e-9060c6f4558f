#!/usr/bin/env python3
"""測試修正後的功能"""

import requests
import json

def test_room_area_validation():
    """測試房間面積驗證修正"""
    base_url = 'http://localhost:8080'
    login_data = {
        'username': 'admin',
        'password': 'admin5813'
    }

    try:
        # 1. 登入獲取 token
        print('🔐 登入系統...')
        response = requests.post(f'{base_url}/auth/login', data=login_data)
        
        if response.status_code != 200:
            print(f'❌ 登入失敗: {response.status_code}')
            return
            
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        print('✅ 登入成功')
        
        # 2. 測試創建面積為 0 的房間
        print('\n🏠 測試創建面積為 0 的房間...')
        room_data = {
            "room_number": "TEST_AREA_0",
            "floor": 1,
            "area": 0.0,
            "rent_single": 8000,
            "rent_double": 12000,
            "description": "測試面積為0的房間"
        }
        
        create_response = requests.post(f'{base_url}/rooms/', json=room_data, headers=headers)
        print(f'創建響應狀態碼: {create_response.status_code}')
        
        if create_response.status_code == 200:
            print('✅ 成功創建面積為 0 的房間')
            created_room = create_response.json()
            room_id = created_room['id']
            print(f'房間 ID: {room_id}')
            
            # 3. 測試更新房間面積為 0
            print('\n🔄 測試更新房間面積為 0...')
            update_data = {
                "area": 0.0,
                "description": "更新後面積為0的房間"
            }
            
            update_response = requests.put(f'{base_url}/rooms/{room_id}', json=update_data, headers=headers)
            print(f'更新響應狀態碼: {update_response.status_code}')
            
            if update_response.status_code == 200:
                print('✅ 成功更新房間面積為 0')
            else:
                print(f'❌ 更新房間失敗: {update_response.status_code}')
                print(f'錯誤詳情: {update_response.text}')
            
            # 4. 清理：刪除測試房間
            print('\n🗑️ 清理測試房間...')
            delete_response = requests.delete(f'{base_url}/rooms/{room_id}', headers=headers)
            if delete_response.status_code == 200:
                print('✅ 測試房間已清理')
            else:
                print(f'⚠️  清理測試房間失敗: {delete_response.status_code}')
                
        else:
            print(f'❌ 創建房間失敗: {create_response.status_code}')
            print(f'錯誤詳情: {create_response.text}')
            
        # 5. 測試負數面積（應該失敗）
        print('\n🚫 測試創建負數面積的房間（應該失敗）...')
        negative_room_data = {
            "room_number": "TEST_NEGATIVE",
            "floor": 1,
            "area": -1.0,
            "rent_single": 8000,
            "rent_double": 12000,
            "description": "測試負數面積的房間"
        }
        
        negative_response = requests.post(f'{base_url}/rooms/', json=negative_room_data, headers=headers)
        print(f'負數面積創建響應狀態碼: {negative_response.status_code}')
        
        if negative_response.status_code == 422:
            print('✅ 正確阻止創建負數面積的房間')
            error_detail = negative_response.json()
            print(f'錯誤訊息: {error_detail}')
        else:
            print(f'⚠️  預期狀態碼 422，實際得到 {negative_response.status_code}')
            
    except Exception as e:
        print(f'❌ 測試過程中發生錯誤: {e}')

def test_date_utils():
    """測試日期工具修正"""
    print('\n📅 測試民國年日期工具修正...')
    
    try:
        # 導入修正後的日期工具
        import sys
        sys.path.append('frontend')
        from date_utils import roc_date_input, format_date_roc
        from datetime import date
        
        # 測試格式化功能
        test_date = date(2025, 7, 20)
        formatted = format_date_roc(test_date, 'full')
        print(f'✅ 日期格式化功能正常: {formatted}')
        
        print('✅ 民國年格式提示文字已移除（需要在前端界面中驗證）')
        
    except Exception as e:
        print(f'❌ 日期工具測試錯誤: {e}')

if __name__ == "__main__":
    print("🧪 測試修正後的功能")
    print("=" * 60)
    
    # 測試房間面積驗證修正
    test_room_area_validation()
    
    # 測試日期工具修正
    test_date_utils()
    
    print("\n" + "=" * 60)
    print("🏁 測試完成")
    print("\n📝 修正總結:")
    print("1. ✅ 移除民國年格式提示文字")
    print("2. ✅ 修正房間面積驗證（允許面積為 0）")
    print("3. ✅ 修正房間刪除後的編輯狀態清理")
    print("\n💡 建議:")
    print("- 重新啟動後端服務以載入面積驗證修正")
    print("- 在前端界面中驗證民國年提示文字是否已移除")
    print("- 測試房間刪除後編輯狀態是否正確結束")
