from sqlalchemy import Column, Integer, String, DateTime, <PERSON><PERSON><PERSON>, Float, ForeignKey, Text
from sqlalchemy.orm import relationship
from datetime import datetime
from .database import Base

class User(Base):
    """用戶模型"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    email = Column(String(100), unique=True, nullable=True)
    role = Column(String(20), default="user")  # admin, manager, user
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    last_login = Column(DateTime, nullable=True)
    
    def to_dict(self):
        return {
            "id": self.id,
            "username": self.username,
            "email": self.email,
            "role": self.role,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "last_login": self.last_login.isoformat() if self.last_login else None
        }

class Room(Base):
    """房間模型"""
    __tablename__ = "rooms"
    
    id = Column(Integer, primary_key=True, index=True)
    room_number = Column(String(20), unique=True, index=True, nullable=False)
    floor = Column(Integer, nullable=True)
    area = Column(Float, nullable=True)
    rent_single = Column(Float, nullable=False)
    rent_double = Column(Float, nullable=False)
    current_occupants = Column(Integer, default=0)
    max_occupants = Column(Integer, default=2)
    status = Column(String(20), default="available")  # available, occupied, partial, maintenance
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 關聯關係
    residents = relationship("Resident", back_populates="room")
    utility_records = relationship("UtilityRecord", back_populates="room")
    rent_records = relationship("RentRecord", back_populates="room")
    comprehensive_bills = relationship("ComprehensiveBill", back_populates="room")
    
    @property
    def current_rent(self):
        current_occupants = self.current_occupants if self.current_occupants is not None else 0
        return self.rent_double if current_occupants == 2 else self.rent_single
    
    def to_dict(self):
        return {
            "id": self.id,
            "room_number": self.room_number,
            "floor": self.floor,
            "area": round(self.area, 2) if self.area is not None else 0.0,
            "rent_single": round(self.rent_single, 2),
            "rent_double": round(self.rent_double, 2),
            "current_occupants": self.current_occupants if self.current_occupants is not None else 0,
            "max_occupants": self.max_occupants if self.max_occupants is not None else 2,
            "status": self.status,
            "description": self.description,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "current_rent": round(self.current_rent, 2)
        }

class Resident(Base):
    """住戶模型"""
    __tablename__ = "residents"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    phone = Column(String(20), nullable=True)
    id_number = Column(String(20), unique=True, nullable=False)
    emergency_contact = Column(String(100), nullable=True)
    emergency_phone = Column(String(20), nullable=True)
    room_id = Column(Integer, ForeignKey("rooms.id"), nullable=False)
    move_in_date = Column(DateTime, nullable=False)
    move_out_date = Column(DateTime, nullable=True)
    lease_end_date = Column(DateTime, nullable=True)  # 租約到期日
    deposit = Column(Float, default=0.0)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 關聯關係
    room = relationship("Room", back_populates="residents")
    rent_records = relationship("RentRecord", back_populates="resident")
    
    @property
    def is_current_resident(self):
        return self.is_active and self.move_out_date is None
    
    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "phone": self.phone,
            "id_number": self.id_number,
            "emergency_contact": self.emergency_contact,
            "emergency_phone": self.emergency_phone,
            "room_id": self.room_id,
            "move_in_date": self.move_in_date.isoformat() if self.move_in_date else None,
            "move_out_date": self.move_out_date.isoformat() if self.move_out_date else None,
            "lease_end_date": self.lease_end_date.isoformat() if self.lease_end_date else None,
            "deposit": self.deposit,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "room": self.room.to_dict() if self.room else None
        }

class UtilityRate(Base):
    """公用事業費率模型"""
    __tablename__ = "utility_rates"

    id = Column(Integer, primary_key=True, index=True)
    electricity_rate = Column(Float, nullable=False)
    monthly_water_fee = Column(Float, nullable=False)
    effective_date = Column(DateTime, nullable=False)
    notes = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            "id": self.id,
            "electricity_rate": round(self.electricity_rate, 2),
            "monthly_water_fee": round(self.monthly_water_fee, 2),
            "effective_date": self.effective_date.isoformat() if self.effective_date else None,
            "notes": self.notes,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

class UtilityRecord(Base):
    """公用事業使用記錄模型"""
    __tablename__ = "utility_records"

    id = Column(Integer, primary_key=True, index=True)
    room_id = Column(Integer, ForeignKey("rooms.id"), nullable=False)
    billing_year = Column(Integer, nullable=False)
    billing_month = Column(Integer, nullable=False)
    previous_electricity_reading = Column(Float, default=0.0)
    current_electricity_reading = Column(Float, nullable=False)
    electricity_usage = Column(Float, nullable=False)
    electricity_rate = Column(Float, nullable=False)
    electricity_cost = Column(Float, nullable=False)
    water_fee = Column(Float, nullable=False)
    total_amount = Column(Float, nullable=False)
    payment_status = Column(String(20), default="pending")  # pending, paid, overdue
    payment_date = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # 關聯關係
    room = relationship("Room", back_populates="utility_records")

    def to_dict(self):
        return {
            "id": self.id,
            "room_id": self.room_id,
            "billing_year": self.billing_year,
            "billing_month": self.billing_month,
            "previous_electricity_reading": round(self.previous_electricity_reading, 2),
            "current_electricity_reading": round(self.current_electricity_reading, 2),
            "electricity_usage": round(self.electricity_usage, 2),
            "electricity_rate": round(self.electricity_rate, 2),
            "electricity_cost": round(self.electricity_cost, 2),
            "water_fee": round(self.water_fee, 2),
            "total_amount": round(self.total_amount, 2),
            "payment_status": self.payment_status,
            "payment_date": self.payment_date.isoformat() if self.payment_date else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "room": self.room.to_dict() if self.room else None
        }

class RentRecord(Base):
    """租金記錄模型"""
    __tablename__ = "rent_records"

    id = Column(Integer, primary_key=True, index=True)
    room_id = Column(Integer, ForeignKey("rooms.id"), nullable=False)
    resident_id = Column(Integer, ForeignKey("residents.id"), nullable=False)
    rent_year = Column(Integer, nullable=False)
    rent_month = Column(Integer, nullable=False)
    rent_amount = Column(Float, nullable=False)
    payment_status = Column(String(20), default="待付款")  # 待付款, 已付款, 逾期
    payment_date = Column(DateTime, nullable=True)
    due_date = Column(DateTime, nullable=False)
    notes = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # 關聯
    room = relationship("Room", back_populates="rent_records")
    resident = relationship("Resident", back_populates="rent_records")

    def to_dict(self):
        return {
            "id": self.id,
            "room_id": self.room_id,
            "resident_id": self.resident_id,
            "rent_year": self.rent_year,
            "rent_month": self.rent_month,
            "rent_amount": round(self.rent_amount, 2),
            "payment_status": self.payment_status,
            "payment_date": self.payment_date.isoformat() if self.payment_date else None,
            "due_date": self.due_date.isoformat() if self.due_date else None,
            "notes": self.notes,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "room": self.room.to_dict() if self.room else None,
            "resident": self.resident.to_dict() if self.resident else None
        }

class ComprehensiveBill(Base):
    """綜合帳單模型"""
    __tablename__ = "comprehensive_bills"

    id = Column(Integer, primary_key=True, index=True)
    room_id = Column(Integer, ForeignKey("rooms.id"), nullable=False)
    billing_year = Column(Integer, nullable=False)
    billing_month = Column(Integer, nullable=False)

    # 租金相關
    rent_amount = Column(Float, nullable=False)
    occupant_count = Column(Integer, nullable=False)  # 計費時的住戶數量

    # 水費相關（固定100元）
    water_fee = Column(Float, default=100.0, nullable=False)

    # 電費相關
    electricity_usage = Column(Float, nullable=False)
    electricity_rate = Column(Float, nullable=False)
    electricity_cost = Column(Float, nullable=False)
    previous_electricity_reading = Column(Float, default=0.0)
    current_electricity_reading = Column(Float, nullable=False)

    # 總費用
    total_amount = Column(Float, nullable=False)

    # 付款狀態
    payment_status = Column(String(20), default="pending")  # pending, paid, overdue
    payment_date = Column(DateTime, nullable=True)
    due_date = Column(DateTime, nullable=False)

    # 備註
    notes = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 關聯
    room = relationship("Room", back_populates="comprehensive_bills")

    def to_dict(self):
        return {
            "id": self.id,
            "room_id": self.room_id,
            "billing_year": self.billing_year,
            "billing_month": self.billing_month,
            "rent_amount": round(self.rent_amount, 2),
            "occupant_count": self.occupant_count,
            "water_fee": round(self.water_fee, 2),
            "electricity_usage": round(self.electricity_usage, 2),
            "electricity_rate": round(self.electricity_rate, 2),
            "electricity_cost": round(self.electricity_cost, 2),
            "previous_electricity_reading": round(self.previous_electricity_reading, 2),
            "current_electricity_reading": round(self.current_electricity_reading, 2),
            "total_amount": round(self.total_amount, 2),
            "payment_status": self.payment_status,
            "payment_date": self.payment_date.isoformat() if self.payment_date else None,
            "due_date": self.due_date.isoformat() if self.due_date else None,
            "notes": self.notes,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "room": self.room.to_dict() if self.room else None
        }
