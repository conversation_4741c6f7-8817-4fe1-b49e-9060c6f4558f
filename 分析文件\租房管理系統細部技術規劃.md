# 租房管理系統細部技術規劃

## 系統架構深度分析框架

### 核心技術組件解構

**多層次架構設計原則：**
```python
# 系統架構認知模型
架構思維框架
├── 資料持久層 (SQLite + SQLAlchemy)
│   ├── 實體關係映射策略
│   ├── 資料完整性保障機制
│   └── 查詢優化適應性設計
├── 業務邏輯層 (FastAPI Services)
│   ├── 領域驅動設計實現
│   ├── 事務處理自動化
│   └── 業務規則驗證引擎
├── API介面層 (FastAPI Routers)
│   ├── RESTful設計標準
│   ├── 自動文檔生成機制
│   └── 類型安全驗證系統
├── 認證授權層 (JWT Integration)
│   ├── 無狀態認證設計
│   ├── 角色權限控制機制
│   └── 安全性強化策略
└── 展示互動層 (Streamlit Components)
    ├── 響應式用戶介面
    ├── 狀態管理最佳化
    └── 用戶體驗流程設計
```

## 資料庫架構精密設計

### 實體關係模型深度規劃

**SQLAlchemy模型架構系統：**

```python
# models.py - 資料模型完整定義
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Float, ForeignKey, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
from typing import Optional

Base = declarative_base()

class BaseModel(Base):
    """基礎模型 - 提供通用欄位和方法"""
    __abstract__ = True
    
    id = Column(Integer, primary_key=True, index=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    
    def to_dict(self):
        """模型轉換為字典"""
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}

class User(BaseModel):
    """用戶認證模型"""
    __tablename__ = "users"
    
    username = Column(String(50), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    email = Column(String(100), unique=True, nullable=True)
    role = Column(String(20), default="user")  # admin, manager, user
    last_login = Column(DateTime, nullable=True)
    
    def __repr__(self):
        return f"<User(username='{self.username}', role='{self.role}')>"

class Room(BaseModel):
    """房間管理模型"""
    __tablename__ = "rooms"
    
    room_number = Column(String(20), unique=True, nullable=False, index=True)
    floor = Column(Integer, nullable=True)
    area = Column(Float, nullable=True)  # 坪數
    rent_single = Column(Float, nullable=False)  # 單人租金
    rent_double = Column(Float, nullable=False)  # 雙人租金
    current_occupants = Column(Integer, default=0)
    max_occupants = Column(Integer, default=2)
    status = Column(String(20), default="available")  # available, occupied, maintenance
    description = Column(Text, nullable=True)
    
    # 關聯關係
    residents = relationship("Resident", back_populates="room")
    utility_records = relationship("UtilityRecord", back_populates="room")
    
    @property
    def current_rent(self):
        """根據住戶數量返回當前租金"""
        return self.rent_double if self.current_occupants == 2 else self.rent_single
    
    def __repr__(self):
        return f"<Room(number='{self.room_number}', occupants={self.current_occupants})>"

class Resident(BaseModel):
    """住戶管理模型"""
    __tablename__ = "residents"
    
    name = Column(String(100), nullable=False)
    phone = Column(String(20), nullable=True)
    id_number = Column(String(20), unique=True, nullable=False)
    emergency_contact = Column(String(100), nullable=True)
    emergency_phone = Column(String(20), nullable=True)
    
    # 住宿資訊
    room_id = Column(Integer, ForeignKey("rooms.id"), nullable=False)
    move_in_date = Column(DateTime, nullable=False)
    move_out_date = Column(DateTime, nullable=True)
    deposit = Column(Float, default=0.0)
    
    # 關聯關係
    room = relationship("Room", back_populates="residents")
    
    @property
    def is_current_resident(self):
        """判斷是否為現住戶"""
        return self.is_active and self.move_out_date is None
    
    def __repr__(self):
        return f"<Resident(name='{self.name}', room='{self.room.room_number if self.room else None}')>"

class UtilityRate(BaseModel):
    """公用事業費率設定模型"""
    __tablename__ = "utility_rates"
    
    electricity_rate = Column(Float, nullable=False)  # 每度電費
    monthly_water_fee = Column(Float, nullable=False)  # 月度水費
    effective_date = Column(DateTime, nullable=False)
    notes = Column(Text, nullable=True)
    
    def __repr__(self):
        return f"<UtilityRate(electricity={self.electricity_rate}, water={self.monthly_water_fee})>"

class UtilityRecord(BaseModel):
    """公用事業使用記錄模型"""
    __tablename__ = "utility_records"
    
    room_id = Column(Integer, ForeignKey("rooms.id"), nullable=False)
    billing_year = Column(Integer, nullable=False)
    billing_month = Column(Integer, nullable=False)
    
    # 電費相關
    previous_electricity_reading = Column(Float, default=0.0)
    current_electricity_reading = Column(Float, nullable=False)
    electricity_usage = Column(Float, nullable=False)
    electricity_rate = Column(Float, nullable=False)
    electricity_cost = Column(Float, nullable=False)
    
    # 水費相關
    water_fee = Column(Float, nullable=False)
    
    # 總費用
    total_amount = Column(Float, nullable=False)
    payment_status = Column(String(20), default="pending")  # pending, paid, overdue
    payment_date = Column(DateTime, nullable=True)
    
    # 關聯關係
    room = relationship("Room", back_populates="utility_records")
    
    def __repr__(self):
        return f"<UtilityRecord(room={self.room.room_number}, {self.billing_year}-{self.billing_month:02d})>"
```

### 資料庫配置與連接管理

**database.py - 資料庫連接策略：**

```python
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from contextlib import contextmanager
import os

class DatabaseConfig:
    """資料庫配置管理"""
    
    def __init__(self):
        self.database_url = os.getenv("DATABASE_URL", "sqlite:///./rental_management.db")
        self.engine = create_engine(
            self.database_url,
            connect_args={"check_same_thread": False},  # SQLite專用
            pool_pre_ping=True,
            echo=False  # 生產環境設為False
        )
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
    
    def create_tables(self):
        """建立資料庫表格"""
        from models import Base
        Base.metadata.create_all(bind=self.engine)
    
    @contextmanager
    def get_db_session(self):
        """資料庫會話管理"""
        db = self.SessionLocal()
        try:
            yield db
            db.commit()
        except Exception as e:
            db.rollback()
            raise e
        finally:
            db.close()
    
    def get_db(self):
        """FastAPI依賴注入用"""
        db = self.SessionLocal()
        try:
            yield db
        finally:
            db.close()

# 全域資料庫實例
db_config = DatabaseConfig()
```

## FastAPI後端架構實現

### 核心服務邏輯設計

**services.py - 業務邏輯層：**

```python
from typing import List, Optional, Dict
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from models import User, Room, Resident, UtilityRate, UtilityRecord
from auth import get_password_hash, verify_password
import calendar

class UserService:
    """用戶管理服務"""
    
    @staticmethod
    def create_user(db: Session, username: str, password: str, email: str = None, role: str = "user"):
        """創建新用戶"""
        password_hash = get_password_hash(password)
        user = User(
            username=username,
            password_hash=password_hash,
            email=email,
            role=role
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        return user
    
    @staticmethod
    def authenticate_user(db: Session, username: str, password: str):
        """用戶認證"""
        user = db.query(User).filter(User.username == username).first()
        if not user or not verify_password(password, user.password_hash):
            return None
        
        # 更新最後登入時間
        user.last_login = datetime.utcnow()
        db.commit()
        return user
    
    @staticmethod
    def get_user_by_username(db: Session, username: str):
        """根據用戶名獲取用戶"""
        return db.query(User).filter(User.username == username).first()

class RoomService:
    """房間管理服務"""
    
    @staticmethod
    def create_room(db: Session, room_data: Dict):
        """創建新房間"""
        room = Room(**room_data)
        db.add(room)
        db.commit()
        db.refresh(room)
        return room
    
    @staticmethod
    def get_all_rooms(db: Session, include_inactive: bool = False):
        """獲取所有房間"""
        query = db.query(Room)
        if not include_inactive:
            query = query.filter(Room.is_active == True)
        return query.all()
    
    @staticmethod
    def update_room_occupancy(db: Session, room_id: int):
        """更新房間住戶數量"""
        room = db.query(Room).filter(Room.id == room_id).first()
        if room:
            active_residents = db.query(Resident).filter(
                Resident.room_id == room_id,
                Resident.is_active == True,
                Resident.move_out_date.is_(None)
            ).count()
            
            room.current_occupants = active_residents
            
            # 更新房間狀態
            if active_residents == 0:
                room.status = "available"
            elif active_residents == room.max_occupants:
                room.status = "occupied"
            else:
                room.status = "partial"
            
            db.commit()
            return room
        return None
    
    @staticmethod
    def get_available_rooms(db: Session):
        """獲取可用房間"""
        return db.query(Room).filter(
            Room.is_active == True,
            Room.current_occupants < Room.max_occupants
        ).all()

class ResidentService:
    """住戶管理服務"""
    
    @staticmethod
    def create_resident(db: Session, resident_data: Dict):
        """創建新住戶"""
        resident = Resident(**resident_data)
        db.add(resident)
        db.commit()
        db.refresh(resident)
        
        # 更新房間住戶數量
        RoomService.update_room_occupancy(db, resident.room_id)
        return resident
    
    @staticmethod
    def move_out_resident(db: Session, resident_id: int, move_out_date: datetime):
        """住戶退房"""
        resident = db.query(Resident).filter(Resident.id == resident_id).first()
        if resident:
            resident.move_out_date = move_out_date
            resident.is_active = False
            db.commit()
            
            # 更新房間住戶數量
            RoomService.update_room_occupancy(db, resident.room_id)
            return resident
        return None
    
    @staticmethod
    def get_active_residents(db: Session, room_id: int = None):
        """獲取活躍住戶"""
        query = db.query(Resident).filter(
            Resident.is_active == True,
            Resident.move_out_date.is_(None)
        )
        
        if room_id:
            query = query.filter(Resident.room_id == room_id)
        
        return query.all()

class UtilityService:
    """公用事業服務"""
    
    @staticmethod
    def create_utility_rate(db: Session, rate_data: Dict):
        """創建新費率"""
        rate = UtilityRate(**rate_data)
        db.add(rate)
        db.commit()
        db.refresh(rate)
        return rate
    
    @staticmethod
    def get_current_utility_rate(db: Session):
        """獲取當前費率"""
        return db.query(UtilityRate).filter(
            UtilityRate.is_active == True
        ).order_by(UtilityRate.effective_date.desc()).first()
    
    @staticmethod
    def calculate_monthly_bill(db: Session, room_id: int, year: int, month: int, 
                             current_reading: float, previous_reading: float = None):
        """計算月度帳單"""
        room = db.query(Room).filter(Room.id == room_id).first()
        if not room:
            raise ValueError("房間不存在")
        
        # 獲取當前費率
        rate = UtilityService.get_current_utility_rate(db)
        if not rate:
            raise ValueError("費率未設定")
        
        # 計算用電量
        if previous_reading is None:
            # 獲取上月記錄
            previous_record = db.query(UtilityRecord).filter(
                UtilityRecord.room_id == room_id,
                UtilityRecord.billing_year == year,
                UtilityRecord.billing_month == month - 1 if month > 1 else 12
            ).first()
            previous_reading = previous_record.current_electricity_reading if previous_record else 0
        
        electricity_usage = current_reading - previous_reading
        electricity_cost = electricity_usage * rate.electricity_rate
        
        # 水費計算（根據住戶數量分攤）
        water_fee = rate.monthly_water_fee / room.current_occupants if room.current_occupants > 0 else rate.monthly_water_fee
        
        total_amount = electricity_cost + water_fee
        
        # 建立記錄
        record = UtilityRecord(
            room_id=room_id,
            billing_year=year,
            billing_month=month,
            previous_electricity_reading=previous_reading,
            current_electricity_reading=current_reading,
            electricity_usage=electricity_usage,
            electricity_rate=rate.electricity_rate,
            electricity_cost=electricity_cost,
            water_fee=water_fee,
            total_amount=total_amount
        )
        
        db.add(record)
        db.commit()
        db.refresh(record)
        return record
    
    @staticmethod
    def get_monthly_bills(db: Session, year: int, month: int):
        """獲取月度帳單"""
        return db.query(UtilityRecord).filter(
            UtilityRecord.billing_year == year,
            UtilityRecord.billing_month == month
        ).all()
```

### API路由架構設計

**routers/auth.py - 認證路由：**

```python
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Optional
from datetime import datetime, timedelta
from auth import create_access_token, verify_token, get_current_user
from services import UserService
from database import db_config

router = APIRouter(prefix="/auth", tags=["authentication"])

class UserRegister(BaseModel):
    username: str
    password: str
    email: Optional[str] = None
    role: str = "user"

class UserLogin(BaseModel):
    username: str
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str
    expires_in: int

@router.post("/register", response_model=dict)
async def register(user_data: UserRegister, db: Session = Depends(db_config.get_db)):
    """用戶註冊"""
    existing_user = UserService.get_user_by_username(db, user_data.username)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用戶名已存在"
        )
    
    user = UserService.create_user(
        db=db,
        username=user_data.username,
        password=user_data.password,
        email=user_data.email,
        role=user_data.role
    )
    
    return {"message": "用戶註冊成功", "user_id": user.id}

@router.post("/login", response_model=Token)
async def login(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(db_config.get_db)):
    """用戶登入"""
    user = UserService.authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用戶名或密碼錯誤",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=30)
    access_token = create_access_token(
        data={"sub": user.username, "role": user.role},
        expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": 1800  # 30分鐘
    }

@router.get("/me")
async def get_current_user_info(current_user: dict = Depends(get_current_user)):
    """獲取當前用戶資訊"""
    return current_user
```

**routers/rooms.py - 房間管理路由：**

```python
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional
from auth import get_current_user, require_role
from services import RoomService
from database import db_config

router = APIRouter(prefix="/rooms", tags=["rooms"])

class RoomCreate(BaseModel):
    room_number: str
    floor: Optional[int] = None
    area: Optional[float] = None
    rent_single: float
    rent_double: float
    description: Optional[str] = None

class RoomResponse(BaseModel):
    id: int
    room_number: str
    floor: Optional[int]
    area: Optional[float]
    rent_single: float
    rent_double: float
    current_occupants: int
    max_occupants: int
    status: str
    description: Optional[str]
    created_at: datetime
    
    class Config:
        orm_mode = True

@router.post("/", response_model=RoomResponse)
async def create_room(
    room_data: RoomCreate,
    db: Session = Depends(db_config.get_db),
    current_user: dict = Depends(require_role(["admin", "manager"]))
):
    """創建新房間"""
    try:
        room = RoomService.create_room(db, room_data.dict())
        return room
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"創建房間失敗: {str(e)}"
        )

@router.get("/", response_model=List[RoomResponse])
async def get_rooms(
    include_inactive: bool = False,
    db: Session = Depends(db_config.get_db),
    current_user: dict = Depends(get_current_user)
):
    """獲取所有房間"""
    rooms = RoomService.get_all_rooms(db, include_inactive)
    return rooms

@router.get("/available", response_model=List[RoomResponse])
async def get_available_rooms(
    db: Session = Depends(db_config.get_db),
    current_user: dict = Depends(get_current_user)
):
    """獲取可用房間"""
    rooms = RoomService.get_available_rooms(db)
    return rooms

@router.put("/{room_id}", response_model=RoomResponse)
async def update_room(
    room_id: int,
    room_data: RoomCreate,
    db: Session = Depends(db_config.get_db),
    current_user: dict = Depends(require_role(["admin", "manager"]))
):
    """更新房間資訊"""
    room = db.query(Room).filter(Room.id == room_id).first()
    if not room:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="房間不存在"
        )
    
    for key, value in room_data.dict().items():
        setattr(room, key, value)
    
    db.commit()
    db.refresh(room)
    return room
```

### 認證系統實現

**auth.py - JWT認證處理：**

```python
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from passlib.context import CryptContext
from jose import JWTError, jwt
from sqlalchemy.orm import Session
from database import db_config
from models import User

# 配置
SECRET_KEY = "your-secret-key-here"  # 生產環境中應使用環境變數
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/login")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """驗證密碼"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """生成密碼雜湊"""
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """創建JWT訪問令牌"""
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Dict[str, Any]:
    """驗證JWT令牌"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        role: str = payload.get("role")
        
        if username is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="無效的認證憑據",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        return {"username": username, "role": role}
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="無效的認證憑據",
            headers={"WWW-Authenticate": "Bearer"},
        )

async def get_current_user(token: str = Depends(oauth2_scheme)) -> Dict[str, Any]:
    """獲取當前用戶"""
    return verify_token(token)

def require_role(required_roles: List[str]):
    """權限檢查裝飾器"""
    def role_checker(current_user: Dict[str, Any] = Depends(get_current_user)):
        if current_user["role"] not in required_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="權限不足"
            )
        return current_user
    return role_checker
```

## Streamlit前端架構實現

### 主應用程式架構

**app.py - 主程式入口：**

```python
import streamlit as st
from datetime import datetime
import requests
from typing import Dict, Any
import json

# 頁面配置
st.set_page_config(
    page_title="租房管理系統",
    page_icon="🏠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 全域配置
API_BASE_URL = "http://localhost:8000"

class SessionState:
    """會話狀態管理"""
    
    @staticmethod
    def init_session():
        """初始化會話狀態"""
        if 'authenticated' not in st.session_state:
            st.session_state.authenticated = False
        if 'user_info' not in st.session_state:
            st.session_state.user_info = None
        if 'access_token' not in st.session_state:
            st.session_state.access_token = None
    
    @staticmethod
    def login(token: str, user_info: Dict[str, Any]):
        """登入處理"""
        st.session_state.authenticated = True
        st.session_state.access_token = token
        st.session_state.user_info = user_info
    
    @staticmethod
    def logout():
        """登出處理"""
        st.session_state.authenticated = False
        st.session_state.access_token = None
        st.session_state.user_info = None

class Navigation:
    """導航管理"""
    
    @staticmethod
    def show_sidebar():
        """顯示側邊欄導航"""
        with st.sidebar:
            st.title("🏠 租房管理系統")
            
            if st.session_state.authenticated:
                st.success(f"歡迎, {st.session_state.user_info['username']}")
                
                # 導航選單
                pages = {
                    "儀表板": "dashboard",
                    "房間管理": "rooms",
                    "住戶管理": "residents",
                    "費用管理": "utilities",
                    "報表統計": "reports"
                }
                
                selected_page = st.selectbox("選擇頁面", list(pages.keys()))
                
                if st.button("登出"):
                    SessionState.logout()
                    st.rerun()
                
                return pages[selected_page]
            else:
                st.info("請先登入")
                return "login"

def main():
    """主程式"""
    SessionState.init_session()
    
    # 顯示導航
    current_page = Navigation.show_sidebar()
    
    # 路由處理
    if current_page == "login":
        from pages.login import show_login_page
        show_login_page()
    elif current_page == "dashboard":
        from pages.dashboard import show_dashboard_page
        show_dashboard_page()
    elif current_page == "rooms":
        from pages.rooms import show_rooms_page
        show_rooms_page()
    elif current_page == "residents":
        from pages.residents import show_residents_page
        show_residents_page()
    elif current_page == "utilities":
        from pages.utilities import show_utilities_page
        show_utilities_page()
    elif current_page == "reports":
        from pages.reports import show_reports_page
        show_reports_page()

if __name__ == "__main__":
    main()
```

### API客戶端整合

**api_client.py - API請求封裝：**

```python
import requests
import streamlit as st
from typing import Dict, Any, Optional, List
import json

class APIClient:
    """API客戶端封裝"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def _get_headers(self) -> Dict[str, str]:
        """獲取請求頭"""
        headers = {"Content-Type": "application/json"}
        
        if st.session_state.get('access_token'):
            headers["Authorization"] = f"Bearer {st.session_state.access_token}"
        
        return headers
    
    def _handle_response(self, response: requests.Response) -> Dict[str, Any]:
        """處理API響應"""
        if response.status_code == 401:
            st.error("認證失效，請重新登入")
            st.session_state.authenticated = False
            st.session_state.access_token = None
            st.rerun()
        
        if not response.ok:
            error_detail = response.json().get('detail', '未知錯誤')
            st.error(f"API錯誤: {error_detail}")
            return None
        
        return response.json()
    
    def login(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """用戶登入"""
        try:
            response = self.session.post(
                f"{self.base_url}/auth/login",
                data={"username": username, "password": password}
            )
            
            if response.ok:
                token_data = response.json()
                # 獲取用戶資訊
                user_info = self.get_current_user(token_data['access_token'])
                if user_info:
                    return {
                        "token": token_data['access_token'],
                        "user_info": user_info
                    }
            else:
                st.error("登入失敗，請檢查用戶名和密碼")
        except Exception as e:
            st.error(f"登入錯誤: {str(e)}")
        
        return None
    
    def get_current_user(self, token: str) -> Optional[Dict[str, Any]]:
        """獲取當前用戶資訊"""
        try:
            headers = {"Authorization": f"Bearer {token}"}
            response = self.session.get(
                f"{self.base_url}/auth/me",
                headers=headers
            )
            
            if response.ok:
                return response.json()
        except Exception as e:
            st.error(f"獲取用戶資訊失敗: {str(e)}")
        
        return None
    
    def get_rooms(self, include_inactive: bool = False) -> List[Dict[str, Any]]:
        """獲取房間列表"""
        try:
            params = {"include_inactive": include_inactive}
            response = self.session.get(
                f"{self.base_url}/rooms/",
                headers=self._get_headers(),
                params=params
            )
            
            return self._handle_response(response) or []
        except Exception as e:
            st.error(f"獲取房間列表失敗: {str(e)}")
            return []
    
    def create_room(self, room_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """創建新房間"""
        try:
            response = self.session.post(
                f"{self.base_url}/rooms/",
                headers=self._get_headers(),
                json=room_data
            )
            
            return self._handle_response(response)
        except Exception as e:
            st.error(f"創建房間失敗: {str(e)}")
            return None
    
    def get_residents(self) -> List[Dict[str, Any]]:
        """獲取住戶列表"""
        try:
            response = self.session.get(
                f"{self.base_url}/residents/",
                headers=self._get_headers()
            )
            
            return self._handle_response(response) or []
        except Exception as e:
            st.error(f"獲取住戶列表失敗: {str(e)}")
            return []
    
    def create_resident(self, resident_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """創建新住戶"""
        try:
            response = self.session.post(
                f"{self.base_url}/residents/",
                headers=self._get_headers(),
                json=resident_data
            )
            
            return self._handle_response(response)
        except Exception as e:
            st.error(f"創建住戶失敗: {str(e)}")
            return None

# 全域API客戶端實例
api_client = APIClient()
```

### 登入頁面組件

**pages/login.py - 登入頁面：**

```python
import streamlit as st
from api_client import api_client

def show_login_page():
    """顯示登入頁面"""
    st.title("🏠 租房管理系統")
    
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.subheader("用戶登入")
        
        with st.form("login_form"):
            username = st.text_input("用戶名", placeholder="請輸入用戶名")
            password = st.text_input("密碼", type="password", placeholder="請輸入密碼")
            
            submitted = st.form_submit_button("登入", use_container_width=True)
            
            if submitted:
                if not username or not password:
                    st.error("請輸入用戶名和密碼")
                else:
                    with st.spinner("登入中..."):
                        login_result = api_client.login(username, password)
                        
                        if login_result:
                            from app import SessionState
                            SessionState.login(
                                login_result['token'],
                                login_result['user_info']
                            )
                            st.success("登入成功！")
                            st.rerun()
        
        st.markdown("---")
        st.info("初次使用請聯絡管理員創建帳號")
```

### 房間管理頁面

**pages/rooms.py - 房間管理頁面：**

```python
import streamlit as st
import pandas as pd
from api_client import api_client
from datetime import datetime

def show_rooms_page():
    """顯示房間管理頁面"""
    st.title("🏠 房間管理")
    
    # 頁籤設計
    tab1, tab2, tab3 = st.tabs(["房間列表", "新增房間", "房間統計"])
    
    with tab1:
        show_rooms_list()
    
    with tab2:
        show_create_room_form()
    
    with tab3:
        show_rooms_statistics()

def show_rooms_list():
    """顯示房間列表"""
    st.subheader("房間列表")
    
    # 搜尋和篩選
    col1, col2, col3 = st.columns([2, 1, 1])
    
    with col1:
        search_term = st.text_input("搜尋房間號", placeholder="輸入房間號")
    
    with col2:
        status_filter = st.selectbox("狀態篩選", ["全部", "可用", "已滿", "維護中"])
    
    with col3:
        if st.button("刷新數據"):
            st.rerun()
    
    # 獲取房間數據
    rooms = api_client.get_rooms()
    
    if not rooms:
        st.info("暫無房間數據")
        return
    
    # 數據篩選
    filtered_rooms = rooms
    
    if search_term:
        filtered_rooms = [r for r in filtered_rooms if search_term.lower() in r['room_number'].lower()]
    
    if status_filter != "全部":
        status_map = {"可用": "available", "已滿": "occupied", "維護中": "maintenance"}
        filtered_rooms = [r for r in filtered_rooms if r['status'] == status_map[status_filter]]
    
    # 數據表格
    if filtered_rooms:
        df = pd.DataFrame(filtered_rooms)
        
        # 格式化顯示
        display_df = df[[
            'room_number', 'floor', 'rent_single', 'rent_double', 
            'current_occupants', 'max_occupants', 'status'
        ]].copy()
        
        display_df.columns = [
            '房間號', '樓層', '單人租金', '雙人租金', 
            '現有住戶', '最大容量', '狀態'
        ]
        
        # 狀態顏色標記
        def highlight_status(val):
            if val == 'available':
                return 'background-color: #d4edda'
            elif val == 'occupied':
                return 'background-color: #f8d7da'
            else:
                return 'background-color: #fff3cd'
        
        styled_df = display_df.style.applymap(highlight_status, subset=['狀態'])
        st.dataframe(styled_df, use_container_width=True)
        
        # 操作按鈕
        selected_room = st.selectbox("選擇房間進行操作", 
                                   options=range(len(filtered_rooms)), 
                                   format_func=lambda x: filtered_rooms[x]['room_number'])
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("編輯房間"):
                st.info("編輯功能開發中...")
        
        with col2:
            if st.button("檢視詳情"):
                show_room_details(filtered_rooms[selected_room])
        
        with col3:
            if st.button("刪除房間"):
                st.warning("刪除功能開發中...")
    else:
        st.info("沒有符合條件的房間")

def show_create_room_form():
    """顯示新增房間表單"""
    st.subheader("新增房間")
    
    with st.form("create_room_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            room_number = st.text_input("房間號*", placeholder="例如: A101")
            floor = st.number_input("樓層", min_value=1, max_value=50, value=1)
            area = st.number_input("坪數", min_value=0.0, value=0.0, step=0.1)
        
        with col2:
            rent_single = st.number_input("單人租金*", min_value=0, value=10000, step=100)
            rent_double = st.number_input("雙人租金*", min_value=0, value=15000, step=100)
            description = st.text_area("房間描述", placeholder="房間設施、特色等")
        
        submitted = st.form_submit_button("創建房間", use_container_width=True)
        
        if submitted:
            if not room_number:
                st.error("請輸入房間號")
            elif rent_single <= 0 or rent_double <= 0:
                st.error("租金必須大於0")
            else:
                room_data = {
                    "room_number": room_number,
                    "floor": floor,
                    "area": area if area > 0 else None,
                    "rent_single": rent_single,
                    "rent_double": rent_double,
                    "description": description if description else None
                }
                
                with st.spinner("創建中..."):
                    result = api_client.create_room(room_data)
                    
                    if result:
                        st.success(f"房間 {room_number} 創建成功！")
                        st.rerun()

def show_room_details(room_data):
    """顯示房間詳情"""
    st.subheader(f"房間 {room_data['room_number']} 詳情")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.metric("當前住戶", room_data['current_occupants'])
        st.metric("最大容量", room_data['max_occupants'])
        st.metric("樓層", room_data.get('floor', 'N/A'))
    
    with col2:
        st.metric("單人租金", f"${room_data['rent_single']:,}")
        st.metric("雙人租金", f"${room_data['rent_double']:,}")
        st.metric("坪數", room_data.get('area', 'N/A'))
    
    if room_data.get('description'):
        st.text_area("房間描述", value=room_data['description'], disabled=True)

def show_rooms_statistics():
    """顯示房間統計"""
    st.subheader("房間統計")
    
    rooms = api_client.get_rooms()
    
    if not rooms:
        st.info("暫無統計數據")
        return
    
    # 統計指標
    total_rooms = len(rooms)
    available_rooms = len([r for r in rooms if r['status'] == 'available'])
    occupied_rooms = len([r for r in rooms if r['status'] == 'occupied'])
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("總房間數", total_rooms)
    
    with col2:
        st.metric("可用房間", available_rooms)
    
    with col3:
        st.metric("已滿房間", occupied_rooms)
    
    with col4:
        occupancy_rate = (occupied_rooms / total_rooms * 100) if total_rooms > 0 else 0
        st.metric("入住率", f"{occupancy_rate:.1f}%")
    
    # 圖表展示
    import plotly.express as px
    
    # 房間狀態分布
    status_counts = {}
    for room in rooms:
        status = room['status']
        status_counts[status] = status_counts.get(status, 0) + 1
    
    if status_counts:
        fig = px.pie(
            values=list(status_counts.values()),
            names=list(status_counts.keys()),
            title="房間狀態分布"
        )
        st.plotly_chart(fig, use_container_width=True)
```

## 測試策略與部署規劃

### 測試架構設計

**測試組織結構：**

```python
# 測試目錄結構
tests/
├── __init__.py
├── conftest.py                 # pytest配置
├── test_models.py             # 模型測試
├── test_services.py           # 服務層測試
├── test_api.py                # API測試
├── test_auth.py               # 認證測試
└── fixtures/                  # 測試數據
    ├── test_data.sql
    └── sample_data.py
```

**conftest.py - 測試配置：**

```python
import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient
from models import Base
from database import DatabaseConfig
from main import app

@pytest.fixture(scope="session")
def test_db():
    """測試資料庫"""
    engine = create_engine("sqlite:///./test.db", connect_args={"check_same_thread": False})
    Base.metadata.create_all(bind=engine)
    
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    yield TestingSessionLocal
    
    # 清理
    Base.metadata.drop_all(bind=engine)

@pytest.fixture
def client(test_db):
    """測試客戶端"""
    def override_get_db():
        db = test_db()
        try:
            yield db
        finally:
            db.close()
    
    app.dependency_overrides[DatabaseConfig().get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client

@pytest.fixture
def sample_user_data():
    """測試用戶數據"""
    return {
        "username": "testuser",
        "password": "testpass123",
        "email": "<EMAIL>",
        "role": "user"
    }
```

### 部署配置策略

**Docker部署配置：**

```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安裝依賴
COPY requirements.txt .
RUN pip install -r requirements.txt

# 複製程式碼
COPY . .

# 環境變數
ENV DATABASE_URL=sqlite:///./rental_management.db
ENV SECRET_KEY=your-secret-key-here

# 暴露端口
EXPOSE 8000

# 啟動命令
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

**docker-compose.yml - 容器編排：**

```yaml
version: '3.8'

services:
  api:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./data:/app/data
    environment:
      - DATABASE_URL=sqlite:///./data/rental_management.db
      - SECRET_KEY=${SECRET_KEY}
    
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.streamlit
    ports:
      - "8501:8501"
    depends_on:
      - api
    environment:
      - API_BASE_URL=http://api:8000
```

**部署腳本 - deploy.sh：**

```bash
#!/bin/bash

# 租房管理系統部署腳本

echo "開始部署租房管理系統..."

# 建立數據目錄
mkdir -p data

# 環境檢查
if [ ! -f ".env" ]; then
    echo "創建環境配置文件..."
    cat > .env << EOF
DATABASE_URL=sqlite:///./data/rental_management.db
SECRET_KEY=$(openssl rand -hex 32)
DEBUG=False
EOF
fi

# 建立虛擬環境
python -m venv venv
source venv/bin/activate

# 安裝依賴
pip install -r requirements.txt

# 數據庫初始化
python init_db.py

# 啟動服務
echo "啟動FastAPI後端..."
uvicorn main:app --host 0.0.0.0 --port 8000 &

echo "啟動Streamlit前端..."
streamlit run app.py --server.port 8501 &

echo "部署完成！"
echo "API服務: http://localhost:8000"
echo "前端界面: http://localhost:8501"
```

### 維護運營策略

**系統監控配置：**

```python
# monitoring.py - 系統監控
import logging
import time
from datetime import datetime
from sqlalchemy import text

class SystemMonitor:
    """系統監控類"""
    
    def __init__(self, db_session):
        self.db = db_session
        self.logger = logging.getLogger(__name__)
    
    def health_check(self):
        """健康檢查"""
        try:
            # 資料庫連接檢查
            result = self.db.execute(text("SELECT 1")).scalar()
            
            # 基本統計
            stats = self.get_system_stats()
            
            return {
                "status": "healthy",
                "timestamp": datetime.utcnow().isoformat(),
                "database": "connected",
                "stats": stats
            }
        except Exception as e:
            self.logger.error(f"健康檢查失敗: {str(e)}")
            return {
                "status": "unhealthy",
                "timestamp": datetime.utcnow().isoformat(),
                "error": str(e)
            }
    
    def get_system_stats(self):
        """獲取系統統計"""
        try:
            total_rooms = self.db.execute(text("SELECT COUNT(*) FROM rooms")).scalar()
            active_residents = self.db.execute(text("SELECT COUNT(*) FROM residents WHERE is_active = 1")).scalar()
            
            return {
                "total_rooms": total_rooms,
                "active_residents": active_residents,
                "last_updated": datetime.utcnow().isoformat()
            }
        except Exception as e:
            self.logger.error(f"統計獲取失敗: {str(e)}")
            return {}
```

**備份恢復策略：**

```python
# backup.py - 備份管理
import shutil
import sqlite3
from datetime import datetime
import os

class BackupManager:
    """備份管理類"""
    
    def __init__(self, db_path: str, backup_dir: str):
        self.db_path = db_path
        self.backup_dir = backup_dir
        os.makedirs(backup_dir, exist_ok=True)
    
    def create_backup(self):
        """創建備份"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"rental_db_backup_{timestamp}.db"
        backup_path = os.path.join(self.backup_dir, backup_filename)
        
        try:
            shutil.copy2(self.db_path, backup_path)
            return backup_path
        except Exception as e:
            raise Exception(f"備份失敗: {str(e)}")
    
    def restore_backup(self, backup_path: str):
        """恢復備份"""
        try:
            shutil.copy2(backup_path, self.db_path)
            return True
        except Exception as e:
            raise Exception(f"恢復失敗: {str(e)}")
    
    def list_backups(self):
        """列出備份文件"""
        backups = []
        for file in os.listdir(self.backup_dir):
            if file.startswith("rental_db_backup_") and file.endswith(".db"):
                file_path = os.path.join(self.backup_dir, file)
                backups.append({
                    "filename": file,
                    "path": file_path,
                    "created": datetime.fromtimestamp(os.path.getctime(file_path))
                })
        
        return sorted(backups, key=lambda x: x["created"], reverse=True)
```

這個細部技術規劃提供了完整的系統實現方案，涵蓋了從資料模型設計到前端組件開發的各個層面，確保系統具備企業級的穩定性和可維護性。