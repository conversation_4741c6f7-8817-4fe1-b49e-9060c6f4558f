from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from sqlalchemy import func
from pydantic import BaseModel
from typing import Dict, Any
from datetime import datetime
from ..database import get_db
from ..auth import get_current_user
from ..models import User, Room, Resident, UtilityRecord

router = APIRouter(prefix="/reports", tags=["報表統計"])

class DashboardStats(BaseModel):
    total_rooms: int
    occupied_rooms: int
    available_rooms: int
    total_residents: int
    occupancy_rate: float
    monthly_income: float

class IncomeStats(BaseModel):
    total_rent: float
    total_utilities: float
    total_income: float
    paid_amount: float
    pending_amount: float

@router.get("/dashboard", response_model=DashboardStats)
async def get_dashboard_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """獲取儀表板統計資料"""
    try:
        # 房間統計
        total_rooms = db.query(Room).filter(Room.is_active == True).count()

        # 已住房間（有住戶的房間）
        occupied_rooms = db.query(Room).filter(
            Room.is_active == True,
            Room.current_occupants > 0
        ).count()

        # 可用房間（還有空位的房間）
        available_rooms = db.query(Room).filter(
            Room.is_active == True,
            Room.current_occupants < Room.max_occupants
        ).count()

        # 住戶統計
        total_residents = db.query(Resident).filter(Resident.is_active == True).count()

        # 入住率計算
        occupancy_rate = (occupied_rooms / total_rooms * 100) if total_rooms > 0 else 0

        # 當月收入統計
        current_month = datetime.now().month
        current_year = datetime.now().year

        # 水電費收入
        try:
            monthly_income_query = db.query(func.sum(UtilityRecord.total_amount)).filter(
                UtilityRecord.billing_year == current_year,
                UtilityRecord.billing_month == current_month
            ).scalar()
            monthly_income = monthly_income_query or 0.0
        except Exception:
            monthly_income = 0.0

        # 租金收入（簡化計算）
        try:
            occupied_rooms_list = db.query(Room).filter(
                Room.is_active == True,
                Room.current_occupants > 0
            ).all()

            rent_income = 0.0
            for room in occupied_rooms_list:
                if room.current_occupants == 1:
                    rent_income += room.rent_single or 0
                elif room.current_occupants >= 2:
                    rent_income += room.rent_double or 0

            monthly_income += rent_income
        except Exception:
            # 如果租金計算失敗，忽略錯誤
            pass

        return {
            "total_rooms": total_rooms,
            "occupied_rooms": occupied_rooms,
            "available_rooms": available_rooms,
            "total_residents": total_residents,
            "occupancy_rate": round(occupancy_rate, 1),
            "monthly_income": round(monthly_income, 2)
        }

    except Exception as e:
        # 記錄錯誤但返回預設值
        print(f"Dashboard stats error: {e}")
        return {
            "total_rooms": 0,
            "occupied_rooms": 0,
            "available_rooms": 0,
            "total_residents": 0,
            "occupancy_rate": 0.0,
            "monthly_income": 0.0
        }

@router.get("/income-summary", response_model=IncomeStats)
async def get_income_summary(
    year: int,
    month: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """獲取收入統計"""
    
    # 公用事業費用統計
    utility_stats = db.query(
        func.sum(UtilityRecord.electricity_cost).label('electricity'),
        func.sum(UtilityRecord.water_fee).label('water'),
        func.sum(UtilityRecord.total_amount).label('total')
    ).filter(
        UtilityRecord.billing_year == year,
        UtilityRecord.billing_month == month
    ).first()
    
    total_utilities = utility_stats.total or 0.0
    
    # 已付款和待付款統計
    paid_amount = db.query(func.sum(UtilityRecord.total_amount)).filter(
        UtilityRecord.billing_year == year,
        UtilityRecord.billing_month == month,
        UtilityRecord.payment_status == "paid"
    ).scalar() or 0.0
    
    pending_amount = total_utilities - paid_amount
    
    # 租金收入（簡化計算）
    occupied_rooms_for_income = db.query(Room).filter(
        Room.is_active == True,
        Room.current_occupants > 0
    ).all()

    total_rent = 0.0
    for room in occupied_rooms_for_income:
        if room.current_occupants == 1:
            total_rent += room.rent_single
        elif room.current_occupants >= 2:
            total_rent += room.rent_double
    
    total_income = total_rent + total_utilities
    
    return {
        "total_rent": total_rent,
        "total_utilities": total_utilities,
        "total_income": total_income,
        "paid_amount": paid_amount,
        "pending_amount": pending_amount
    }
