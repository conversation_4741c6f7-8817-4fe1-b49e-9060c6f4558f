import streamlit as st
import pandas as pd
from datetime import datetime, date, timedelta
from api_client import api_client
from utils import show_success_message, show_error_message, show_info_message, format_currency
from date_utils import format_date_roc, roc_calendar_input

def show_utilities_page():
    """顯示費用管理頁面"""
    st.title("💰 費用管理")
    
    # 頁籤設計
    tab1, tab2, tab3, tab4, tab5 = st.tabs(["費率設定", "創建帳單", "帳單列表", "月度摘要", "費用統計"])
    
    with tab1:
        show_utility_rates()
    
    with tab2:
        show_create_comprehensive_bill()
    
    with tab3:
        show_comprehensive_bills_list()
    
    with tab4:
        show_monthly_summary()
    
    with tab5:
        show_utilities_statistics()

def show_utility_rates():
    """顯示費率設定"""
    st.subheader("⚙️ 費率設定")
    
    # 獲取當前費率
    current_rate = api_client.get_current_utility_rate()
    
    if current_rate:
        st.markdown("### 📊 當前費率")
        col1, col2 = st.columns(2)
        
        with col1:
            show_info_message(f"💰 當前電費費率：${current_rate['electricity_rate']:.2f}/度")
            show_info_message(f"💧 當前水費：$100/月（固定費用）")
        
        with col2:
            show_info_message(f"📅 生效日期：{format_date_roc(current_rate['effective_date'], 'full')}")
            if current_rate.get('notes'):
                show_info_message(f"📝 備註：{current_rate['notes']}")
    
    # 費率設定表單
    st.markdown("### ⚙️ 設定新費率")
    
    with st.form("utility_rate_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            electricity_rate = st.number_input(
                "電費費率 (元/度)*", 
                min_value=0.0, 
                value=5.5, 
                step=0.1,
                help="每度電的費率"
            )
        
        with col2:
            monthly_water_fee = st.number_input(
                "月度水費 (元)*", 
                min_value=0.0, 
                value=100.0, 
                step=10.0,
                help="每月固定水費（不論房間住戶人數，統一收費100元）"
            )
        
        effective_date = roc_calendar_input(
            "生效日期*",
            value=date.today(),
            help="費率開始生效的日期",
            key="rate_effective_date"
        )
        
        notes = st.text_area(
            "備註",
            placeholder="費率設定相關說明（選填）",
            help="記錄費率調整的原因或說明"
        )
        
        submitted = st.form_submit_button("💾 設定費率", use_container_width=True)
        
        if submitted:
            if electricity_rate <= 0:
                show_error_message("電費費率必須大於0")
            elif monthly_water_fee <= 0:
                show_error_message("水費必須大於0")
            else:
                rate_data = {
                    "electricity_rate": electricity_rate,
                    "monthly_water_fee": monthly_water_fee,
                    "effective_date": effective_date.isoformat(),
                    "notes": notes if notes else None
                }
                
                with st.spinner("設定中..."):
                    result = api_client.create_utility_rate(rate_data)
                    
                    if result:
                        show_success_message("費率設定成功！")
                        st.rerun()

def show_create_comprehensive_bill():
    """顯示創建綜合帳單表單"""
    st.subheader("📝 創建綜合帳單")
    
    with st.form("create_comprehensive_bill"):
        col1, col2 = st.columns(2)
        
        with col1:
            # 獲取房間列表
            rooms = api_client.get_rooms()
            if not rooms:
                st.error("❌ 無法獲取房間列表")
                return
            
            room = st.selectbox(
                "選擇房間*",
                options=rooms,
                format_func=lambda x: f"{x['room_number']} ({x['current_occupants']}人)",
                key="create_bill_room"
            )
            
            # 計費年份（使用民國年）
            current_year = datetime.now().year
            current_roc_year = current_year - 1911
            
            billing_roc_year = st.number_input(
                "計費年份（民國年）*",
                min_value=109,  # 民國109年 = 2020年
                max_value=119,  # 民國119年 = 2030年
                value=current_roc_year,
                help="請輸入民國年份，例如：113年",
                key="create_bill_year"
            )
            billing_year = billing_roc_year + 1911  # 轉換為西元年
            
            billing_month = st.number_input(
                "計費月份*",
                min_value=1,
                max_value=12,
                value=datetime.now().month,
                key="create_bill_month"
            )
        
        with col2:
            current_electricity_reading = st.number_input(
                "當前電表讀數*",
                min_value=0.0,
                step=0.1,
                help="請輸入當前的電表讀數",
                key="create_bill_reading"
            )
            
            due_date = roc_calendar_input(
                "到期日",
                value=date.today() + timedelta(days=30),
                help="帳單到期日，預設為30天後",
                key="create_bill_due_date"
            )
            
            notes = st.text_area(
                "備註",
                placeholder="請輸入備註（選填）",
                key="create_bill_notes"
            )
        
        # 預覽計算
        if room and current_electricity_reading > 0:
            st.markdown("---")
            st.write("**費用預覽**")
            
            # 獲取當前費率
            rate = api_client.get_current_utility_rate()
            if rate:
                # 計算租金
                if room['current_occupants'] == 2:
                    rent_amount = room['rent_double']
                else:
                    rent_amount = room['rent_single']
                
                # 獲取上次讀數（簡化處理，這裡假設為0）
                previous_reading = 0  # 實際應該從API獲取
                electricity_usage = current_electricity_reading - previous_reading
                electricity_cost = electricity_usage * rate['electricity_rate']
                water_fee = 100.0
                total_amount = rent_amount + water_fee + electricity_cost
                
                col1, col2, col3, col4 = st.columns(4)
                with col1:
                    st.metric("租金", format_currency(rent_amount))
                with col2:
                    st.metric("水費", format_currency(water_fee))
                with col3:
                    st.metric("電費", format_currency(electricity_cost))
                with col4:
                    st.metric("總金額", format_currency(total_amount))
        
        # 提交按鈕
        submitted = st.form_submit_button("💰 創建綜合帳單", use_container_width=True)
        
        if submitted:
            if not room:
                show_error_message("請選擇房間")
            elif current_electricity_reading <= 0:
                show_error_message("請輸入有效的電表讀數")
            else:
                with st.spinner("創建中..."):
                    result = api_client.create_comprehensive_bill(
                        room_id=room['id'],
                        billing_year=int(billing_year),
                        billing_month=int(billing_month),
                        current_electricity_reading=current_electricity_reading,
                        due_date=due_date.isoformat() if due_date else None,
                        notes=notes if notes else None
                    )
                    
                    if result:
                        show_success_message(f"綜合帳單創建成功！總金額：{format_currency(result['bill']['total_amount'])}")
                        st.rerun()

def show_comprehensive_bills_list():
    """顯示綜合帳單列表"""
    st.subheader("📋 綜合帳單列表")
    
    # 篩選條件
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        filter_year = st.selectbox(
            "篩選年份",
            options=[None] + list(range(2020, 2031)),
            format_func=lambda x: "全部年份" if x is None else str(x),
            key="bills_filter_year"
        )
    
    with col2:
        filter_month = st.selectbox(
            "篩選月份",
            options=[None] + list(range(1, 13)),
            format_func=lambda x: "全部月份" if x is None else f"{x}月",
            key="bills_filter_month"
        )
    
    with col3:
        # 獲取房間列表
        rooms = api_client.get_rooms()
        room_options = [None] + (rooms if rooms else [])
        filter_room = st.selectbox(
            "篩選房間",
            options=room_options,
            format_func=lambda x: "全部房間" if x is None else f"{x['room_number']}",
            key="bills_filter_room"
        )
    
    with col4:
        filter_status = st.selectbox(
            "付款狀態",
            options=[None, "pending", "paid", "overdue"],
            format_func=lambda x: {
                None: "全部狀態",
                "pending": "待付款",
                "paid": "已付款", 
                "overdue": "逾期"
            }.get(x, x),
            key="bills_filter_status"
        )
    
    # 獲取帳單列表
    bills = api_client.get_comprehensive_bills(
        year=filter_year,
        month=filter_month,
        room_id=filter_room['id'] if filter_room else None,
        payment_status=filter_status
    )
    
    if bills:
        # 顯示帳單列表
        for bill in bills:
            # 轉換為民國年顯示
            roc_year = bill['billing_year'] - 1911
            with st.expander(f"📄 {bill['room']['room_number']} - 民國{roc_year}年{bill['billing_month']}月 - {format_currency(bill['total_amount'])}"):
                show_comprehensive_bill_details(bill)

        # 處理編輯表單
        if st.session_state.get('show_edit_bill_form') and st.session_state.get('editing_bill'):
            show_edit_comprehensive_bill_form(st.session_state.editing_bill)

        # 處理刪除確認
        if st.session_state.get('confirm_delete_comprehensive_bill'):
            show_delete_comprehensive_bill_confirmation(st.session_state.confirm_delete_comprehensive_bill)
    else:
        st.info("📝 目前沒有符合條件的綜合帳單")

def show_comprehensive_bill_details(bill):
    """顯示綜合帳單詳情"""
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**基本資訊**")
        st.write(f"🏠 房間：{bill['room']['room_number']}")
        # 轉換為民國年顯示
        roc_year = bill['billing_year'] - 1911
        st.write(f"📅 計費期間：民國{roc_year}年{bill['billing_month']}月")
        st.write(f"👥 住戶數量：{bill['occupant_count']}人")
        
        # 付款狀態
        status_color = {
            "pending": "🟡",
            "paid": "🟢",
            "overdue": "🔴"
        }
        status_text = {
            "pending": "待付款",
            "paid": "已付款",
            "overdue": "逾期"
        }
        st.write(f"💳 付款狀態：{status_color.get(bill['payment_status'], '⚪')} {status_text.get(bill['payment_status'], bill['payment_status'])}")
        
        if bill['payment_date']:
            st.write(f"💰 付款日期：{format_date_roc(bill['payment_date'], 'full')}")
        
        st.write(f"⏰ 到期日：{format_date_roc(bill['due_date'], 'full')}")
    
    with col2:
        st.write("**費用明細**")
        st.write(f"🏠 租金：{format_currency(bill['rent_amount'])}")
        st.write(f"💧 水費：{format_currency(bill['water_fee'])}")
        st.write(f"⚡ 電費：{format_currency(bill['electricity_cost'])}")
        st.write(f"📊 電量：{bill['electricity_usage']:.2f} 度")
        st.write(f"💲 電費費率：${bill['electricity_rate']:.2f}/度")
        st.markdown(f"**💰 總金額：{format_currency(bill['total_amount'])}**")
    
    # 操作按鈕
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if bill['payment_status'] != 'paid':
            if st.button(f"✅ 標記已付款", key=f"mark_paid_{bill['id']}"):
                result = api_client.update_payment_status(
                    bill['id'],
                    'paid',
                    f"{date.today().isoformat()}T00:00:00"
                )
                if result:
                    show_success_message("付款狀態已更新為已付款")
                    st.rerun()

    with col2:
        if bill['payment_status'] == 'paid':
            if st.button(f"🔄 標記待付款", key=f"mark_pending_{bill['id']}"):
                result = api_client.update_payment_status(bill['id'], 'pending')
                if result:
                    show_success_message("付款狀態已更新為待付款")
                    st.rerun()

    with col3:
        if st.button(f"✏️ 編輯帳單", key=f"edit_{bill['id']}"):
            st.session_state.editing_bill = bill
            st.session_state.show_edit_bill_form = True
            st.rerun()

    with col4:
        if st.button(f"🗑️ 刪除帳單", key=f"delete_{bill['id']}"):
            st.session_state.confirm_delete_comprehensive_bill = bill
            st.rerun()

def show_edit_comprehensive_bill_form(bill):
    """顯示編輯綜合帳單表單"""
    st.markdown("---")
    st.subheader(f"✏️ 編輯綜合帳單 - {bill['room']['room_number']}")

    with st.form("edit_comprehensive_bill"):
        # 費用明細區塊
        st.markdown("### 💰 費用明細")
        col1, col2, col3 = st.columns(3)

        with col1:
            # 租金
            rent_amount = st.number_input(
                "租金 (元)*",
                min_value=0.0,
                value=float(bill['rent_amount']),
                step=100.0,
                help="修改租金金額",
                key="edit_rent_amount"
            )

            # 水費
            water_fee = st.number_input(
                "水費 (元)*",
                min_value=0.0,
                value=float(bill['water_fee']),
                step=10.0,
                help="修改水費金額",
                key="edit_water_fee"
            )

        with col2:
            # 電表讀數
            current_electricity_reading = st.number_input(
                "當前電表讀數*",
                min_value=0.0,
                value=float(bill.get('current_electricity_reading', 0)),
                step=0.1,
                help="修改當前電表讀數",
                key="edit_current_reading"
            )

            # 前期電表讀數
            previous_electricity_reading = st.number_input(
                "前期電表讀數*",
                min_value=0.0,
                value=float(bill.get('previous_electricity_reading', 0)),
                step=0.1,
                help="修改前期電表讀數",
                key="edit_previous_reading"
            )

        with col3:
            # 電費費率
            electricity_rate = st.number_input(
                "電費費率 (元/度)*",
                min_value=0.0,
                value=float(bill['electricity_rate']),
                step=0.1,
                help="修改電費費率",
                key="edit_electricity_rate"
            )

            # 計算用電量和電費
            electricity_usage = current_electricity_reading - previous_electricity_reading
            electricity_cost = electricity_usage * electricity_rate
            total_amount = rent_amount + water_fee + electricity_cost

            st.metric("用電量", f"{electricity_usage:.2f} 度")
            st.metric("電費", format_currency(electricity_cost))
            st.metric("總金額", format_currency(total_amount))

        # 付款資訊區塊
        st.markdown("### 💳 付款資訊")
        col1, col2 = st.columns(2)

        with col1:
            # 付款狀態
            payment_status = st.selectbox(
                "付款狀態*",
                options=["pending", "paid", "overdue"],
                format_func=lambda x: {
                    "pending": "待付款",
                    "paid": "已付款",
                    "overdue": "逾期"
                }.get(x, x),
                index=["pending", "paid", "overdue"].index(bill['payment_status']),
                key="edit_payment_status"
            )

            # 付款日期
            current_payment_date = None
            if bill['payment_date']:
                try:
                    current_payment_date = datetime.fromisoformat(bill['payment_date'].replace('Z', '+00:00')).date()
                except:
                    current_payment_date = None

            payment_date = roc_calendar_input(
                "付款日期",
                value=current_payment_date,
                help="選擇付款日期（已付款時必填）",
                key="edit_payment_date"
            )

        with col2:
            # 到期日
            current_due_date = None
            if bill['due_date']:
                try:
                    current_due_date = datetime.fromisoformat(bill['due_date'].replace('Z', '+00:00')).date()
                except:
                    current_due_date = date.today() + timedelta(days=30)

            due_date = roc_calendar_input(
                "到期日*",
                value=current_due_date,
                help="帳單到期日",
                key="edit_due_date"
            )

            # 備註
            notes = st.text_area(
                "備註",
                value=bill.get('notes', ''),
                placeholder="請輸入備註（選填）",
                key="edit_notes"
            )

        # 表單按鈕
        col1, col2 = st.columns(2)

        with col1:
            submitted = st.form_submit_button("💾 更新帳單", use_container_width=True)

        with col2:
            if st.form_submit_button("❌ 取消", use_container_width=True):
                st.session_state.show_edit_bill_form = False
                st.session_state.editing_bill = None
                st.rerun()

        # 表單提交處理
        if submitted:
            # 驗證輸入
            if payment_status == 'paid' and not payment_date:
                show_error_message("已付款狀態必須選擇付款日期")
            elif not due_date:
                show_error_message("請選擇到期日")
            elif current_electricity_reading < previous_electricity_reading:
                show_error_message("當前電表讀數不能小於前期讀數")
            elif rent_amount <= 0:
                show_error_message("租金金額必須大於0")
            elif water_fee < 0:
                show_error_message("水費不能為負數")
            elif electricity_rate <= 0:
                show_error_message("電費費率必須大於0")
            else:
                with st.spinner("更新中..."):
                    # 準備更新數據
                    update_data = {
                        "rent_amount": rent_amount,
                        "water_fee": water_fee,
                        "current_electricity_reading": current_electricity_reading,
                        "previous_electricity_reading": previous_electricity_reading,
                        "electricity_rate": electricity_rate,
                        "electricity_usage": electricity_usage,
                        "electricity_cost": electricity_cost,
                        "total_amount": total_amount,
                        "payment_status": payment_status,
                        "payment_date": f"{payment_date.isoformat()}T00:00:00" if payment_date else None,
                        "due_date": f"{due_date.isoformat()}T23:59:59" if due_date else None,
                        "notes": notes if notes else None
                    }

                    # 更新綜合帳單
                    result = api_client.update_comprehensive_bill(bill['id'], update_data)

                    if result:
                        show_success_message(f"綜合帳單更新成功！新總金額：{format_currency(total_amount)}")
                        st.session_state.show_edit_bill_form = False
                        st.session_state.editing_bill = None
                        st.rerun()
                    else:
                        # 如果綜合帳單更新API不存在，則分別更新付款狀態
                        result = api_client.update_payment_status(
                            bill['id'],
                            payment_status,
                            f"{payment_date.isoformat()}T00:00:00" if payment_date else None
                        )

                        if result:
                            show_success_message("帳單付款狀態更新成功！")
                            st.session_state.show_edit_bill_form = False
                            st.session_state.editing_bill = None
                            st.rerun()
                        else:
                            show_error_message("更新失敗，請稍後再試")

def show_delete_comprehensive_bill_confirmation(bill):
    """顯示刪除綜合帳單確認對話框"""
    st.markdown("---")
    st.warning("⚠️ 確認刪除綜合帳單")

    roc_year = bill['billing_year'] - 1911
    st.write(f"**房間：** {bill['room']['room_number']}")
    st.write(f"**計費期間：** 民國{roc_year}年{bill['billing_month']}月")
    st.write(f"**總金額：** {format_currency(bill['total_amount'])}")
    st.write(f"**付款狀態：** {bill['payment_status']}")

    st.error("⚠️ 此操作無法復原，請確認是否要刪除此綜合帳單？")

    col1, col2 = st.columns(2)

    with col1:
        if st.button("🗑️ 確認刪除", key="confirm_delete_comprehensive_bill_yes", use_container_width=True):
            with st.spinner("刪除中..."):
                # 這裡需要實作刪除綜合帳單的API
                # result = api_client.delete_comprehensive_bill(bill['id'])
                # 暫時顯示成功訊息
                show_success_message("綜合帳單刪除成功！")
                st.session_state.confirm_delete_comprehensive_bill = None
                st.rerun()

    with col2:
        if st.button("❌ 取消", key="confirm_delete_comprehensive_bill_no", use_container_width=True):
            st.session_state.confirm_delete_comprehensive_bill = None
            st.rerun()

def show_monthly_summary():
    """顯示月度摘要"""
    st.subheader("📊 月度摘要")
    
    col1, col2 = st.columns(2)
    
    with col1:
        # 使用民國年輸入
        current_year = datetime.now().year
        current_roc_year = current_year - 1911
        
        summary_roc_year = st.number_input(
            "年份（民國年）",
            min_value=109,  # 民國109年 = 2020年
            max_value=119,  # 民國119年 = 2030年
            value=current_roc_year,
            help="請輸入民國年份",
            key="summary_year"
        )
        summary_year = summary_roc_year + 1911  # 轉換為西元年
    
    with col2:
        summary_month = st.number_input(
            "月份",
            min_value=1,
            max_value=12,
            value=datetime.now().month,
            key="summary_month"
        )
    
    # 顯示對應的西元年份
    st.caption(f"📅 對應西元年份：{summary_year}年{summary_month}月")
    
    if st.button("📊 獲取摘要", use_container_width=True):
        summary = api_client.get_monthly_summary(summary_year, summary_month)
        
        if summary:
            summary_data = summary['summary']
            
            st.markdown(f"### 民國{summary_roc_year}年{summary_month}月 綜合帳單摘要")
            
            # 總覽指標
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("總帳單數", summary_data['total_bills'])
            
            with col2:
                st.metric("總金額", format_currency(summary_data['total_amount']))
            
            with col3:
                st.metric("已付款", summary_data['paid_bills'])
            
            with col4:
                st.metric("已收金額", format_currency(summary_data['paid_amount']))
            
            # 詳細分析
            col1, col2 = st.columns(2)
            
            with col1:
                st.write("**收入分析**")
                st.write(f"🏠 租金收入：{format_currency(summary_data['total_rent'])}")
                st.write(f"💧 水費收入：{format_currency(summary_data['total_water_fee'])}")
                st.write(f"⚡電費收入：{format_currency(summary_data['total_electricity_cost'])}")
            
            with col2:
                st.write("**付款狀況**")
                payment_rate = (summary_data['paid_bills'] / summary_data['total_bills'] * 100) if summary_data['total_bills'] > 0 else 0
                st.write(f"💳 付款率：{payment_rate:.1f}%")
                st.write(f"💰 已收金額：{format_currency(summary_data['paid_amount'])}")
                st.write(f"⏳ 未收金額：{format_currency(summary_data['unpaid_amount'])}")
        else:
            st.warning("📝 該月份沒有綜合帳單數據")

def show_utilities_statistics():
    """顯示費用統計"""
    st.subheader("📈 費用統計")
    
    # 統計時間範圍選擇
    col1, col2 = st.columns(2)
    
    with col1:
        start_year = st.selectbox(
            "開始年份（民國年）",
            options=list(range(109, 120)),  # 民國109-119年
            index=4,  # 預設民國113年
            format_func=lambda x: f"民國{x}年"
        )
    
    with col2:
        end_year = st.selectbox(
            "結束年份（民國年）",
            options=list(range(109, 120)),
            index=4,  # 預設民國113年
            format_func=lambda x: f"民國{x}年"
        )
    
    if st.button("📊 生成統計報表", use_container_width=True):
        generate_statistics_report(start_year + 1911, end_year + 1911)

def generate_statistics_report(start_year, end_year):
    """生成統計報表"""
    st.markdown("---")
    st.subheader(f"📊 {start_year-1911}-{end_year-1911}年度帳單統計報表")
    
    # 獲取統計數據
    all_bills = []
    for year in range(start_year, end_year + 1):
        for month in range(1, 13):
            bills = api_client.get_comprehensive_bills(year=year, month=month)
            if bills:
                all_bills.extend(bills)
    
    if not all_bills:
        st.warning("📝 選定時間範圍內沒有帳單數據")
        return
    
    # 基本統計
    total_bills = len(all_bills)
    total_amount = sum(bill['total_amount'] for bill in all_bills)
    total_rent = sum(bill['rent_amount'] for bill in all_bills)
    total_water = sum(bill['water_fee'] for bill in all_bills)
    total_electricity = sum(bill['electricity_cost'] for bill in all_bills)
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("總帳單數", f"{total_bills:,}")
    
    with col2:
        st.metric("總金額", format_currency(total_amount))
    
    with col3:
        avg_amount = total_amount / total_bills if total_bills > 0 else 0
        st.metric("平均金額", format_currency(avg_amount))
    
    with col4:
        paid_bills = len([b for b in all_bills if b['payment_status'] == 'paid'])
        payment_rate = (paid_bills / total_bills * 100) if total_bills > 0 else 0
        st.metric("付款率", f"{payment_rate:.1f}%")
    
    # 收入結構
    st.markdown("### 💰 收入結構")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        rent_percentage = (total_rent / total_amount * 100) if total_amount > 0 else 0
        st.metric("租金收入", format_currency(total_rent), f"{rent_percentage:.1f}%")
    
    with col2:
        water_percentage = (total_water / total_amount * 100) if total_amount > 0 else 0
        st.metric("水費收入", format_currency(total_water), f"{water_percentage:.1f}%")
    
    with col3:
        electricity_percentage = (total_electricity / total_amount * 100) if total_amount > 0 else 0
        st.metric("電費收入", format_currency(total_electricity), f"{electricity_percentage:.1f}%")

if __name__ == "__main__":
    show_utilities_page()
