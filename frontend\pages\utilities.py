import streamlit as st
import pandas as pd
from datetime import datetime, date, timedelta
from api_client import api_client
from utils import (format_currency, format_date, show_success_message,
                   show_error_message, show_warning_message, show_info_message,
                   get_payment_status_color)
import sys
import os
# 添加父目錄到路徑以便導入date_utils
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from frontend.date_utils import format_date_roc, roc_date_input, roc_calendar_input, format_date_for_api, create_roc_date_display

def show_utilities_page():
    """顯示費用管理頁面"""
    st.title("💰 費用管理")
    
    # 頁籤設計
    tab1, tab2, tab3, tab4 = st.tabs(["費率設定", "電表抄錄", "帳單管理", "費用統計"])
    
    with tab1:
        show_utility_rates()
    
    with tab2:
        show_meter_reading()
    
    with tab3:
        show_bills_management()
    
    with tab4:
        show_utilities_statistics()

def show_utility_rates():
    """顯示費率設定"""
    st.subheader("公用事業費率設定")

    # 頁籤設計
    rate_tab1, rate_tab2, rate_tab3 = st.tabs(["當前費率", "所有費率", "新增費率"])

    with rate_tab1:
        show_current_rate()

    with rate_tab2:
        show_all_rates()

    with rate_tab3:
        show_create_rate_form()

def show_current_rate():
    """顯示當前費率"""
    current_rate = api_client.get_current_utility_rate()

    if current_rate:
        show_success_message("當前費率設定")
        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("電費每度", f"${current_rate['electricity_rate']:.2f}")

        with col2:
            st.metric("月度水費", format_currency(current_rate['monthly_water_fee']))

        with col3:
            st.metric("生效日期", format_date_roc(current_rate['effective_date'], "full"))

        if current_rate.get('notes'):
            show_info_message(f"備註：{current_rate['notes']}")
    else:
        show_warning_message("尚未設定費率，請先設定費率")

def show_all_rates():
    """顯示所有費率"""
    st.subheader("所有費率記錄")

    rates = api_client.get_utility_rates()

    # 獲取當前費率ID
    current_rate = api_client.get_current_utility_rate()
    current_rate_id = current_rate['id'] if current_rate else None

    if rates:
        # 創建表格數據
        rate_data = []
        for rate in rates:
            # 判斷是否為當前使用的費率
            if rate['id'] == current_rate_id:
                status = "🟢 使用中"
            elif rate.get('is_active'):
                status = "🟡 活躍"
            else:
                status = "🔴 歷史"

            rate_data.append({
                "ID": rate['id'],
                "電費每度": f"${rate['electricity_rate']:.2f}",
                "月度水費": format_currency(rate['monthly_water_fee']),
                "生效日期": format_date_roc(rate['effective_date'], "short"),
                "狀態": status,
                "備註": rate.get('notes', '')
            })

        df = pd.DataFrame(rate_data)
        st.dataframe(df, use_container_width=True)

        # 資料清理功能
        st.subheader("🧹 資料清理")
        col1, col2 = st.columns(2)

        with col1:
            if st.button("🗑️ 清除歷史費率", key="clear_historical_rates", use_container_width=True, help="保留當前費率，清除所有歷史費率"):
                st.session_state.confirm_clear_historical_rates = True
                st.rerun()

        with col2:
            if st.button("🗑️ 清除所有費率", key="clear_all_rates", use_container_width=True, help="清除所有費率並創建預設費率"):
                st.session_state.confirm_clear_all_rates = True
                st.rerun()

        # 選擇費率進行操作
        if len(rates) > 0:
            st.subheader("費率操作")

            rate_options = [f"ID: {rate['id']} - {format_date_roc(rate['effective_date'], 'short')}" for rate in rates]
            selected_rate_idx = st.selectbox("選擇費率", range(len(rate_options)), format_func=lambda x: f"{x+1}. {rate_options[x]}")

            if selected_rate_idx is not None:
                selected_rate = rates[selected_rate_idx]

                col1, col2, col3 = st.columns(3)

                with col1:
                    if st.button("✏️ 編輯費率", key="edit_rate", use_container_width=True):
                        st.session_state.editing_rate = selected_rate
                        st.session_state.show_edit_form = True
                        st.rerun()

                with col2:
                    # 顯示費率狀態和刪除按鈕
                    if selected_rate.get('is_active'):
                        st.button("🗑️ 無法刪除", key="cannot_delete_rate", disabled=True, use_container_width=True, help="無法刪除當前使用的費率")
                    else:
                        if st.button("🗑️ 刪除費率", key="delete_rate", use_container_width=True):
                            st.session_state.confirm_delete_rate = selected_rate
                            st.rerun()

                with col3:
                    if selected_rate.get('is_active'):
                        if st.button("❌ 停用費率", key="deactivate_rate", use_container_width=True):
                            st.session_state.confirm_deactivate_rate = selected_rate
                            st.rerun()

                # 顯示費率詳情
                st.write("**選中費率詳情：**")
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.write(f"**電費每度：** ${selected_rate['electricity_rate']:.2f}")
                with col2:
                    st.write(f"**月度水費：** {format_currency(selected_rate['monthly_water_fee'])}")
                with col3:
                    st.write(f"**狀態：** {'使用中' if selected_rate.get('is_active') else '歷史'}")

                if selected_rate.get('notes'):
                    st.write(f"**備註：** {selected_rate['notes']}")

                # 確認刪除對話框
                if st.session_state.get('confirm_delete_rate'):
                    show_delete_rate_confirmation(st.session_state.confirm_delete_rate)

                # 確認停用對話框
                if st.session_state.get('confirm_deactivate_rate'):
                    show_deactivate_rate_confirmation(st.session_state.confirm_deactivate_rate)

        # 確認清理歷史費率對話框
        if st.session_state.get('confirm_clear_historical_rates'):
            show_clear_historical_rates_confirmation()

        # 確認清理所有費率對話框
        if st.session_state.get('confirm_clear_all_rates'):
            show_clear_all_rates_confirmation()
    else:
        show_info_message("暫無費率記錄")

    # 顯示編輯表單
    if st.session_state.get('show_edit_form', False) and st.session_state.get('editing_rate'):
        st.markdown("---")
        show_edit_rate_form(st.session_state.editing_rate)

def show_delete_rate_confirmation(rate: dict):
    """顯示刪除費率確認對話框"""
    st.markdown("---")
    st.subheader("⚠️ 確認刪除費率")

    st.warning(f"您確定要刪除以下費率記錄嗎？")

    col1, col2 = st.columns(2)
    with col1:
        st.write(f"**生效日期：** {format_date_roc(rate['effective_date'], 'full')}")
        st.write(f"**電費每度：** ${rate['electricity_rate']:.2f}")
    with col2:
        st.write(f"**月度水費：** {format_currency(rate['monthly_water_fee'])}")
        st.write(f"**備註：** {rate.get('notes', '無')}")

    st.error("⚠️ 此操作無法撤銷！")

    col1, col2, col3 = st.columns([1, 1, 1])

    with col1:
        if st.button("✅ 確認刪除", key="confirm_delete_yes", use_container_width=True):
            with st.spinner("刪除中..."):
                if api_client.delete_utility_rate(rate['id']):
                    show_success_message("費率記錄已成功刪除！")
                    st.session_state.confirm_delete_rate = None
                    st.rerun()

    with col2:
        if st.button("❌ 取消", key="confirm_delete_no", use_container_width=True):
            st.session_state.confirm_delete_rate = None
            st.rerun()

def show_deactivate_rate_confirmation(rate: dict):
    """顯示停用費率確認對話框"""
    st.markdown("---")
    st.subheader("⚠️ 確認停用費率")

    st.warning(f"您確定要停用當前使用的費率嗎？")

    col1, col2 = st.columns(2)
    with col1:
        st.write(f"**生效日期：** {format_date_roc(rate['effective_date'], 'full')}")
        st.write(f"**電費每度：** ${rate['electricity_rate']:.2f}")
    with col2:
        st.write(f"**月度水費：** {format_currency(rate['monthly_water_fee'])}")
        st.write(f"**備註：** {rate.get('notes', '無')}")

    st.info("💡 停用後，此費率將變為歷史記錄，可以被刪除")

    col1, col2, col3 = st.columns([1, 1, 1])

    with col1:
        if st.button("✅ 確認停用", key="confirm_deactivate_yes", use_container_width=True):
            with st.spinner("停用中..."):
                update_data = {"is_active": False}
                if api_client.update_utility_rate(rate['id'], update_data):
                    show_success_message("費率已成功停用！")
                    st.session_state.confirm_deactivate_rate = None
                    st.rerun()

    with col2:
        if st.button("❌ 取消", key="confirm_deactivate_no", use_container_width=True):
            st.session_state.confirm_deactivate_rate = None
            st.rerun()

def show_create_rate_form():
    
    """新增費率設定"""
    st.subheader("新增費率設定")

    with st.form("rate_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            electricity_rate = st.number_input(
                "電費每度 (元)*", 
                min_value=0.0, 
                value=5.5, 
                step=0.1,
                help="每度電的費用"
            )
            
            effective_date = roc_calendar_input(
                "生效日期*",
                value=date.today(),
                help="新費率的生效日期",
                key="create_rate_effective_date"
            )
        
        with col2:
            monthly_water_fee = st.number_input(
                "月度水費 (元)*",
                min_value=0.0,
                value=100.0,
                step=10.0,
                help="每月固定水費（不論房間住戶人數，統一收費100元）"
            )
        
        notes = st.text_area("備註", placeholder="費率調整原因或說明")
        
        submitted = st.form_submit_button("💰 設定費率", use_container_width=True)
        
        if submitted:
            if electricity_rate <= 0:
                show_error_message("電費每度必須大於0")
            elif monthly_water_fee <= 0:
                show_error_message("月度水費必須大於0")
            else:
                rate_data = {
                    "electricity_rate": electricity_rate,
                    "monthly_water_fee": monthly_water_fee,
                    "effective_date": format_date_for_api(effective_date),
                    "notes": notes if notes else None
                }
                
                with st.spinner("設定中..."):
                    result = api_client.create_utility_rate(rate_data)
                    
                    if result:
                        show_success_message("費率設定成功！")
                        st.rerun()

def show_meter_reading():
    """顯示電表抄錄"""
    st.subheader("電表抄錄")

    # 獲取房間列表
    rooms = api_client.get_rooms()

    if not rooms:
        show_warning_message("沒有房間資料，請先新增房間")
        return

    # 檢查是否有費率設定
    current_rate = api_client.get_current_utility_rate()
    if not current_rate:
        show_error_message("請先設定費率才能進行電表抄錄")
        return

    # 篩選有住戶的房間並改善顯示格式
    occupied_rooms = [room for room in rooms if room['current_occupants'] > 0]

    if not occupied_rooms:
        show_warning_message("目前沒有有住戶的房間需要抄錄")
        return

    with st.form("meter_reading_form"):
        col1, col2 = st.columns(2)

        with col1:
            # 改善房間選擇的顯示格式
            room_options = {}
            for room in occupied_rooms:
                room_display = f"房間 {room['room_number']} (住戶: {room['current_occupants']}人, 狀態: {room['status']})"
                room_options[room_display] = room['id']

            selected_room_display = st.selectbox("選擇房間*", options=list(room_options.keys()))
            selected_room_id = room_options[selected_room_display]

            # 計費月份
            billing_year = st.number_input("計費年份*", min_value=2020, max_value=2030, value=datetime.now().year)
            billing_month = st.number_input("計費月份*", min_value=1, max_value=12, value=datetime.now().month)

        with col2:
            # 獲取並顯示前期度數
            previous_reading = get_previous_reading(selected_room_id, billing_year, billing_month)

            show_info_message(f"📊 前期電表讀數：{previous_reading:.1f} 度")

            current_reading = st.number_input(
                "本月電表讀數*",
                min_value=previous_reading,
                value=previous_reading,
                step=1.0,
                help=f"請輸入本月的電表讀數（需大於前期讀數 {previous_reading:.1f}）"
            )

            # 顯示預估用電量
            if current_reading > previous_reading:
                estimated_usage = current_reading - previous_reading
                show_success_message(f"⚡ 預估用電量：{estimated_usage:.1f} 度")

            show_info_message(f"💰 當前電費費率：${current_rate['electricity_rate']:.2f}/度")
            show_info_message(f"💧 當前水費：$100/月（固定費用）")
        
        submitted = st.form_submit_button("📊 計算費用", use_container_width=True)

        if submitted:
            if current_reading <= previous_reading:
                show_error_message(f"本月讀數必須大於前期讀數 {previous_reading:.1f}")
            else:
                reading_data = {
                    "room_id": selected_room_id,
                    "billing_year": billing_year,
                    "billing_month": billing_month,
                    "current_electricity_reading": current_reading
                }

                with st.spinner("計算中..."):
                    result = api_client.create_meter_reading(reading_data)

                    if result:
                        show_success_message("電表抄錄成功！")

                        # 顯示計算結果
                        st.subheader("費用計算結果")
                        col1, col2, col3, col4 = st.columns(4)

                        with col1:
                            st.metric("用電量", f"{result['electricity_usage']:.1f} 度")

                        with col2:
                            st.metric("電費", format_currency(result['electricity_cost']))

                        with col3:
                            st.metric("水費", format_currency(result['water_fee']))

                        with col4:
                            st.metric("總費用", format_currency(result['total_amount']))

                        st.rerun()

def get_previous_reading(room_id: int, year: int, month: int) -> float:
    """獲取前期電表讀數"""
    try:
        # 計算上月
        if month == 1:
            prev_year, prev_month = year - 1, 12
        else:
            prev_year, prev_month = year, month - 1

        # 獲取上月帳單
        bills = api_client.get_utility_bills(prev_year, prev_month)

        # 查找對應房間的帳單
        for bill in bills:
            if bill['room_id'] == room_id:
                return bill['current_electricity_reading']

        # 如果沒有找到上月記錄，返回0
        return 0.0
    except Exception as e:
        st.warning(f"無法獲取前期讀數: {str(e)}")
        return 0.0

def show_bills_management():
    """顯示帳單管理"""
    st.subheader("帳單管理")

    # 選擇查詢月份
    col1, col2, col3 = st.columns([1, 1, 1])

    with col1:
        query_year = st.number_input("查詢年份", min_value=2020, max_value=2030, value=datetime.now().year)

    with col2:
        query_month = st.number_input("查詢月份", min_value=1, max_value=12, value=datetime.now().month)

    with col3:
        if st.button("🔍 查詢帳單", key="utilities_query_bills"):
            st.rerun()

    # 資料清理功能
    st.subheader("🧹 資料清理")
    if st.button("🗑️ 清除所有帳單記錄", key="clear_all_bills", use_container_width=True, help="清除所有水電費帳單記錄"):
        st.session_state.confirm_clear_all_bills = True
        st.rerun()

    # 確認清理所有帳單對話框
    if st.session_state.get('confirm_clear_all_bills'):
        show_clear_all_bills_confirmation()

    # 合併顯示所有帳單
    show_combined_bills(query_year, query_month)

def show_combined_bills(query_year: int, query_month: int):
    """顯示合併的帳單（水電費+租金）"""
    # 獲取水電費帳單
    utility_bills = api_client.get_utility_bills(query_year, query_month) or []

    # 獲取租金記錄
    rent_records = api_client.get_rent_records(year=query_year, month=query_month) or []

    # 獲取所有住戶資訊，用於關聯水電費帳單
    all_residents = api_client.get_residents() or []

    if not utility_bills and not rent_records:
        st.info(f"{query_year}年{query_month}月暫無帳單資料")

        # 顯示創建選項
        st.subheader("創建帳單")
        col1, col2 = st.columns(2)

        with col1:
            if st.button("💡 創建水電費帳單", use_container_width=True):
                st.info("請先到「電表抄錄」頁面進行抄錄以生成水電費帳單")

        with col2:
            if st.button("🏠 創建租金記錄", use_container_width=True):
                st.session_state.show_create_rent_form = True
                st.rerun()

        # 顯示創建租金記錄表單
        if st.session_state.get('show_create_rent_form', False):
            st.subheader("創建租金記錄")
            show_create_rent_record_form(query_year, query_month)

        return

    # 合併帳單數據
    combined_bills = []

    # 處理水電費帳單
    for bill in utility_bills:
        room_info = bill.get('room', {})
        room_id = bill.get('room_id')

        # 查找該房間的住戶
        room_residents = [r for r in all_residents if r.get('room_id') == room_id and r.get('is_active')]
        resident_names = [r['name'] for r in room_residents] if room_residents else []
        resident_display = ", ".join(resident_names) if resident_names else "N/A"

        combined_bills.append({
            "類型": "💡 水電費",
            "房間號": room_info.get('room_number', 'N/A'),
            "住戶": resident_display,
            "用電量": f"{bill['electricity_usage']:.1f} 度",
            "電費": format_currency(bill['electricity_cost']),
            "水費": format_currency(bill['water_fee']),
            "租金": "N/A",
            "總費用": format_currency(bill['total_amount']),
            "付款狀態": f"{get_payment_status_color(bill['payment_status'])} {bill['payment_status']}",
            "到期日期": "N/A",
            "付款日期": format_date_roc(bill['payment_date'], "short") if bill['payment_date'] else 'N/A',
            "備註": "N/A",
            "帳單ID": bill['id'],
            "帳單類型": "utility"
        })

    # 處理租金記錄
    for record in rent_records:
        room_info = record.get('room', {})
        resident_info = record.get('resident', {})
        combined_bills.append({
            "類型": "🏠 租金",
            "房間號": room_info.get('room_number', 'N/A'),
            "住戶": resident_info.get('name', 'N/A'),
            "用電量": "N/A",
            "電費": "N/A",
            "水費": "N/A",
            "租金": format_currency(record['rent_amount']),
            "總費用": format_currency(record['rent_amount']),
            "付款狀態": f"{get_payment_status_color(record['payment_status'])} {record['payment_status']}",
            "到期日期": format_date_roc(record['due_date'], "short"),
            "付款日期": format_date_roc(record['payment_date'], "short") if record['payment_date'] else 'N/A',
            "備註": record.get('notes', '') or 'N/A',
            "帳單ID": record['id'],
            "帳單類型": "rent"
        })

    # 按房間號排序
    combined_bills.sort(key=lambda x: x['房間號'])

    # 顯示合併帳單表格
    st.subheader(f"📋 {query_year}年{query_month}月帳單總覽")

    if combined_bills:
        df = pd.DataFrame(combined_bills)
        # 移除內部使用的欄位
        display_df = df.drop(columns=['帳單ID', '帳單類型'])
        st.dataframe(display_df, use_container_width=True)

        # 統計資訊
        st.subheader("📊 帳單統計")
        col1, col2, col3, col4 = st.columns(4)

        utility_count = len([b for b in combined_bills if b['帳單類型'] == 'utility'])
        rent_count = len([b for b in combined_bills if b['帳單類型'] == 'rent'])

        total_utility_amount = sum([float(b['總費用'].replace('$', '').replace(',', ''))
                                   for b in combined_bills if b['帳單類型'] == 'utility'])
        total_rent_amount = sum([float(b['總費用'].replace('$', '').replace(',', ''))
                                for b in combined_bills if b['帳單類型'] == 'rent'])

        with col1:
            st.metric("水電費帳單", f"{utility_count} 筆")

        with col2:
            st.metric("租金記錄", f"{rent_count} 筆")

        with col3:
            st.metric("水電費總額", format_currency(total_utility_amount))

        with col4:
            st.metric("租金總額", format_currency(total_rent_amount))

        # 帳單操作
        show_combined_bill_operations(combined_bills, utility_bills, rent_records)

def show_combined_bill_operations(combined_bills: list, utility_bills: list, rent_records: list):
    """顯示合併帳單操作"""
    st.subheader("💼 帳單操作")

    if not combined_bills:
        return

    # 選擇帳單
    bill_options = []
    for i, bill in enumerate(combined_bills):
        bill_type = "💡" if bill['帳單類型'] == 'utility' else "🏠"
        bill_options.append(f"{bill_type} {bill['房間號']} - {bill['總費用']}")

    selected_bill_idx = st.selectbox(
        "選擇要操作的帳單",
        options=range(len(combined_bills)),
        format_func=lambda x: f"{x+1}. {bill_options[x]}",
        key="combined_bill_select"
    )

    if selected_bill_idx is not None:
        selected_bill = combined_bills[selected_bill_idx]
        bill_type = selected_bill['帳單類型']
        bill_id = selected_bill['帳單ID']

        # 顯示帳單詳情
        st.write("**帳單詳情：**")
        col1, col2 = st.columns(2)

        with col1:
            st.write(f"**類型：** {selected_bill['類型']}")
            st.write(f"**房間號：** {selected_bill['房間號']}")
            st.write(f"**總費用：** {selected_bill['總費用']}")

        with col2:
            st.write(f"**付款狀態：** {selected_bill['付款狀態']}")
            st.write(f"**付款日期：** {selected_bill['付款日期']}")
            if selected_bill['住戶'] != 'N/A':
                st.write(f"**住戶：** {selected_bill['住戶']}")

        # 操作按鈕
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            if st.button("💳 標記已付款", key=f"mark_paid_{bill_type}_{bill_id}", use_container_width=True):
                if bill_type == 'utility':
                    update_utility_payment_status(bill_id, "paid", datetime.now().isoformat())
                else:
                    update_rent_payment_status(bill_id, "已付款", datetime.now().isoformat())

        with col2:
            if st.button("⏰ 標記逾期", key=f"mark_overdue_{bill_type}_{bill_id}", use_container_width=True):
                if bill_type == 'utility':
                    update_utility_payment_status(bill_id, "overdue")
                else:
                    update_rent_payment_status(bill_id, "逾期")

        with col3:
            if bill_type == 'rent':
                if st.button("✏️ 編輯租金", key=f"edit_rent_{bill_id}", use_container_width=True):
                    # 找到對應的租金記錄
                    rent_record = next((r for r in rent_records if r['id'] == bill_id), None)
                    if rent_record:
                        st.session_state.editing_rent_record = rent_record
                        st.rerun()
            else:
                st.write("")  # 空白佔位符

        with col4:
            if st.button("🗑️ 刪除帳單", key=f"delete_bill_{bill_type}_{bill_id}", use_container_width=True):
                st.session_state.confirm_delete_bill = {
                    'type': bill_type,
                    'id': bill_id,
                    'info': f"{selected_bill['房間號']} - {selected_bill['總費用']}"
                }
                st.rerun()

        # 確認刪除帳單對話框
        if st.session_state.get('confirm_delete_bill'):
            show_delete_bill_confirmation(st.session_state.confirm_delete_bill)

    # 顯示編輯表單
    if hasattr(st.session_state, 'editing_rent_record') and st.session_state.editing_rent_record:
        show_edit_rent_record_form(st.session_state.editing_rent_record)

    # 創建新租金記錄按鈕
    if st.button("➕ 創建新租金記錄", key="create_new_rent", use_container_width=True):
        st.session_state.show_create_rent_form = True
        st.rerun()

    # 顯示創建租金記錄表單
    if st.session_state.get('show_create_rent_form', False):
        st.subheader("創建新租金記錄")
        show_create_rent_record_form(datetime.now().year, datetime.now().month)


def show_utility_bills(query_year: int, query_month: int):
    """顯示水電費帳單"""
    # 獲取水電費帳單資料
    bills = api_client.get_utility_bills(query_year, query_month)

    if not bills:
        st.info(f"{query_year}年{query_month}月暫無水電費帳單")
        return

    # 顯示帳單列表
    display_data = []
    for bill in bills:
        room_info = bill.get('room', {})
        display_data.append({
            "房間號": room_info.get('room_number', 'N/A'),
            "用電量": f"{bill['electricity_usage']:.1f} 度",
            "電費": format_currency(bill['electricity_cost']),
            "水費": format_currency(bill['water_fee']),
            "總費用": format_currency(bill['total_amount']),
            "付款狀態": f"{get_payment_status_color(bill['payment_status'])} {bill['payment_status']}",
            "付款日期": format_date_roc(bill['payment_date'], "short") if bill['payment_date'] else 'N/A'
        })

    df = pd.DataFrame(display_data)
    st.dataframe(df, use_container_width=True)

    # 帳單操作
    st.subheader("水電費帳單操作")

    selected_bill_idx = st.selectbox(
        "選擇水電費帳單",
        options=range(len(bills)),
        format_func=lambda x: f"{x+1}. {bills[x].get('room', {}).get('room_number', 'N/A')} - {format_currency(bills[x]['total_amount'])}",
        key="utility_bill_select"
    )

    if selected_bill_idx is not None:
        selected_bill = bills[selected_bill_idx]

        col1, col2 = st.columns(2)

        with col1:
            if st.button("💳 標記已付款", key="utilities_mark_paid", use_container_width=True):
                update_utility_payment_status(selected_bill['id'], "paid", datetime.now().isoformat())

        with col2:
            if st.button("⏰ 標記逾期", key="utilities_mark_overdue", use_container_width=True):
                update_utility_payment_status(selected_bill['id'], "overdue")

def show_rent_bills(query_year: int, query_month: int):
    """顯示租金帳單"""
    # 獲取租金記錄
    rent_records = api_client.get_rent_records(year=query_year, month=query_month)

    if not rent_records:
        st.info(f"{query_year}年{query_month}月暫無租金記錄")

        # 顯示創建租金記錄的選項
        st.subheader("創建租金記錄")
        show_create_rent_record_form(query_year, query_month)
        return

    # 顯示租金記錄列表
    display_data = []
    for record in rent_records:
        room_info = record.get('room', {})
        resident_info = record.get('resident', {})
        display_data.append({
            "房間號": room_info.get('room_number', 'N/A'),
            "住戶": resident_info.get('name', 'N/A'),
            "租金金額": format_currency(record['rent_amount']),
            "付款狀態": f"{get_payment_status_color(record['payment_status'])} {record['payment_status']}",
            "到期日期": format_date_roc(record['due_date'], "short"),
            "付款日期": format_date_roc(record['payment_date'], "short") if record['payment_date'] else 'N/A',
            "備註": record.get('notes', '') or 'N/A'
        })

    df = pd.DataFrame(display_data)
    st.dataframe(df, use_container_width=True)

    # 租金記錄操作
    st.subheader("租金記錄操作")

    selected_rent_idx = st.selectbox(
        "選擇租金記錄",
        options=range(len(rent_records)),
        format_func=lambda x: f"{x+1}. {rent_records[x].get('room', {}).get('room_number', 'N/A')} - {format_currency(rent_records[x]['rent_amount'])}",
        key="rent_record_select"
    )

    if selected_rent_idx is not None:
        selected_record = rent_records[selected_rent_idx]

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("💳 標記已付款", key="rent_mark_paid", use_container_width=True):
                update_rent_payment_status(selected_record['id'], "已付款", datetime.now().isoformat())

        with col2:
            if st.button("⏰ 標記逾期", key="rent_mark_overdue", use_container_width=True):
                update_rent_payment_status(selected_record['id'], "逾期")

        with col3:
            if st.button("✏️ 編輯記錄", key="rent_edit", use_container_width=True):
                st.session_state.editing_rent_record = selected_record
                st.rerun()

    # 顯示編輯表單
    if hasattr(st.session_state, 'editing_rent_record') and st.session_state.editing_rent_record:
        show_edit_rent_record_form(st.session_state.editing_rent_record)

    # 創建新租金記錄
    st.subheader("創建新租金記錄")
    show_create_rent_record_form(query_year, query_month)

def update_utility_payment_status(bill_id: int, status: str, payment_date: str = None):
    """更新水電費付款狀態"""
    with st.spinner("更新中..."):
        result = api_client.update_payment_status(bill_id, status, payment_date)

        if result:
            show_success_message("水電費付款狀態更新成功！")
            st.rerun()

def update_rent_payment_status(record_id: int, status: str, payment_date: str = None):
    """更新租金付款狀態"""
    with st.spinner("更新中..."):
        update_data = {"payment_status": status}
        if payment_date:
            update_data["payment_date"] = payment_date

        result = api_client.update_rent_record(record_id, update_data)

        if result:
            show_success_message("租金付款狀態更新成功！")
            st.rerun()

def calculate_due_date(move_in_date: str, rent_year: int, rent_month: int) -> date:
    """計算租金到期日（入住日前一天）"""
    from datetime import datetime, date
    import calendar

    try:
        # 解析入住日期
        move_in = datetime.fromisoformat(move_in_date.replace('Z', '+00:00')).date()
        move_in_day = move_in.day

        # 計算到期日（入住日前一天）
        due_day = move_in_day - 1

        # 如果入住日是1號，到期日是上個月的最後一天
        if due_day <= 0:
            if rent_month == 1:
                due_year = rent_year - 1
                due_month = 12
            else:
                due_year = rent_year
                due_month = rent_month - 1

            # 獲取上個月的最後一天
            _, last_day = calendar.monthrange(due_year, due_month)
            due_day = last_day
        else:
            due_year = rent_year
            due_month = rent_month

            # 確保到期日不超過該月的最後一天
            _, last_day = calendar.monthrange(due_year, due_month)
            due_day = min(due_day, last_day)

        return date(due_year, due_month, due_day)

    except Exception as e:
        # 如果計算失敗，返回該月5號作為預設
        return date(rent_year, rent_month, 5)

def show_create_rent_record_form(default_year: int, default_month: int):
    """顯示創建租金記錄表單"""
    with st.form("create_rent_record"):
        col1, col2 = st.columns(2)

        with col1:
            # 獲取房間列表
            rooms = api_client.get_rooms()
            if not rooms:
                st.error("無可用房間")
                return

            room_options = {f"{room['room_number']} (樓層: {room.get('floor', 'N/A')})": room['id'] for room in rooms}
            selected_room_display = st.selectbox("選擇房間*", options=list(room_options.keys()))
            selected_room_id = room_options[selected_room_display]

            # 獲取該房間的住戶
            residents = api_client.get_residents()
            if residents:
                room_residents = [r for r in residents if r.get('room_id') == selected_room_id and r.get('is_active')]
                if room_residents:
                    resident_options = {f"{resident['name']} ({resident['phone']})": resident['id'] for resident in room_residents}
                    selected_resident_display = st.selectbox("選擇住戶*", options=list(resident_options.keys()))
                    selected_resident_id = resident_options[selected_resident_display]

                    # 獲取選中的住戶資訊
                    selected_resident = next(r for r in room_residents if r['id'] == selected_resident_id)
                else:
                    st.warning("該房間暫無住戶")
                    return
            else:
                st.error("無住戶資料")
                return

        with col2:
            rent_year = st.number_input("租金年份*", min_value=2020, max_value=2030, value=default_year)
            rent_month = st.number_input("租金月份*", min_value=1, max_value=12, value=default_month)

            # 獲取房間租金
            selected_room = next(room for room in rooms if room['id'] == selected_room_id)
            default_rent = selected_room.get('current_rent', 0)

            rent_amount = st.number_input("租金金額*", min_value=0.0, value=float(default_rent), step=100.0)

            # 自動計算到期日
            if selected_resident.get('move_in_date'):
                calculated_due_date = calculate_due_date(
                    selected_resident['move_in_date'],
                    rent_year,
                    rent_month
                )
                st.info(f"💡 根據入住日期自動計算到期日: {format_date_roc(calculated_due_date, 'full')}")
                due_date = roc_calendar_input("到期日期*", value=calculated_due_date, help="租金到期日期", key="rent_due_date_calculated")
            else:
                due_date = roc_calendar_input("到期日期*", value=datetime(rent_year, rent_month, 5).date(), help="租金到期日期", key="rent_due_date_default")

        notes = st.text_area("備註", placeholder="租金相關說明")

        submitted = st.form_submit_button("💰 創建租金記錄", use_container_width=True)

        if submitted:
            if rent_amount <= 0:
                show_error_message("租金金額必須大於0")
            else:
                rent_data = {
                    "room_id": selected_room_id,
                    "resident_id": selected_resident_id,
                    "rent_year": rent_year,
                    "rent_month": rent_month,
                    "rent_amount": rent_amount,
                    "due_date": format_date_for_api(due_date),
                    "notes": notes if notes else None
                }

                with st.spinner("創建中..."):
                    result = api_client.create_rent_record(rent_data)

                    if result:
                        show_success_message("租金記錄創建成功！")
                        st.rerun()

def show_edit_rent_record_form(record: dict):
    """顯示編輯租金記錄表單"""
    st.subheader("編輯租金記錄")

    with st.form("edit_rent_record"):
        col1, col2 = st.columns(2)

        with col1:
            rent_amount = st.number_input(
                "租金金額*",
                min_value=0.0,
                value=float(record['rent_amount']),
                step=100.0
            )

            payment_status = st.selectbox(
                "付款狀態*",
                options=["待付款", "已付款", "逾期"],
                index=["待付款", "已付款", "逾期"].index(record['payment_status'])
            )

        with col2:
            payment_date = None
            if record.get('payment_date'):
                try:
                    payment_date = datetime.fromisoformat(record['payment_date'].replace('Z', '+00:00')).date()
                except:
                    payment_date = None

            new_payment_date = roc_calendar_input("付款日期", value=payment_date, key=f"edit_payment_date_{record.get('id', 'unknown')}")

        notes = st.text_area("備註", value=record.get('notes', '') or '')

        col1, col2 = st.columns(2)

        with col1:
            submitted = st.form_submit_button("💾 更新記錄", use_container_width=True)

        with col2:
            cancelled = st.form_submit_button("❌ 取消", use_container_width=True)

        if cancelled:
            st.session_state.editing_rent_record = None
            st.rerun()

        if submitted:
            if rent_amount <= 0:
                show_error_message("租金金額必須大於0")
            else:
                update_data = {
                    "rent_amount": rent_amount,
                    "payment_status": payment_status,
                    "payment_date": new_payment_date.isoformat() if new_payment_date else None,
                    "notes": notes if notes else None
                }

                with st.spinner("更新中..."):
                    result = api_client.update_rent_record(record['id'], update_data)

                    if result:
                        show_success_message("租金記錄更新成功！")
                        st.session_state.editing_rent_record = None
                        st.rerun()

def show_utilities_statistics():
    """顯示費用統計"""
    st.subheader("費用統計")
    
    # 選擇統計月份
    col1, col2 = st.columns(2)
    
    with col1:
        stats_year = st.number_input("統計年份", min_value=2020, max_value=2030, value=datetime.now().year)
    
    with col2:
        stats_month = st.number_input("統計月份", min_value=1, max_value=12, value=datetime.now().month)
    
    # 獲取收入統計
    income_stats = api_client.get_income_summary(stats_year, stats_month)
    
    if not income_stats:
        st.info("暫無統計資料")
        return
    
    # 顯示統計指標
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("租金收入", format_currency(income_stats['total_rent']))
        st.metric("公用事業費", format_currency(income_stats['total_utilities']))
    
    with col2:
        st.metric("總收入", format_currency(income_stats['total_income']))
        st.metric("已收款", format_currency(income_stats['paid_amount']))
    
    with col3:
        st.metric("待收款", format_currency(income_stats['pending_amount']))
        
        # 收款率
        collection_rate = (income_stats['paid_amount'] / income_stats['total_utilities'] * 100) if income_stats['total_utilities'] > 0 else 0
        st.metric("收款率", f"{collection_rate:.1f}%")
    
    # 圖表展示
    import plotly.express as px
    
    # 收入結構圓餅圖
    fig = px.pie(
        values=[income_stats['total_rent'], income_stats['total_utilities']],
        names=['租金收入', '公用事業費'],
        title=f"{stats_year}年{stats_month}月收入結構"
    )
    st.plotly_chart(fig, use_container_width=True)
    
    # 付款狀態圓餅圖
    fig2 = px.pie(
        values=[income_stats['paid_amount'], income_stats['pending_amount']],
        names=['已付款', '待付款'],
        title="付款狀態分布"
    )
    st.plotly_chart(fig2, use_container_width=True)

def show_edit_rate_form(rate_data):
    """顯示編輯費率表單"""
    st.subheader(f"編輯費率 - ID: {rate_data['id']}")

    with st.form("edit_rate_form"):
        col1, col2 = st.columns(2)

        with col1:
            electricity_rate = st.number_input(
                "電費每度 (元)*",
                min_value=0.0,
                value=rate_data['electricity_rate'],
                step=0.1,
                help="每度電的費用"
            )

        with col2:
            monthly_water_fee = st.number_input(
                "月度水費 (元)*",
                min_value=0.0,
                value=rate_data['monthly_water_fee'],
                step=10.0,
                help="每月固定水費（不論房間住戶人數，統一收費100元）"
            )

        notes = st.text_area("備註", value=rate_data.get('notes', ''), help="費率說明或備註")

        col1, col2 = st.columns(2)

        with col1:
            submitted = st.form_submit_button("💾 更新費率", use_container_width=True)

        with col2:
            if st.form_submit_button("❌ 取消", use_container_width=True):
                st.session_state.show_edit_form = False
                st.session_state.editing_rate = None
                st.rerun()

        if submitted:
            if electricity_rate <= 0 or monthly_water_fee <= 0:
                show_error_message("費率必須大於0")
            else:
                update_data = {
                    "electricity_rate": electricity_rate,
                    "monthly_water_fee": monthly_water_fee,
                    "notes": notes if notes else None
                }

                with st.spinner("更新中..."):
                    result = api_client.update_utility_rate(rate_data['id'], update_data)

                    if result:
                        show_success_message("費率更新成功！")
                        st.session_state.show_edit_form = False
                        st.session_state.editing_rate = None
                        st.rerun()

def show_clear_historical_rates_confirmation():
    """顯示清除歷史費率確認對話框"""
    st.markdown("---")
    st.warning("⚠️ 確認清除歷史費率")
    st.write("此操作將：")
    st.write("- 保留當前活躍費率")
    st.write("- 刪除所有歷史費率記錄")
    st.write("- **此操作無法復原**")

    col1, col2 = st.columns(2)

    with col1:
        if st.button("✅ 確認清除", key="confirm_clear_historical_yes", use_container_width=True):
            with st.spinner("清除中..."):
                result = api_client.clear_all_utility_rates(keep_current=True)
                if result:
                    show_success_message(f"成功清除 {result.get('deleted_count', 0)} 筆歷史費率記錄！")
                    st.session_state.confirm_clear_historical_rates = None
                    st.rerun()

    with col2:
        if st.button("❌ 取消", key="confirm_clear_historical_no", use_container_width=True):
            st.session_state.confirm_clear_historical_rates = None
            st.rerun()

def show_clear_all_rates_confirmation():
    """顯示清除所有費率確認對話框"""
    st.markdown("---")
    st.error("🚨 確認清除所有費率")
    st.write("此操作將：")
    st.write("- 刪除所有費率記錄（包括當前費率）")
    st.write("- 創建一個預設費率（電費 $5.5/度，水費 $200/月）")
    st.write("- **此操作無法復原**")

    col1, col2 = st.columns(2)

    with col1:
        if st.button("✅ 確認清除", key="confirm_clear_all_yes", use_container_width=True):
            with st.spinner("清除中..."):
                result = api_client.clear_all_utility_rates(keep_current=False)
                if result:
                    show_success_message(f"成功清除 {result.get('deleted_count', 0)} 筆費率記錄並創建預設費率！")
                    st.session_state.confirm_clear_all_rates = None
                    st.rerun()

    with col2:
        if st.button("❌ 取消", key="confirm_clear_all_no", use_container_width=True):
            st.session_state.confirm_clear_all_rates = None
            st.rerun()

def show_clear_all_bills_confirmation():
    """顯示清除所有帳單確認對話框"""
    st.markdown("---")
    st.error("🚨 確認清除所有帳單記錄")
    st.write("此操作將：")
    st.write("- 刪除所有水電費帳單記錄")
    st.write("- **此操作無法復原**")
    st.write("- 租金記錄不會被影響")

    col1, col2 = st.columns(2)

    with col1:
        if st.button("✅ 確認清除", key="confirm_clear_bills_yes", use_container_width=True):
            with st.spinner("清除中..."):
                result = api_client.clear_all_utility_bills()
                if result:
                    show_success_message(f"成功清除 {result.get('deleted_count', 0)} 筆帳單記錄！")
                    st.session_state.confirm_clear_all_bills = None
                    st.rerun()

    with col2:
        if st.button("❌ 取消", key="confirm_clear_bills_no", use_container_width=True):
            st.session_state.confirm_clear_all_bills = None
            st.rerun()

def show_delete_bill_confirmation(bill_info: dict):
    """顯示刪除個別帳單確認對話框"""
    st.markdown("---")
    st.warning("⚠️ 確認刪除帳單")
    st.write(f"確定要刪除以下帳單嗎？")
    st.write(f"**類型：** {'水電費' if bill_info['type'] == 'utility' else '租金'}")
    st.write(f"**帳單：** {bill_info['info']}")
    st.write("**此操作無法復原**")

    col1, col2 = st.columns(2)

    with col1:
        if st.button("✅ 確認刪除", key="confirm_delete_bill_yes", use_container_width=True):
            with st.spinner("刪除中..."):
                if bill_info['type'] == 'utility':
                    result = api_client.delete_utility_bill(bill_info['id'])
                else:
                    # 租金記錄刪除（如果需要的話）
                    result = api_client.delete_rent_record(bill_info['id'])

                if result:
                    show_success_message("帳單刪除成功！")
                    st.session_state.confirm_delete_bill = None
                    st.rerun()

    with col2:
        if st.button("❌ 取消", key="confirm_delete_bill_no", use_container_width=True):
            st.session_state.confirm_delete_bill = None
            st.rerun()
