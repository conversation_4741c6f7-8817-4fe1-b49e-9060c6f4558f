#!/usr/bin/env python3
"""測試編輯綜合帳單功能"""

import os
import sys

def test_edit_comprehensive_bill_form():
    """測試編輯綜合帳單表單功能"""
    print("🔧 測試編輯綜合帳單表單功能...")
    
    utilities_file = 'frontend/pages/utilities.py'
    
    if os.path.exists(utilities_file):
        with open(utilities_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"   檢查 {utilities_file}:")
        
        # 檢查是否包含新增的費用明細欄位
        required_fields = [
            'edit_rent_amount',
            'edit_water_fee', 
            'edit_current_reading',
            'edit_previous_reading',
            'edit_electricity_rate'
        ]
        
        all_fields_present = True
        for field in required_fields:
            if field in content:
                print(f"      ✅ {field}: 已添加")
            else:
                print(f"      ❌ {field}: 未找到")
                all_fields_present = False
        
        # 檢查是否包含費用計算邏輯
        calculation_logic = [
            'electricity_usage = current_electricity_reading - previous_electricity_reading',
            'electricity_cost = electricity_usage * electricity_rate',
            'total_amount = rent_amount + water_fee + electricity_cost'
        ]
        
        for logic in calculation_logic:
            if logic in content:
                print(f"      ✅ 費用計算邏輯: {logic[:30]}... 已實作")
            else:
                print(f"      ❌ 費用計算邏輯: {logic[:30]}... 未實作")
                all_fields_present = False
        
        # 檢查是否包含表單驗證
        validation_checks = [
            'current_electricity_reading < previous_electricity_reading',
            'rent_amount <= 0',
            'electricity_rate <= 0'
        ]
        
        for check in validation_checks:
            if check in content:
                print(f"      ✅ 表單驗證: {check[:30]}... 已實作")
            else:
                print(f"      ❌ 表單驗證: {check[:30]}... 未實作")
                all_fields_present = False
        
        return all_fields_present
    else:
        print(f"   ❌ {utilities_file} 不存在")
        return False

def test_api_client_update_method():
    """測試API客戶端更新方法"""
    print("🔗 測試API客戶端更新方法...")
    
    api_client_file = 'frontend/api_client.py'
    
    if os.path.exists(api_client_file):
        with open(api_client_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"   檢查 {api_client_file}:")
        
        # 檢查是否包含update_comprehensive_bill方法
        if 'def update_comprehensive_bill(' in content:
            print(f"      ✅ update_comprehensive_bill方法: 已添加")
        else:
            print(f"      ❌ update_comprehensive_bill方法: 未添加")
            return False
        
        # 檢查方法實現
        method_components = [
            'self.session.put(',
            'comprehensive-bills/{bill_id}',
            'json=update_data'
        ]
        
        for component in method_components:
            if component in content:
                print(f"      ✅ 方法組件: {component} 已實作")
            else:
                print(f"      ❌ 方法組件: {component} 未實作")
                return False
        
        return True
    else:
        print(f"   ❌ {api_client_file} 不存在")
        return False

def test_form_structure():
    """測試表單結構"""
    print("📝 測試表單結構...")
    
    utilities_file = 'frontend/pages/utilities.py'
    
    if os.path.exists(utilities_file):
        with open(utilities_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"   檢查 {utilities_file}:")
        
        # 檢查是否包含區塊標題
        section_titles = [
            '### 💰 費用明細',
            '### 💳 付款資訊'
        ]
        
        for title in section_titles:
            if title in content:
                print(f"      ✅ 區塊標題: {title} 已添加")
            else:
                print(f"      ❌ 區塊標題: {title} 未添加")
                return False
        
        # 檢查是否包含即時計算顯示
        metrics = [
            'st.metric("用電量"',
            'st.metric("電費"',
            'st.metric("總金額"'
        ]
        
        for metric in metrics:
            if metric in content:
                print(f"      ✅ 即時計算顯示: {metric} 已添加")
            else:
                print(f"      ❌ 即時計算顯示: {metric} 未添加")
                return False
        
        return True
    else:
        print(f"   ❌ {utilities_file} 不存在")
        return False

def test_error_handling():
    """測試錯誤處理"""
    print("⚠️ 測試錯誤處理...")
    
    utilities_file = 'frontend/pages/utilities.py'
    
    if os.path.exists(utilities_file):
        with open(utilities_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"   檢查 {utilities_file}:")
        
        # 檢查錯誤訊息
        error_messages = [
            '當前電表讀數不能小於前期讀數',
            '租金金額必須大於0',
            '電費費率必須大於0'
        ]
        
        for message in error_messages:
            if message in content:
                print(f"      ✅ 錯誤訊息: {message} 已添加")
            else:
                print(f"      ❌ 錯誤訊息: {message} 未添加")
                return False
        
        # 檢查備用處理邏輯
        if 'update_comprehensive_bill(bill[\'id\'], update_data)' in content:
            print(f"      ✅ 主要更新邏輯: 已實作")
        else:
            print(f"      ❌ 主要更新邏輯: 未實作")
            return False
        
        if 'update_payment_status(' in content:
            print(f"      ✅ 備用更新邏輯: 已實作")
        else:
            print(f"      ❌ 備用更新邏輯: 未實作")
            return False
        
        return True
    else:
        print(f"   ❌ {utilities_file} 不存在")
        return False

if __name__ == "__main__":
    print("🧪 編輯綜合帳單功能測試")
    print("=" * 60)
    
    # 1. 測試編輯表單功能
    form_test = test_edit_comprehensive_bill_form()
    
    # 2. 測試API客戶端更新方法
    api_test = test_api_client_update_method()
    
    # 3. 測試表單結構
    structure_test = test_form_structure()
    
    # 4. 測試錯誤處理
    error_handling_test = test_error_handling()
    
    print("\n" + "=" * 60)
    print("📝 測試結果總結:")
    print("1. ✅ 編輯表單功能" if form_test else "1. ❌ 編輯表單功能")
    print("2. ✅ API客戶端更新方法" if api_test else "2. ❌ API客戶端更新方法")
    print("3. ✅ 表單結構" if structure_test else "3. ❌ 表單結構")
    print("4. ✅ 錯誤處理" if error_handling_test else "4. ❌ 錯誤處理")
    
    all_passed = all([form_test, api_test, structure_test, error_handling_test])
    
    print("\n💡 新增功能總結:")
    print("🔧 編輯綜合帳單增強功能:")
    print("   - 可修改租金金額")
    print("   - 可修改水費金額")
    print("   - 可修改當前電表讀數")
    print("   - 可修改前期電表讀數")
    print("   - 可修改電費費率")
    print("   - 即時計算用電量、電費和總金額")
    
    print("\n📝 表單改進:")
    print("   - 分為費用明細和付款資訊兩個區塊")
    print("   - 提供即時的費用計算預覽")
    print("   - 完整的表單驗證機制")
    print("   - 清楚的錯誤提示訊息")
    
    print("\n🔗 API增強:")
    print("   - 新增update_comprehensive_bill方法")
    print("   - 支援完整的帳單數據更新")
    print("   - 提供備用的付款狀態更新機制")
    print("   - 完善的錯誤處理")
    
    print("\n⚠️ 驗證機制:")
    print("   - 電表讀數邏輯驗證")
    print("   - 金額正數驗證")
    print("   - 付款日期必填驗證")
    print("   - 到期日必填驗證")
    
    print(f"\n🏁 測試完成 - {'全部通過' if all_passed else '部分失敗'}")
    
    if all_passed:
        print("\n🎉 編輯綜合帳單功能增強完成！")
        print("   現在可以修改電量、租金和所有相關費用項目")
        print("   提供完整的表單驗證和即時計算功能")
        print("   支援安全的數據更新和錯誤處理")
    else:
        print("\n⚠️ 部分功能需要進一步檢查和調整")
