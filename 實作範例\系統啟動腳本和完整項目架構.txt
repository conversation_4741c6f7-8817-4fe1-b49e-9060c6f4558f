# =================== start_rental_system.py ===================
#!/usr/bin/env python3
"""
租房管理系統一鍵啟動腳本
自動安裝依賴、初始化資料庫、啟動前後端服務
"""

import os
import sys
import subprocess
import threading
import time
import signal
import webbrowser
from pathlib import Path
import platform
import requests

class Color:
    """終端顏色輔助類"""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

class RentalSystemManager:
    """租房管理系統管理器"""
    
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.running = False
        self.base_dir = Path(__file__).parent
        self.backend_dir = self.base_dir / "backend"
        self.frontend_dir = self.base_dir / "frontend"
        
    def print_banner(self):
        """顯示系統橫幅"""
        banner = f"""
{Color.OKGREEN}{Color.BOLD}
╔═══════════════════════════════════════════════════════════════╗
║                    🏠 租房管理系統                             ║
║                  Rental Management System                    ║
║                                                              ║
║              基於 FastAPI + Streamlit 構建                   ║
║                     Version 1.0.0                          ║
╚═══════════════════════════════════════════════════════════════╝
{Color.ENDC}
        """
        print(banner)
    
    def check_system_requirements(self):
        """檢查系統要求"""
        print(f"{Color.OKBLUE}🔍 檢查系統要求...{Color.ENDC}")
        
        # 檢查Python版本
        python_version = sys.version_info
        if python_version < (3, 9):
            print(f"{Color.FAIL}❌ 需要Python 3.9或更高版本，目前版本: {python_version.major}.{python_version.minor}{Color.ENDC}")
            return False
        
        print(f"{Color.OKGREEN}✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}{Color.ENDC}")
        
        # 檢查pip
        try:
            subprocess.run([sys.executable, "-m", "pip", "--version"], 
                         capture_output=True, check=True)
            print(f"{Color.OKGREEN}✅ pip已安裝{Color.ENDC}")
        except subprocess.CalledProcessError:
            print(f"{Color.FAIL}❌ pip未安裝或無法使用{Color.ENDC}")
            return False
        
        # 檢查項目結構
        required_dirs = [self.backend_dir, self.frontend_dir]
        for dir_path in required_dirs:
            if not dir_path.exists():
                print(f"{Color.FAIL}❌ 缺少目錄: {dir_path}{Color.ENDC}")
                return False
        
        print(f"{Color.OKGREEN}✅ 項目結構完整{Color.ENDC}")
        return True
    
    def install_dependencies(self):
        """安裝依賴套件"""
        print(f"{Color.OKBLUE}📦 安裝依賴套件...{Color.ENDC}")
        
        try:
            # 安裝後端依賴
            print(f"{Color.OKCYAN}  安裝後端依賴...{Color.ENDC}")
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", 
                str(self.backend_dir / "requirements.txt")
            ], check=True, capture_output=True)
            
            # 安裝前端依賴
            print(f"{Color.OKCYAN}  安裝前端依賴...{Color.ENDC}")
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", 
                str(self.frontend_dir / "requirements.txt")
            ], check=True, capture_output=True)
            
            print(f"{Color.OKGREEN}✅ 依賴安裝完成{Color.ENDC}")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"{Color.FAIL}❌ 依賴安裝失敗: {e}{Color.ENDC}")
            return False
    
    def setup_environment(self):
        """設置環境"""
        print(f"{Color.OKBLUE}🔧 設置環境...{Color.ENDC}")
        
        # 創建後端環境配置
        backend_env = self.backend_dir / ".env"
        if not backend_env.exists():
            env_content = """DATABASE_URL=sqlite:///./rental_management.db
SECRET_KEY=rental-management-secret-key-change-in-production-2024
DEBUG=True
ACCESS_TOKEN_EXPIRE_MINUTES=30
DEFAULT_ELECTRICITY_RATE=5.5
DEFAULT_WATER_FEE=200.0
"""
            with open(backend_env, "w", encoding="utf-8") as f:
                f.write(env_content)
            print(f"{Color.OKGREEN}✅ 後端環境配置已創建{Color.ENDC}")
        
        # 初始化資料庫
        print(f"{Color.OKCYAN}  初始化資料庫...{Color.ENDC}")
        try:
            subprocess.run([
                sys.executable, "init_db.py"
            ], cwd=self.backend_dir, check=True, capture_output=True)
            print(f"{Color.OKGREEN}✅ 資料庫初始化完成{Color.ENDC}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"{Color.FAIL}❌ 資料庫初始化失敗: {e}{Color.ENDC}")
            return False
    
    def start_backend(self):
        """啟動後端服務"""
        print(f"{Color.OKBLUE}🚀 啟動後端API服務...{Color.ENDC}")
        
        try:
            self.backend_process = subprocess.Popen([
                sys.executable, "run_server.py"
            ], cwd=self.backend_dir, 
               stdout=subprocess.PIPE, 
               stderr=subprocess.STDOUT,
               universal_newlines=True)
            
            # 等待後端啟動
            for i in range(30):  # 最多等待30秒
                try:
                    response = requests.get("http://localhost:8000/health", timeout=1)
                    if response.status_code == 200:
                        print(f"{Color.OKGREEN}✅ 後端服務啟動成功 (http://localhost:8000){Color.ENDC}")
                        return True
                except requests.RequestException:
                    pass
                
                time.sleep(1)
                print(f"{Color.OKCYAN}  等待後端服務啟動... ({i+1}/30){Color.ENDC}")
            
            print(f"{Color.FAIL}❌ 後端服務啟動超時{Color.ENDC}")
            return False
            
        except Exception as e:
            print(f"{Color.FAIL}❌ 後端服務啟動失敗: {e}{Color.ENDC}")
            return False
    
    def start_frontend(self):
        """啟動前端服務"""
        print(f"{Color.OKBLUE}🚀 啟動前端界面...{Color.ENDC}")
        
        try:
            # 設置環境變數
            env = os.environ.copy()
            env['STREAMLIT_SERVER_HEADLESS'] = 'true'
            env['STREAMLIT_SERVER_PORT'] = '8501'
            
            self.frontend_process = subprocess.Popen([
                sys.executable, "-m", "streamlit", "run", "app.py",
                "--server.port", "8501",
                "--server.headless", "true",
                "--server.fileWatcherType", "none",
                "--browser.gatherUsageStats", "false"
            ], cwd=self.frontend_dir,
               env=env,
               stdout=subprocess.PIPE,
               stderr=subprocess.STDOUT,
               universal_newlines=True)
            
            # 等待前端啟動
            for i in range(30):  # 最多等待30秒
                try:
                    response = requests.get("http://localhost:8501", timeout=1)
                    if response.status_code == 200:
                        print(f"{Color.OKGREEN}✅ 前端服務啟動成功 (http://localhost:8501){Color.ENDC}")
                        return True
                except requests.RequestException:
                    pass
                
                time.sleep(1)
                print(f"{Color.OKCYAN}  等待前端服務啟動... ({i+1}/30){Color.ENDC}")
            
            print(f"{Color.FAIL}❌ 前端服務啟動超時{Color.ENDC}")
            return False
            
        except Exception as e:
            print(f"{Color.FAIL}❌ 前端服務啟動失敗: {e}{Color.ENDC}")
            return False
    
    def open_browser(self):
        """打開瀏覽器"""
        try:
            webbrowser.open("http://localhost:8501")
            print(f"{Color.OKGREEN}🌐 瀏覽器已打開{Color.ENDC}")
        except Exception as e:
            print(f"{Color.WARNING}⚠️ 無法自動打開瀏覽器: {e}{Color.ENDC}")
    
    def show_success_info(self):
        """顯示成功資訊"""
        success_info = f"""
{Color.OKGREEN}{Color.BOLD}
🎉 系統啟動成功！
{Color.ENDC}

{Color.OKBLUE}📍 訪問地址:{Color.ENDC}
  • 前端界面: http://localhost:8501
  • API文檔:  http://localhost:8000/docs
  • 健康檢查: http://localhost:8000/health

{Color.OKBLUE}🔑 預設帳號:{Color.ENDC}
  • 用戶名: admin
  • 密碼: admin123
  • 角色: 系統管理員

{Color.OKBLUE}📋 主要功能:{Color.ENDC}
  • 🏠 房間管理 - 房間資訊維護
  • 👥 住戶管理 - 入住退房管理  
  • 💰 費用管理 - 電費水費計算
  • 📊 統計報表 - 數據分析展示

{Color.WARNING}⚠️ 按 Ctrl+C 停止系統{Color.ENDC}
        """
        print(success_info)
    
    def signal_handler(self, signum, frame):
        """信號處理器"""
        print(f"\n{Color.WARNING}⏹️ 接收到停止信號，正在關閉系統...{Color.ENDC}")
        self.shutdown()
        sys.exit(0)
    
    def shutdown(self):
        """關閉系統"""
        print(f"{Color.OKBLUE}🛑 正在停止系統服務...{Color.ENDC}")
        
        if self.backend_process:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=10)
                print(f"{Color.OKGREEN}✅ 後端服務已停止{Color.ENDC}")
            except subprocess.TimeoutExpired:
                self.backend_process.kill()
                print(f"{Color.WARNING}⚠️ 強制停止後端服務{Color.ENDC}")
        
        if self.frontend_process:
            try:
                self.frontend_process.terminate()
                self.frontend_process.wait(timeout=10)
                print(f"{Color.OKGREEN}✅ 前端服務已停止{Color.ENDC}")
            except subprocess.TimeoutExpired:
                self.frontend_process.kill()
                print(f"{Color.WARNING}⚠️ 強制停止前端服務{Color.ENDC}")
        
        self.running = False
        print(f"{Color.OKGREEN}✅ 系統已完全停止{Color.ENDC}")
    
    def monitor_services(self):
        """監控服務狀態"""
        while self.running:
            time.sleep(5)
            
            # 檢查後端服務
            if self.backend_process and self.backend_process.poll() is not None:
                print(f"{Color.FAIL}❌ 後端服務意外停止{Color.ENDC}")
                self.shutdown()
                break
            
            # 檢查前端服務
            if self.frontend_process and self.frontend_process.poll() is not None:
                print(f"{Color.FAIL}❌ 前端服務意外停止{Color.ENDC}")
                self.shutdown()
                break
    
    def run(self):
        """運行系統"""
        try:
            # 註冊信號處理器
            signal.signal(signal.SIGINT, self.signal_handler)
            signal.signal(signal.SIGTERM, self.signal_handler)
            
            # 顯示橫幅
            self.print_banner()
            
            # 檢查系統要求
            if not self.check_system_requirements():
                return False
            
            # 安裝依賴
            if not self.install_dependencies():
                return False
            
            # 設置環境
            if not self.setup_environment():
                return False
            
            # 啟動後端
            if not self.start_backend():
                return False
            
            # 啟動前端
            if not self.start_frontend():
                self.shutdown()
                return False
            
            # 設置運行狀態
            self.running = True
            
            # 顯示成功資訊
            self.show_success_info()
            
            # 打開瀏覽器
            self.open_browser()
            
            # 監控服務
            self.monitor_services()
            
            return True
            
        except Exception as e:
            print(f"{Color.FAIL}❌ 系統啟動失敗: {e}{Color.ENDC}")
            self.shutdown()
            return False

def main():
    """主程式"""
    manager = RentalSystemManager()
    
    try:
        success = manager.run()
        return 0 if success else 1
    except KeyboardInterrupt:
        print(f"\n{Color.WARNING}⏹️ 用戶中斷啟動{Color.ENDC}")
        return 0
    except Exception as e:
        print(f"{Color.FAIL}❌ 未預期的錯誤: {e}{Color.ENDC}")
        return 1

if __name__ == "__main__":
    sys.exit(main())

# =================== install_system.py ===================
#!/usr/bin/env python3
"""
租房管理系統安裝腳本
自動檢查環境、安裝依賴、配置系統
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import platform

class SystemInstaller:
    """系統安裝器"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.backend_dir = self.base_dir / "backend"
        self.frontend_dir = self.base_dir / "frontend"
        self.venv_dir = self.base_dir / "venv"
    
    def print_header(self):
        """顯示安裝標題"""
        print("""
╔═══════════════════════════════════════════════════════════════╗
║                 🏠 租房管理系統安裝程序                        ║
║                                                              ║
║              自動化安裝和配置系統環境                         ║
╚═══════════════════════════════════════════════════════════════╝
        """)
    
    def check_python_version(self):
        """檢查Python版本"""
        print("🔍 檢查Python版本...")
        
        version = sys.version_info
        if version < (3, 9):
            print(f"❌ 需要Python 3.9或更高版本，當前版本: {version.major}.{version.minor}")
            return False
        
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    
    def create_virtual_environment(self):
        """創建虛擬環境"""
        print("🔧 創建虛擬環境...")
        
        try:
            if self.venv_dir.exists():
                print("⚠️  虛擬環境已存在，跳過創建")
                return True
            
            subprocess.run([
                sys.executable, "-m", "venv", str(self.venv_dir)
            ], check=True)
            
            print("✅ 虛擬環境創建成功")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 虛擬環境創建失敗: {e}")
            return False
    
    def get_venv_python(self):
        """獲取虛擬環境Python路徑"""
        if platform.system() == "Windows":
            return str(self.venv_dir / "Scripts" / "python.exe")
        else:
            return str(self.venv_dir / "bin" / "python")
    
    def install_requirements(self):
        """安裝依賴"""
        print("📦 安裝依賴套件...")
        
        python_exe = self.get_venv_python()
        
        try:
            # 升級pip
            subprocess.run([
                python_exe, "-m", "pip", "install", "--upgrade", "pip"
            ], check=True)
            
            # 安裝後端依賴
            print("  📦 安裝後端依賴...")
            subprocess.run([
                python_exe, "-m", "pip", "install", "-r", 
                str(self.backend_dir / "requirements.txt")
            ], check=True)
            
            # 安裝前端依賴
            print("  📦 安裝前端依賴...")
            subprocess.run([
                python_exe, "-m", "pip", "install", "-r", 
                str(self.frontend_dir / "requirements.txt")
            ], check=True)
            
            print("✅ 依賴安裝完成")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 依賴安裝失敗: {e}")
            return False
    
    def setup_configuration(self):
        """設置配置文件"""
        print("⚙️  設置配置文件...")
        
        # 創建後端配置
        backend_env = self.backend_dir / ".env"
        if not backend_env.exists():
            env_content = """DATABASE_URL=sqlite:///./rental_management.db
SECRET_KEY=rental-management-secret-key-change-in-production-2024
DEBUG=True
ACCESS_TOKEN_EXPIRE_MINUTES=30
DEFAULT_ELECTRICITY_RATE=5.5
DEFAULT_WATER_FEE=200.0
"""
            with open(backend_env, "w", encoding="utf-8") as f:
                f.write(env_content)
            print("✅ 後端配置文件已創建")
        
        return True
    
    def initialize_database(self):
        """初始化資料庫"""
        print("🗄️  初始化資料庫...")
        
        try:
            python_exe = self.get_venv_python()
            subprocess.run([
                python_exe, "init_db.py"
            ], cwd=self.backend_dir, check=True)
            
            print("✅ 資料庫初始化完成")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 資料庫初始化失敗: {e}")
            return False
    
    def create_startup_scripts(self):
        """創建啟動腳本"""
        print("📝 創建啟動腳本...")
        
        # Windows批處理腳本
        if platform.system() == "Windows":
            bat_content = f"""@echo off
echo 啟動租房管理系統...
cd /d "{self.base_dir}"
"{self.get_venv_python()}" start_rental_system.py
pause
"""
            with open(self.base_dir / "start_system.bat", "w", encoding="utf-8") as f:
                f.write(bat_content)
        
        # Unix shell腳本
        else:
            sh_content = f"""#!/bin/bash
echo "啟動租房管理系統..."
cd "{self.base_dir}"
"{self.get_venv_python()}" start_rental_system.py
"""
            script_path = self.base_dir / "start_system.sh"
            with open(script_path, "w", encoding="utf-8") as f:
                f.write(sh_content)
            
            # 設置執行權限
            os.chmod(script_path, 0o755)
        
        print("✅ 啟動腳本創建完成")
        return True
    
    def run_tests(self):
        """運行測試"""
        print("🧪 運行系統測試...")
        
        try:
            python_exe = self.get_venv_python()
            
            # 安裝測試依賴
            subprocess.run([
                python_exe, "-m", "pip", "install", "pytest", "pytest-cov"
            ], check=True, capture_output=True)
            
            # 運行測試
            result = subprocess.run([
                python_exe, "-m", "pytest", "tests/", "-v"
            ], cwd=self.backend_dir, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 所有測試通過")
            else:
                print("⚠️  部分測試失敗，但系統仍可正常運行")
            
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"⚠️  測試執行失敗: {e}")
            return True  # 測試失敗不影響安裝
    
    def show_completion_message(self):
        """顯示完成訊息"""
        start_command = "start_system.bat" if platform.system() == "Windows" else "./start_system.sh"
        
        completion_message = f"""
🎉 安裝完成！

📋 啟動方式:
  方式1: 雙擊執行 {start_command}
  方式2: 執行 python start_rental_system.py

🌐 訪問地址:
  前端界面: http://localhost:8501
  API文檔:  http://localhost:8000/docs

🔑 預設帳號:
  用戶名: admin
  密碼: admin123

📁 項目結構:
  backend/     - 後端API服務
  frontend/    - 前端界面
  venv/        - 虛擬環境
  tests/       - 測試文件

📞 技術支援:
  如需幫助，請查看 README.md 文件
        """
        
        print(completion_message)
    
    def install(self):
        """執行安裝"""
        try:
            self.print_header()
            
            # 檢查Python版本
            if not self.check_python_version():
                return False
            
            # 創建虛擬環境
            if not self.create_virtual_environment():
                return False
            
            # 安裝依賴
            if not self.install_requirements():
                return False
            
            # 設置配置
            if not self.setup_configuration():
                return False
            
            # 初始化資料庫
            if not self.initialize_database():
                return False
            
            # 創建啟動腳本
            if not self.create_startup_scripts():
                return False
            
            # 運行測試
            self.run_tests()
            
            # 顯示完成訊息
            self.show_completion_message()
            
            return True
            
        except Exception as e:
            print(f"❌ 安裝失敗: {e}")
            return False

def main():
    """主程式"""
    installer = SystemInstaller()
    
    try:
        success = installer.install()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n⏹️  安裝已取消")
        return 0
    except Exception as e:
        print(f"❌ 安裝程序錯誤: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())

# =================== backend/run_server.py ===================
#!/usr/bin/env python3
"""
後端服務啟動腳本
"""

import uvicorn
from app.config import settings
import logging
import sys

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def main():
    """主程式"""
    try:
        logger.info("啟動租房管理系統後端服務...")
        logger.info(f"配置: DEBUG={settings.debug}")
        
        uvicorn.run(
            "app.main:app",
            host="0.0.0.0",
            port=8000,
            reload=settings.debug,
            log_level="info" if settings.debug else "warning",
            access_log=settings.debug
        )
        
    except KeyboardInterrupt:
        logger.info("服務已停止")
    except Exception as e:
        logger.error(f"服務啟動失敗: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

# =================== frontend/run_frontend.py ===================
#!/usr/bin/env python3
"""
前端服務啟動腳本
"""

import subprocess
import sys
import os

def main():
    """主程式"""
    try:
        print("啟動租房管理系統前端界面...")
        
        # 設置環境變數
        env = os.environ.copy()
        env['STREAMLIT_SERVER_HEADLESS'] = 'true'
        
        # 啟動Streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "app.py",
            "--server.port", "8501",
            "--server.headless", "true",
            "--server.fileWatcherType", "none",
            "--browser.gatherUsageStats", "false"
        ], env=env)
        
    except KeyboardInterrupt:
        print("\n前端服務已停止")
    except Exception as e:
        print(f"前端服務啟動失敗: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

# =================== quick_start.py ===================
#!/usr/bin/env python3
"""
快速啟動腳本 - 開發環境使用
"""

import os
import sys
import subprocess
import threading
import time
import signal
from pathlib import Path

def run_backend():
    """運行後端服務"""
    backend_dir = Path(__file__).parent / "backend"
    os.chdir(backend_dir)
    subprocess.run([sys.executable, "run_server.py"])

def run_frontend():
    """運行前端服務"""
    frontend_dir = Path(__file__).parent / "frontend"
    os.chdir(frontend_dir)
    subprocess.run([sys.executable, "run_frontend.py"])

def main():
    """主程式"""
    print("🚀 快速啟動租房管理系統...")
    
    # 啟動後端服務
    backend_thread = threading.Thread(target=run_backend, daemon=True)
    backend_thread.start()
    
    # 等待後端啟動
    time.sleep(3)
    
    # 啟動前端服務
    frontend_thread = threading.Thread(target=run_frontend, daemon=True)
    frontend_thread.start()
    
    print("✅ 系統啟動完成!")
    print("🌐 前端地址: http://localhost:8501")
    print("📖 API文檔: http://localhost:8000/docs")
    print("按 Ctrl+C 停止服務")
    
    try:
        # 保持主線程運行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n⏹️  系統已停止")

if __name__ == "__main__":
    main()

# =================== check_environment.py ===================
#!/usr/bin/env python3
"""
環境檢查腳本
"""

import sys
import subprocess
import importlib
from pathlib import Path

def check_python_version():
    """檢查Python版本"""
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version < (3, 9):
        print("❌ 需要Python 3.9或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True

def check_package(package_name):
    """檢查套件是否已安裝"""
    try:
        importlib.import_module(package_name)
        print(f"✅ {package_name}")
        return True
    except ImportError:
        print(f"❌ {package_name} 未安裝")
        return False

def check_dependencies():
    """檢查依賴套件"""
    print("\n檢查依賴套件:")
    
    backend_packages = [
        "fastapi", "uvicorn", "sqlalchemy", "pydantic", 
        "jose", "passlib", "python_multipart", "python_dotenv"
    ]
    
    frontend_packages = [
        "streamlit", "requests", "pandas", "plotly"
    ]
    
    backend_ok = all(check_package(pkg) for pkg in backend_packages)
    frontend_ok = all(check_package(pkg) for pkg in frontend_packages)
    
    return backend_ok and frontend_ok

def check_project_structure():
    """檢查項目結構"""
    print("\n檢查項目結構:")
    
    required_files = [
        "backend/app/main.py",
        "backend/app/models.py",
        "backend/requirements.txt",
        "frontend/app.py",
        "frontend/requirements.txt"
    ]
    
    base_dir = Path(__file__).parent
    all_exist = True
    
    for file_path in required_files:
        full_path = base_dir / file_path
        if full_path.exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} 不存在")
            all_exist = False
    
    return all_exist

def main():
    """主程式"""
    print("🔍 環境檢查開始...")
    
    checks = [
        check_python_version(),
        check_dependencies(),
        check_project_structure()
    ]
    
    if all(checks):
        print("\n🎉 環境檢查通過！可以啟動系統。")
        return True
    else:
        print("\n❌ 環境檢查失敗！請解決上述問題。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)