import streamlit as st
import pandas as pd
from datetime import datetime
from utils import show_error_message, show_success_message
from api_client import APIClient

def show_user_management_page():
    """顯示用戶管理頁面"""
    st.title("👥 帳號管理")
    
    # 檢查管理員權限
    if not check_admin_permission():
        st.error("🚫 權限不足：只有系統管理員才能管理用戶帳號")
        st.info("如需管理用戶帳號，請聯絡系統管理員")
        return
    
    st.success("✅ 管理員權限已驗證")
    
    # 頁籤設計
    tab1, tab2 = st.tabs(["用戶列表", "新增用戶"])
    
    with tab1:
        show_user_list()
    
    with tab2:
        show_create_user()

def check_admin_permission():
    """檢查是否為管理員權限"""
    if not st.session_state.get('authenticated', False):
        return False
    
    user_info = st.session_state.get('user_info', {})
    user_role = user_info.get('role', '')
    
    # 只有admin角色才能訪問
    return user_role == 'admin'

def show_user_list():
    """顯示用戶列表"""
    st.subheader("📋 用戶列表")
    
    api_client = APIClient()
    
    # 重新載入按鈕
    col1, col2 = st.columns([3, 1])
    with col2:
        if st.button("🔄 重新載入", key="reload_users"):
            st.rerun()
    
    # 獲取用戶列表
    users = api_client.get_all_users()
    
    if not users:
        st.info("目前沒有用戶資料")
        return
    
    # 轉換為DataFrame
    df = pd.DataFrame(users)
    
    # 格式化日期
    if 'created_at' in df.columns:
        df['created_at'] = pd.to_datetime(df['created_at']).dt.strftime('%Y-%m-%d %H:%M')
    if 'last_login' in df.columns:
        df['last_login'] = pd.to_datetime(df['last_login']).dt.strftime('%Y-%m-%d %H:%M')
    
    # 顯示統計資訊
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("總用戶數", len(users))
    
    with col2:
        active_users = len([u for u in users if u['is_active']])
        st.metric("啟用用戶", active_users)
    
    with col3:
        admin_users = len([u for u in users if u['role'] == 'admin'])
        st.metric("管理員", admin_users)
    
    with col4:
        inactive_users = len(users) - active_users
        st.metric("停用用戶", inactive_users)
    
    st.markdown("---")
    
    # 用戶表格
    for user in users:
        with st.container():
            col1, col2, col3, col4 = st.columns([2, 2, 1, 2])
            
            with col1:
                # 用戶基本資訊
                status_icon = "🟢" if user['is_active'] else "🔴"
                role_icon = "👑" if user['role'] == 'admin' else "👤"
                st.markdown(f"**{role_icon} {user['username']}** {status_icon}")
                st.caption(f"ID: {user['id']} | 角色: {user['role']}")
            
            with col2:
                # 聯絡資訊
                email = user.get('email', '未設定')
                st.text(f"📧 {email}")
                created_at = user.get('created_at', '未知')
                st.caption(f"創建時間: {created_at}")
            
            with col3:
                # 狀態
                if user['is_active']:
                    st.success("啟用")
                else:
                    st.error("停用")
            
            with col4:
                # 操作按鈕
                current_user_id = st.session_state.user_info.get('id')
                
                # 編輯按鈕
                if st.button("✏️ 編輯", key=f"edit_{user['id']}"):
                    st.session_state.edit_user_id = user['id']
                    st.session_state.show_edit_modal = True
                
                # 刪除按鈕（不能刪除自己）
                if user['id'] != current_user_id:
                    if st.button("🗑️ 刪除", key=f"delete_{user['id']}"):
                        if st.session_state.get(f"confirm_delete_{user['id']}", False):
                            # 執行刪除
                            success = api_client.delete_user(user['id'])
                            if success:
                                st.rerun()
                        else:
                            # 顯示確認
                            st.session_state[f"confirm_delete_{user['id']}"] = True
                            st.warning(f"確定要刪除用戶 {user['username']} 嗎？再次點擊刪除按鈕確認。")
                else:
                    st.caption("(自己)")
        
        st.markdown("---")
    
    # 編輯用戶模態框
    if st.session_state.get('show_edit_modal', False):
        show_edit_user_modal()

def show_edit_user_modal():
    """顯示編輯用戶模態框"""
    user_id = st.session_state.get('edit_user_id')
    if not user_id:
        return
    
    # 獲取用戶資訊
    api_client = APIClient()
    users = api_client.get_all_users()
    user = next((u for u in users if u['id'] == user_id), None)
    
    if not user:
        st.error("找不到用戶資訊")
        return
    
    st.subheader(f"編輯用戶: {user['username']}")
    
    with st.form("edit_user_form"):
        # 電子郵件
        email = st.text_input(
            "電子郵件",
            value=user.get('email', ''),
            help="用戶的電子郵件地址"
        )
        
        # 角色
        role = st.selectbox(
            "角色",
            options=['user', 'admin'],
            index=0 if user['role'] == 'user' else 1,
            help="用戶的系統角色"
        )
        
        # 帳號狀態
        is_active = st.checkbox(
            "啟用帳號",
            value=user['is_active'],
            help="是否啟用此用戶帳號"
        )
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.form_submit_button("💾 保存變更", use_container_width=True):
                success = api_client.update_user(
                    user_id=user_id,
                    email=email if email else None,
                    role=role,
                    is_active=is_active
                )
                
                if success:
                    st.session_state.show_edit_modal = False
                    st.rerun()
        
        with col2:
            if st.form_submit_button("❌ 取消", use_container_width=True):
                st.session_state.show_edit_modal = False
                st.rerun()

def show_create_user():
    """顯示創建用戶表單"""
    st.subheader("➕ 新增用戶")
    
    api_client = APIClient()
    
    with st.form("create_user_form"):
        st.markdown("**用戶基本資訊**")
        
        # 用戶名
        username = st.text_input(
            "用戶名*",
            help="用戶登入時使用的用戶名（至少3個字符）"
        )
        
        # 密碼
        password = st.text_input(
            "密碼*",
            type="password",
            help="用戶的登入密碼（至少6個字符）"
        )
        
        # 確認密碼
        confirm_password = st.text_input(
            "確認密碼*",
            type="password",
            help="請再次輸入密碼"
        )
        
        # 電子郵件
        email = st.text_input(
            "電子郵件*",
            help="用戶的電子郵件地址（必填）"
        )
        
        # 角色
        role = st.selectbox(
            "角色*",
            options=['user', 'admin'],
            index=0,
            help="用戶的系統角色"
        )
        
        # 提交按鈕
        submitted = st.form_submit_button("➕ 創建用戶", use_container_width=True)
        
        if submitted:
            # 驗證輸入
            if not username:
                show_error_message("請輸入用戶名")
                return
            
            if len(username) < 3:
                show_error_message("用戶名長度至少需要3個字符")
                return
            
            if not password:
                show_error_message("請輸入密碼")
                return
            
            if len(password) < 6:
                show_error_message("密碼長度至少需要6個字符")
                return
            
            if password != confirm_password:
                show_error_message("密碼與確認密碼不一致")
                return

            if not email:
                show_error_message("請輸入電子郵件")
                return

            # 簡單的電子郵件格式驗證
            if "@" not in email or "." not in email:
                show_error_message("請輸入有效的電子郵件格式")
                return

            # 創建用戶
            with st.spinner("正在創建用戶..."):
                success = api_client.create_user(
                    username=username,
                    password=password,
                    email=email,
                    role=role
                )

                if success:
                    show_success_message("用戶創建成功！")
                    st.rerun()
                else:
                    # 錯誤訊息已經在API客戶端中顯示了
                    pass
