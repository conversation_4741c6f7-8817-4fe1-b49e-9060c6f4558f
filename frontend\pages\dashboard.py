import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from api_client import api_client
from utils import format_currency

def show_dashboard_page():
    """顯示儀表板頁面"""
    st.title("📊 系統儀表板")
    
    # 獲取統計資料
    try:
        stats = api_client.get_dashboard_stats()

        if not stats:
            st.error("API錯誤: Internal Server Error")
            st.error("無法獲取統計資料")
            st.info("請檢查後端服務是否正常運行，或聯繫系統管理員")
            return
    except Exception as e:
        st.error(f"API錯誤: {str(e)}")
        st.error("無法獲取統計資料")
        st.info("請檢查網路連接和後端服務狀態")
        return
    
    # 主要統計指標
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            label="總房間數",
            value=stats['total_rooms'],
            delta=None
        )
    
    with col2:
        st.metric(
            label="已住房間",
            value=stats['occupied_rooms'],
            delta=f"{stats['occupied_rooms'] - stats['available_rooms']}"
        )
    
    with col3:
        st.metric(
            label="可用房間",
            value=stats['available_rooms'],
            delta=None
        )
    
    with col4:
        st.metric(
            label="入住率",
            value=f"{stats['occupancy_rate']}%",
            delta=f"{stats['occupancy_rate'] - 50:.1f}%"
        )
    
    st.markdown("---")
    
    # 圖表展示
    col1, col2 = st.columns(2)
    
    with col1:
        # 房間狀態分布餅圖
        fig_rooms = go.Figure(data=[go.Pie(
            labels=['已住房間', '可用房間'],
            values=[stats['occupied_rooms'], stats['available_rooms']],
            hole=0.3,
            marker_colors=['#ff6b6b', '#4ecdc4']
        )])
        
        fig_rooms.update_layout(
            title="房間狀態分布",
            annotations=[dict(text='房間狀態', x=0.5, y=0.5, font_size=20, showarrow=False)]
        )
        
        st.plotly_chart(fig_rooms, use_container_width=True)
    
    with col2:
        # 入住率指標
        fig_occupancy = go.Figure(go.Indicator(
            mode="gauge+number+delta",
            value=stats['occupancy_rate'],
            domain={'x': [0, 1], 'y': [0, 1]},
            title={'text': "入住率 (%)"},
            delta={'reference': 80},
            gauge={
                'axis': {'range': [None, 100]},
                'bar': {'color': "darkblue"},
                'steps': [
                    {'range': [0, 50], 'color': "lightgray"},
                    {'range': [50, 80], 'color': "gray"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 90
                }
            }
        ))
        
        fig_occupancy.update_layout(height=400)
        st.plotly_chart(fig_occupancy, use_container_width=True)
    
    # 當月收入統計
    st.subheader("當月收入統計")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric(
            label="當月收入",
            value=format_currency(stats['monthly_income']),
            delta=None
        )
    
    with col2:
        st.metric(
            label="住戶總數",
            value=stats['total_residents'],
            delta=None
        )
    
    with col3:
        avg_income = stats['monthly_income'] / stats['total_residents'] if stats['total_residents'] > 0 else 0
        st.metric(
            label="平均收入",
            value=format_currency(avg_income),
            delta=None
        )
    
    # 快速操作
    st.subheader("快速操作")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("🏠 新增房間", key="dashboard_add_room", use_container_width=True):
            st.session_state.page = "rooms"
            st.rerun()

    with col2:
        if st.button("👥 新增住戶", key="dashboard_add_resident", use_container_width=True):
            st.session_state.page = "residents"
            st.rerun()

    with col3:
        if st.button("💰 費用管理", key="dashboard_utilities", use_container_width=True):
            st.session_state.page = "utilities"
            st.rerun()

    with col4:
        if st.button("📊 查看報表", key="dashboard_reports", use_container_width=True):
            st.session_state.page = "reports"
            st.rerun()
