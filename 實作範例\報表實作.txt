# =================== frontend/pages/reports.py ===================
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, date, timedelta
from calendar import monthrange
import json
from api_client import api_client
from utils import (
    format_currency, format_date, format_datetime,
    show_success_message, show_error_message, show_warning_message, show_info_message
)

def show_reports_page():
    """顯示報表統計頁面"""
    st.title("📊 報表統計")
    
    # 頁籤設計
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "營收報表", "住戶分析", "房間分析", "趨勢分析", "自定義報表"
    ])
    
    with tab1:
        show_revenue_report()
    
    with tab2:
        show_resident_analysis()
    
    with tab3:
        show_room_analysis()
    
    with tab4:
        show_trend_analysis()
    
    with tab5:
        show_custom_report()

def show_revenue_report():
    """營收報表"""
    st.subheader("💰 營收報表")
    
    # 時間範圍選擇
    col1, col2, col3 = st.columns([1, 1, 1])
    
    with col1:
        report_year = st.selectbox("年份", range(2020, 2030), index=datetime.now().year - 2020)
    
    with col2:
        report_month = st.selectbox("月份", range(1, 13), index=datetime.now().month - 1)
    
    with col3:
        if st.button("🔍 生成報表"):
            st.rerun()
    
    # 獲取營收數據
    revenue_data = api_client.get_income_summary(report_year, report_month)
    
    if not revenue_data:
        show_info_message(f"{report_year}年{report_month}月暫無營收數據")
        return
    
    # 顯示營收概覽
    show_revenue_overview(revenue_data, report_year, report_month)
    
    # 顯示詳細分析
    show_revenue_details(revenue_data, report_year, report_month)

def show_revenue_overview(revenue_data, year, month):
    """顯示營收概覽"""
    st.markdown(f"### 📈 {year}年{month}月營收概覽")
    
    # 主要指標
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "總營收",
            format_currency(revenue_data['total_income']),
            help="本月總收入金額"
        )
    
    with col2:
        st.metric(
            "電費收入",
            format_currency(revenue_data['electricity_income']),
            delta=f"{revenue_data['electricity_income']/revenue_data['total_income']*100:.1f}%" if revenue_data['total_income'] > 0 else "0%"
        )
    
    with col3:
        st.metric(
            "水費收入",
            format_currency(revenue_data['water_income']),
            delta=f"{revenue_data['water_income']/revenue_data['total_income']*100:.1f}%" if revenue_data['total_income'] > 0 else "0%"
        )
    
    with col4:
        st.metric(
            "收款率",
            f"{revenue_data['collection_rate']:.1f}%",
            delta=f"{revenue_data['collection_rate'] - 85:.1f}%" if revenue_data['collection_rate'] >= 0 else "N/A"
        )
    
    # 收款狀況
    col1, col2 = st.columns(2)
    
    with col1:
        st.metric(
            "已收款金額",
            format_currency(revenue_data['paid_amount']),
            help="已收到的款項"
        )
    
    with col2:
        st.metric(
            "待收款金額",
            format_currency(revenue_data['unpaid_amount']),
            help="尚未收到的款項"
        )

def show_revenue_details(revenue_data, year, month):
    """顯示營收詳細分析"""
    st.markdown("### 📊 詳細分析")
    
    # 圖表展示
    col1, col2 = st.columns(2)
    
    with col1:
        # 收入構成分析
        income_composition = {
            "收入類型": ["電費收入", "水費收入"],
            "金額": [revenue_data['electricity_income'], revenue_data['water_income']],
            "占比": [
                revenue_data['electricity_income']/revenue_data['total_income']*100 if revenue_data['total_income'] > 0 else 0,
                revenue_data['water_income']/revenue_data['total_income']*100 if revenue_data['total_income'] > 0 else 0
            ]
        }
        
        fig1 = px.pie(
            income_composition,
            values="金額",
            names="收入類型",
            title="收入構成分析",
            hover_data=["占比"],
            color_discrete_map={"電費收入": "#FF6B6B", "水費收入": "#4ECDC4"}
        )
        st.plotly_chart(fig1, use_container_width=True)
    
    with col2:
        # 收款狀況分析
        collection_status = {
            "狀態": ["已收款", "未收款"],
            "金額": [revenue_data['paid_amount'], revenue_data['unpaid_amount']]
        }
        
        fig2 = px.bar(
            collection_status,
            x="狀態",
            y="金額",
            title="收款狀況分析",
            color="狀態",
            color_discrete_map={"已收款": "#28a745", "未收款": "#dc3545"}
        )
        st.plotly_chart(fig2, use_container_width=True)
    
    # 詳細帳單分析
    show_bills_analysis(year, month)

def show_bills_analysis(year, month):
    """顯示帳單分析"""
    st.markdown("#### 💳 帳單分析")
    
    bills = api_client.get_utility_bills(year, month)
    
    if not bills:
        show_info_message("本月暫無帳單數據")
        return
    
    # 按房間分析
    room_analysis = {}
    for bill in bills:
        room_number = bill['room']['room_number'] if bill['room'] else 'N/A'
        if room_number not in room_analysis:
            room_analysis[room_number] = {
                "電費": 0,
                "水費": 0,
                "總金額": 0,
                "用電量": 0,
                "狀態": bill['payment_status']
            }
        
        room_analysis[room_number]["電費"] += bill['electricity_cost']
        room_analysis[room_number]["水費"] += bill['water_fee']
        room_analysis[room_number]["總金額"] += bill['total_amount']
        room_analysis[room_number]["用電量"] += bill['electricity_usage']
    
    # 轉換為DataFrame
    df_rooms = pd.DataFrame.from_dict(room_analysis, orient='index')
    df_rooms.index.name = '房間號'
    df_rooms = df_rooms.reset_index()
    
    # 顯示房間費用排行
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("##### 🏠 房間費用排行 (TOP 5)")
        top_rooms = df_rooms.nlargest(5, '總金額')
        
        fig3 = px.bar(
            top_rooms,
            x="房間號",
            y="總金額",
            title="房間費用排行",
            color="總金額",
            color_continuous_scale="viridis"
        )
        st.plotly_chart(fig3, use_container_width=True)
    
    with col2:
        st.markdown("##### ⚡ 房間用電排行 (TOP 5)")
        top_usage = df_rooms.nlargest(5, '用電量')
        
        fig4 = px.bar(
            top_usage,
            x="房間號",
            y="用電量",
            title="房間用電排行",
            color="用電量",
            color_continuous_scale="plasma"
        )
        st.plotly_chart(fig4, use_container_width=True)
    
    # 詳細表格
    st.markdown("##### 📋 房間費用詳細")
    
    # 格式化數據
    df_display = df_rooms.copy()
    df_display['電費'] = df_display['電費'].apply(lambda x: format_currency(x))
    df_display['水費'] = df_display['水費'].apply(lambda x: format_currency(x))
    df_display['總金額'] = df_display['總金額'].apply(lambda x: format_currency(x))
    df_display['用電量'] = df_display['用電量'].apply(lambda x: f"{x:.0f} 度")
    
    st.dataframe(df_display, use_container_width=True)
    
    # 匯出功能
    if st.button("📥 匯出營收報表"):
        export_revenue_report(revenue_data, df_rooms, year, month)

def export_revenue_report(revenue_data, df_rooms, year, month):
    """匯出營收報表"""
    # 準備匯出數據
    export_data = {
        "報表期間": f"{year}年{month}月",
        "總營收": revenue_data['total_income'],
        "電費收入": revenue_data['electricity_income'],
        "水費收入": revenue_data['water_income'],
        "收款率": f"{revenue_data['collection_rate']:.1f}%",
        "已收款": revenue_data['paid_amount'],
        "未收款": revenue_data['unpaid_amount']
    }
    
    # 轉換為CSV
    summary_df = pd.DataFrame([export_data])
    
    # 合併數據
    with pd.ExcelWriter(f"revenue_report_{year}_{month:02d}.xlsx", engine='openpyxl') as writer:
        summary_df.to_excel(writer, sheet_name='營收摘要', index=False)
        df_rooms.to_excel(writer, sheet_name='房間詳細', index=False)
    
    st.success("報表已匯出！")

def show_resident_analysis():
    """住戶分析"""
    st.subheader("👥 住戶分析")
    
    # 獲取住戶數據
    residents = api_client.get_residents(active_only=False)
    
    if not residents:
        show_info_message("暫無住戶數據")
        return
    
    # 基本統計
    show_resident_statistics(residents)
    
    # 住戶分布分析
    show_resident_distribution(residents)
    
    # 住戶流動分析
    show_resident_turnover(residents)

def show_resident_statistics(residents):
    """顯示住戶基本統計"""
    st.markdown("### 📊 住戶基本統計")
    
    # 計算統計指標
    total_residents = len(residents)
    active_residents = len([r for r in residents if r['is_active']])
    moved_out_residents = total_residents - active_residents
    
    # 押金統計
    active_deposits = [r['deposit'] for r in residents if r['is_active']]
    total_deposits = sum(active_deposits)
    avg_deposit = total_deposits / len(active_deposits) if active_deposits else 0
    max_deposit = max(active_deposits) if active_deposits else 0
    min_deposit = min(active_deposits) if active_deposits else 0
    
    # 住宿時長統計
    current_date = datetime.now()
    stay_durations = []
    for resident in residents:
        if resident['is_active'] and resident['move_in_date']:
            move_in = datetime.fromisoformat(resident['move_in_date'].replace('Z', '+00:00'))
            duration = (current_date - move_in).days
            stay_durations.append(duration)
    
    avg_stay = sum(stay_durations) / len(stay_durations) if stay_durations else 0
    
    # 顯示指標
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("總住戶數", total_residents)
        st.metric("在住住戶", active_residents)
    
    with col2:
        st.metric("已退房住戶", moved_out_residents)
        st.metric("平均住宿天數", f"{avg_stay:.0f} 天")
    
    with col3:
        st.metric("押金總額", format_currency(total_deposits))
        st.metric("平均押金", format_currency(avg_deposit))
    
    with col4:
        st.metric("最高押金", format_currency(max_deposit))
        st.metric("最低押金", format_currency(min_deposit))

def show_resident_distribution(residents):
    """顯示住戶分布分析"""
    st.markdown("### 🏠 住戶分布分析")
    
    # 按房間分布
    room_distribution = {}
    for resident in residents:
        if resident['is_active'] and resident['room']:
            room_number = resident['room']['room_number']
            if room_number not in room_distribution:
                room_distribution[room_number] = 0
            room_distribution[room_number] += 1
    
    if room_distribution:
        col1, col2 = st.columns(2)
        
        with col1:
            # 房間住戶分布圖
            room_dist_data = {
                "房間": list(room_distribution.keys()),
                "住戶數": list(room_distribution.values())
            }
            
            fig1 = px.bar(
                room_dist_data,
                x="房間",
                y="住戶數",
                title="各房間住戶分布",
                color="住戶數",
                color_continuous_scale="blues"
            )
            st.plotly_chart(fig1, use_container_width=True)
        
        with col2:
            # 住戶數分布
            occupancy_dist = {}
            for count in room_distribution.values():
                occupancy_dist[f"{count}人房"] = occupancy_dist.get(f"{count}人房", 0) + 1
            
            occ_data = {
                "類型": list(occupancy_dist.keys()),
                "房間數": list(occupancy_dist.values())
            }
            
            fig2 = px.pie(
                occ_data,
                values="房間數",
                names="類型",
                title="房間住戶數分布"
            )
            st.plotly_chart(fig2, use_container_width=True)

def show_resident_turnover(residents):
    """顯示住戶流動分析"""
    st.markdown("### 🔄 住戶流動分析")
    
    # 按月統計入住和退房
    monthly_stats = {}
    
    for resident in residents:
        # 入住統計
        if resident['move_in_date']:
            move_in = datetime.fromisoformat(resident['move_in_date'].replace('Z', '+00:00'))
            month_key = f"{move_in.year}-{move_in.month:02d}"
            if month_key not in monthly_stats:
                monthly_stats[month_key] = {"入住": 0, "退房": 0}
            monthly_stats[month_key]["入住"] += 1
        
        # 退房統計
        if resident['move_out_date']:
            move_out = datetime.fromisoformat(resident['move_out_date'].replace('Z', '+00:00'))
            month_key = f"{move_out.year}-{move_out.month:02d}"
            if month_key not in monthly_stats:
                monthly_stats[month_key] = {"入住": 0, "退房": 0}
            monthly_stats[month_key]["退房"] += 1
    
    if monthly_stats:
        # 轉換為DataFrame
        df_turnover = pd.DataFrame.from_dict(monthly_stats, orient='index')
        df_turnover.index.name = '月份'
        df_turnover = df_turnover.reset_index()
        df_turnover = df_turnover.sort_values('月份')
        
        # 計算淨流入
        df_turnover['淨流入'] = df_turnover['入住'] - df_turnover['退房']
        
        # 顯示趨勢圖
        fig3 = px.line(
            df_turnover,
            x='月份',
            y=['入住', '退房', '淨流入'],
            title='住戶流動趨勢',
            markers=True
        )
        st.plotly_chart(fig3, use_container_width=True)
        
        # 顯示數據表格
        st.markdown("#### 📋 月度流動數據")
        st.dataframe(df_turnover, use_container_width=True)

def show_room_analysis():
    """房間分析"""
    st.subheader("🏠 房間分析")
    
    # 獲取房間數據
    rooms = api_client.get_rooms()
    
    if not rooms:
        show_info_message("暫無房間數據")
        return
    
    # 房間使用率分析
    show_room_utilization(rooms)
    
    # 租金分析
    show_rent_analysis(rooms)
    
    # 房間收益分析
    show_room_revenue_analysis(rooms)

def show_room_utilization(rooms):
    """顯示房間使用率分析"""
    st.markdown("### 📊 房間使用率分析")
    
    # 計算使用率
    total_capacity = sum(room['max_occupants'] for room in rooms)
    current_occupancy = sum(room['current_occupants'] for room in rooms)
    utilization_rate = (current_occupancy / total_capacity * 100) if total_capacity > 0 else 0
    
    # 狀態統計
    status_stats = {}
    for room in rooms:
        status = room['status']
        status_stats[status] = status_stats.get(status, 0) + 1
    
    # 顯示指標
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("總房間數", len(rooms))
    
    with col2:
        st.metric("總容量", f"{total_capacity}人")
    
    with col3:
        st.metric("目前住戶", f"{current_occupancy}人")
    
    with col4:
        st.metric("使用率", f"{utilization_rate:.1f}%")
    
    # 圖表展示
    col1, col2 = st.columns(2)
    
    with col1:
        # 房間狀態分布
        status_data = {
            "狀態": list(status_stats.keys()),
            "數量": list(status_stats.values())
        }
        
        fig1 = px.pie(
            status_data,
            values="數量",
            names="狀態",
            title="房間狀態分布"
        )
        st.plotly_chart(fig1, use_container_width=True)
    
    with col2:
        # 使用率儀表板
        fig2 = go.Figure(go.Indicator(
            mode="gauge+number+delta",
            value=utilization_rate,
            domain={'x': [0, 1], 'y': [0, 1]},
            title={'text': "房間使用率 (%)"},
            delta={'reference': 80},
            gauge={
                'axis': {'range': [None, 100]},
                'bar': {'color': "lightgreen"},
                'steps': [
                    {'range': [0, 50], 'color': "lightgray"},
                    {'range': [50, 80], 'color': "yellow"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 90
                }
            }
        ))
        st.plotly_chart(fig2, use_container_width=True)

def show_rent_analysis(rooms):
    """顯示租金分析"""
    st.markdown("### 💰 租金分析")
    
    # 租金統計
    single_rents = [room['rent_single'] for room in rooms]
    double_rents = [room['rent_double'] for room in rooms]
    current_rents = [room['current_rent'] for room in rooms]
    
    # 統計指標
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("平均單人租金", format_currency(sum(single_rents) / len(single_rents)))
    
    with col2:
        st.metric("平均雙人租金", format_currency(sum(double_rents) / len(double_rents)))
    
    with col3:
        st.metric("目前平均租金", format_currency(sum(current_rents) / len(current_rents)))
    
    with col4:
        potential_revenue = sum(double_rents)  # 假設全滿
        actual_revenue = sum(current_rents)
        revenue_realization = (actual_revenue / potential_revenue * 100) if potential_revenue > 0 else 0
        st.metric("收益實現率", f"{revenue_realization:.1f}%")
    
    # 租金分布圖
    col1, col2 = st.columns(2)
    
    with col1:
        # 租金分布直方圖
        rent_data = {
            "房間": [room['room_number'] for room in rooms],
            "單人租金": single_rents,
            "雙人租金": double_rents
        }
        
        df_rent = pd.DataFrame(rent_data)
        
        fig1 = px.bar(
            df_rent,
            x="房間",
            y=["單人租金", "雙人租金"],
            title="各房間租金對比",
            barmode="group"
        )
        st.plotly_chart(fig1, use_container_width=True)
    
    with col2:
        # 租金區間分布
        rent_ranges = {
            "8000以下": len([r for r in current_rents if r < 8000]),
            "8000-12000": len([r for r in current_rents if 8000 <= r < 12000]),
            "12000-16000": len([r for r in current_rents if 12000 <= r < 16000]),
            "16000以上": len([r for r in current_rents if r >= 16000])
        }
        
        range_data = {
            "租金區間": list(rent_ranges.keys()),
            "房間數": list(rent_ranges.values())
        }
        
        fig2 = px.bar(
            range_data,
            x="租金區間",
            y="房間數",
            title="租金區間分布"
        )
        st.plotly_chart(fig2, use_container_width=True)

def show_room_revenue_analysis(rooms):
    """顯示房間收益分析"""
    st.markdown("### 📈 房間收益分析")
    
    # 計算每個房間的收益潛力
    room_revenue_data = []
    for room in rooms:
        room_data = {
            "房間號": room['room_number'],
            "目前收益": room['current_rent'],
            "最大收益": room['rent_double'],
            "收益實現率": (room['current_rent'] / room['rent_double'] * 100) if room['rent_double'] > 0 else 0,
            "收益缺口": room['rent_double'] - room['current_rent'],
            "住戶數": room['current_occupants'],
            "狀態": room['status']
        }
        room_revenue_data.append(room_data)
    
    df_revenue = pd.DataFrame(room_revenue_data)
    
    # 收益排行
    col1, col2 = st.columns(2)
    
    with col1:
        # 目前收益排行
        top_revenue = df_revenue.nlargest(5, '目前收益')
        
        fig1 = px.bar(
            top_revenue,
            x="房間號",
            y="目前收益",
            title="房間收益排行 (TOP 5)",
            color="目前收益",
            color_continuous_scale="greens"
        )
        st.plotly_chart(fig1, use_container_width=True)
    
    with col2:
        # 收益缺口分析
        gap_revenue = df_revenue.nlargest(5, '收益缺口')
        
        fig2 = px.bar(
            gap_revenue,
            x="房間號",
            y="收益缺口",
            title="收益缺口分析 (TOP 5)",
            color="收益缺口",
            color_continuous_scale="reds"
        )
        st.plotly_chart(fig2, use_container_width=True)
    
    # 詳細表格
    st.markdown("#### 📋 房間收益詳細")
    
    # 格式化顯示數據
    df_display = df_revenue.copy()
    df_display['目前收益'] = df_display['目前收益'].apply(lambda x: format_currency(x))
    df_display['最大收益'] = df_display['最大收益'].apply(lambda x: format_currency(x))
    df_display['收益缺口'] = df_display['收益缺口'].apply(lambda x: format_currency(x))
    df_display['收益實現率'] = df_display['收益實現率'].apply(lambda x: f"{x:.1f}%")
    
    st.dataframe(df_display, use_container_width=True)

def show_trend_analysis():
    """趨勢分析"""
    st.subheader("📈 趨勢分析")
    
    # 時間範圍選擇
    col1, col2 = st.columns(2)
    
    with col1:
        months_back = st.slider("分析月數", min_value=3, max_value=12, value=6)
    
    with col2:
        analysis_type = st.selectbox("分析類型", ["收入趨勢", "入住率趨勢", "住戶流動趨勢"])
    
    # 根據選擇顯示不同的趨勢分析
    if analysis_type == "收入趨勢":
        show_income_trend(months_back)
    elif analysis_type == "入住率趨勢":
        show_occupancy_trend(months_back)
    else:
        show_turnover_trend(months_back)

def show_income_trend(months_back):
    """顯示收入趨勢"""
    st.markdown("### 💰 收入趨勢分析")
    
    # 獲取歷史數據
    income_data = []
    current_date = datetime.now()
    
    for i in range(months_back):
        target_date = current_date - timedelta(days=30 * i)
        year = target_date.year
        month = target_date.month
        
        try:
            monthly_data = api_client.get_income_summary(year, month)
            if monthly_data:
                income_data.append({
                    "年月": f"{year}-{month:02d}",
                    "年": year,
                    "月": month,
                    "總收入": monthly_data['total_income'],
                    "電費收入": monthly_data['electricity_income'],
                    "水費收入": monthly_data['water_income'],
                    "收款率": monthly_data['collection_rate']
                })
        except:
            continue
    
    if not income_data:
        show_info_message("暫無足夠的歷史數據")
        return
    
    # 排序數據
    income_data.sort(key=lambda x: (x['年'], x['月']))
    df_income = pd.DataFrame(income_data)
    
    # 收入趨勢圖
    fig1 = px.line(
        df_income,
        x="年月",
        y=["總收入", "電費收入", "水費收入"],
        title="收入趨勢",
        markers=True
    )
    st.plotly_chart(fig1, use_container_width=True)
    
    # 收款率趨勢
    fig2 = px.line(
        df_income,
        x="年月",
        y="收款率",
        title="收款率趨勢",
        markers=True,
        line_shape="spline"
    )
    st.plotly_chart(fig2, use_container_width=True)
    
    # 統計摘要
    col1, col2, col3 = st.columns(3)
    
    with col1:
        avg_income = df_income['總收入'].mean()
        st.metric("平均月收入", format_currency(avg_income))
    
    with col2:
        income_growth = ((df_income['總收入'].iloc[-1] / df_income['總收入'].iloc[0] - 1) * 100) if len(df_income) > 1 else 0
        st.metric("收入成長率", f"{income_growth:.1f}%")
    
    with col3:
        avg_collection_rate = df_income['收款率'].mean()
        st.metric("平均收款率", f"{avg_collection_rate:.1f}%")

def show_occupancy_trend(months_back):
    """顯示入住率趨勢"""
    st.markdown("### 🏠 入住率趨勢分析")
    
    # 這裡應該從歷史數據計算入住率
    # 由於API限制，我們使用模擬數據進行展示
    st.info("入住率歷史數據功能開發中，目前顯示當前狀態")
    
    # 獲取當前房間數據
    rooms = api_client.get_rooms()
    
    if rooms:
        total_capacity = sum(room['max_occupants'] for room in rooms)
        current_occupancy = sum(room['current_occupants'] for room in rooms)
        current_rate = (current_occupancy / total_capacity * 100) if total_capacity > 0 else 0
        
        st.metric("當前入住率", f"{current_rate:.1f}%")
        
        # 入住率儀表板
        fig = go.Figure(go.Indicator(
            mode="gauge+number",
            value=current_rate,
            title={'text': "當前入住率"},
            gauge={'axis': {'range': [None, 100]},
                   'bar': {'color': "darkblue"},
                   'steps': [{'range': [0, 50], 'color': "lightgray"},
                            {'range': [50, 80], 'color': "yellow"}],
                   'threshold': {'line': {'color': "red", 'width': 4},
                               'thickness': 0.75, 'value': 90}}
        ))
        st.plotly_chart(fig, use_container_width=True)

def show_turnover_trend(months_back):
    """顯示住戶流動趨勢"""
    st.markdown("### 🔄 住戶流動趨勢分析")
    
    residents = api_client.get_residents(active_only=False)
    
    if not residents:
        show_info_message("暫無住戶數據")
        return
    
    # 計算流動數據（與住戶分析中的邏輯相同）
    monthly_stats = {}
    
    for resident in residents:
        # 入住統計
        if resident['move_in_date']:
            move_in = datetime.fromisoformat(resident['move_in_date'].replace('Z', '+00:00'))
            month_key = f"{move_in.year}-{move_in.month:02d}"
            if month_key not in monthly_stats:
                monthly_stats[month_key] = {"入住": 0, "退房": 0}
            monthly_stats[month_key]["入住"] += 1
        
        # 退房統計
        if resident['move_out_date']:
            move_out = datetime.fromisoformat(resident['move_out_date'].replace('Z', '+00:00'))
            month_key = f"{move_out.year}-{move_out.month:02d}"
            if month_key not in monthly_stats:
                monthly_stats[month_key] = {"入住": 0, "退房": 0}
            monthly_stats[month_key]["退房"] += 1
    
    if monthly_stats:
        # 只取最近的月份
        recent_months = sorted(monthly_stats.keys())[-months_back:]
        filtered_stats = {month: monthly_stats[month] for month in recent_months}
        
        df_turnover = pd.DataFrame.from_dict(filtered_stats, orient='index')
        df_turnover.index.name = '月份'
        df_turnover = df_turnover.reset_index()
        df_turnover['淨流入'] = df_turnover['入住'] - df_turnover['退房']
        
        # 流動趨勢圖
        fig = px.line(
            df_turnover,
            x='月份',
            y=['入住', '退房', '淨流入'],
            title=f'最近{months_back}個月住戶流動趨勢',
            markers=True
        )
        st.plotly_chart(fig, use_container_width=True)
        
        # 流動統計
        col1, col2, col3 = st.columns(3)
        
        with col1:
            total_move_in = df_turnover['入住'].sum()
            st.metric("總入住人次", total_move_in)
        
        with col2:
            total_move_out = df_turnover['退房'].sum()
            st.metric("總退房人次", total_move_out)
        
        with col3:
            net_flow = total_move_in - total_move_out
            st.metric("淨流入", net_flow)

def show_custom_report():
    """自定義報表"""
    st.subheader("🛠️ 自定義報表")
    
    st.markdown("### 📋 報表生成器")
    
    # 報表類型選擇
    report_type = st.selectbox(
        "報表類型",
        ["綜合報表", "營收報表", "住戶報表", "房間報表"]
    )
    
    # 時間範圍選擇
    col1, col2 = st.columns(2)
    
    with col1:
        start_date = st.date_input("開始日期", value=date.today() - timedelta(days=30))
    
    with col2:
        end_date = st.date_input("結束日期", value=date.today())
    
    # 報表選項
    with st.expander("📊 報表選項"):
        include_charts = st.checkbox("包含圖表", value=True)
        include_details = st.checkbox("包含詳細數據", value=True)
        export_format = st.selectbox("匯出格式", ["Excel", "PDF", "CSV"])
    
    # 生成報表
    if st.button("📈 生成自定義報表", use_container_width=True):
        generate_custom_report(report_type, start_date, end_date, include_charts, include_details, export_format)

def generate_custom_report(report_type, start_date, end_date, include_charts, include_details, export_format):
    """生成自定義報表"""
    with st.spinner("正在生成報表..."):
        # 模擬報表生成
        import time
        time.sleep(2)
        
        if report_type == "綜合報表":
            show_comprehensive_report(start_date, end_date)
        elif report_type == "營收報表":
            show_revenue_custom_report(start_date, end_date)
        elif report_type == "住戶報表":
            show_resident_custom_report(start_date, end_date)
        else:
            show_room_custom_report(start_date, end_date)
        
        show_success_message(f"✅ {report_type}生成成功！")

def show_comprehensive_report(start_date, end_date):
    """顯示綜合報表"""
    st.markdown(f"### 📊 綜合報表 ({start_date} ~ {end_date})")
    
    # 獲取當前數據摘要
    dashboard_stats = api_client.get_dashboard_stats()
    
    if dashboard_stats:
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("總房間數", dashboard_stats['total_rooms'])
        
        with col2:
            st.metric("住戶總數", dashboard_stats['total_residents'])
        
        with col3:
            st.metric("入住率", f"{dashboard_stats['occupancy_rate']}%")
        
        with col4:
            st.metric("月收入", format_currency(dashboard_stats['monthly_income']))
        
        # 綜合圖表
        fig = go.Figure()
        
        # 添加入住率數據
        fig.add_trace(go.Scatter(
            x=[start_date, end_date],
            y=[dashboard_stats['occupancy_rate'], dashboard_stats['occupancy_rate']],
            mode='lines+markers',
            name='入住率 (%)',
            line=dict(color='blue')
        ))
        
        fig.update_layout(
            title="綜合指標趨勢",
            xaxis_title="日期",
            yaxis_title="數值",
            showlegend=True
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    st.info("💡 綜合報表功能將持續完善，敬請期待更多功能！")

def show_revenue_custom_report(start_date, end_date):
    """顯示自定義營收報表"""
    st.markdown(f"### 💰 營收報表 ({start_date} ~ {end_date})")
    st.info("自定義營收報表功能開發中...")

def show_resident_custom_report(start_date, end_date):
    """顯示自定義住戶報表"""
    st.markdown(f"### 👥 住戶報表 ({start_date} ~ {end_date})")
    st.info("自定義住戶報表功能開發中...")

def show_room_custom_report(start_date, end_date):
    """顯示自定義房間報表"""
    st.markdown(f"### 🏠 房間報表 ({start_date} ~ {end_date})")
    st.info("自定義房間報表功能開發中...")