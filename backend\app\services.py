from sqlalchemy.orm import Session
from .models import User, Room, Resident, UtilityRate, UtilityRecord, RentRecord
from .auth import get_password_hash, verify_password
from datetime import datetime
from typing import List, Optional

class UserService:
    """用戶服務"""
    
    @staticmethod
    def create_user(db: Session, username: str, password: str, email: str = None, role: str = "user"):
        """創建新用戶"""
        # 檢查用戶名是否已存在
        if db.query(User).filter(User.username == username).first():
            raise ValueError("用戶名已存在")

        # 檢查電子郵件是否已存在
        if email and db.query(User).filter(User.email == email).first():
            raise ValueError("電子郵件已存在")

        password_hash = get_password_hash(password)
        user = User(
            username=username,
            password_hash=password_hash,
            email=email,
            role=role
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        return user
    
    @staticmethod
    def authenticate_user(db: Session, username: str, password: str):
        """用戶認證"""
        user = db.query(User).filter(User.username == username).first()
        if not user or not verify_password(password, user.password_hash):
            return None

        # 更新最後登入時間
        user.last_login = datetime.utcnow()
        db.commit()
        return user
    
    @staticmethod
    def get_user_by_username(db: Session, username: str):
        """根據用戶名獲取用戶"""
        return db.query(User).filter(User.username == username).first()

    @staticmethod
    def get_user_by_email(db: Session, email: str):
        """根據電子郵件獲取用戶"""
        return db.query(User).filter(User.email == email).first()

    @staticmethod
    def verify_password(password: str, password_hash: str) -> bool:
        """驗證密碼"""
        return verify_password(password, password_hash)

    @staticmethod
    def change_password(db: Session, user_id: int, new_password: str) -> bool:
        """變更用戶密碼"""
        try:
            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                return False

            # 更新密碼
            user.password_hash = get_password_hash(new_password)
            db.commit()
            return True
        except Exception:
            db.rollback()
            return False

    @staticmethod
    def get_all_users(db: Session):
        """獲取所有用戶"""
        return db.query(User).all()

    @staticmethod
    def get_user_by_id(db: Session, user_id: int):
        """根據ID獲取用戶"""
        return db.query(User).filter(User.id == user_id).first()

    @staticmethod
    def update_user(db: Session, user_id: int, update_data: dict):
        """更新用戶資訊"""
        try:
            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                return None

            # 更新欄位
            for key, value in update_data.items():
                if hasattr(user, key) and value is not None:
                    setattr(user, key, value)

            db.commit()
            db.refresh(user)
            return user
        except Exception:
            db.rollback()
            return None

    @staticmethod
    def delete_user(db: Session, user_id: int) -> bool:
        """刪除用戶"""
        try:
            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                return False

            db.delete(user)
            db.commit()
            return True
        except Exception:
            db.rollback()
            return False

class RoomService:
    """房間服務"""
    
    @staticmethod
    def create_room(db: Session, room_data: dict):
        """創建新房間"""
        # 檢查房間號是否已存在
        if db.query(Room).filter(Room.room_number == room_data["room_number"]).first():
            raise ValueError("房間重複")
        
        room = Room(**room_data)
        db.add(room)
        db.commit()
        db.refresh(room)
        return room
    
    @staticmethod
    def get_all_rooms(db: Session, include_inactive: bool = False):
        """獲取所有房間"""
        query = db.query(Room)
        if not include_inactive:
            query = query.filter(Room.is_active == True)
        return query.all()
    
    @staticmethod
    def get_room_by_id(db: Session, room_id: int):
        """根據ID獲取房間"""
        return db.query(Room).filter(Room.id == room_id).first()
    
    @staticmethod
    def update_room_occupancy(db: Session, room_id: int):
        """更新房間住戶數量"""
        room = db.query(Room).filter(Room.id == room_id).first()
        if room:
            active_residents = db.query(Resident).filter(
                Resident.room_id == room_id,
                Resident.is_active == True,
                Resident.move_out_date.is_(None)
            ).count()
            
            room.current_occupants = active_residents
            
            # 更新房間狀態
            if active_residents == 0:
                room.status = "available"
            elif active_residents == room.max_occupants:
                room.status = "occupied"
            else:
                room.status = "partial"
            
            db.commit()
            return room
        return None
    
    @staticmethod
    def get_available_rooms(db: Session):
        """獲取可用房間"""
        return db.query(Room).filter(
            Room.is_active == True,
            Room.current_occupants < Room.max_occupants
        ).all()

    @staticmethod
    def soft_delete_room(db: Session, room_id: int, current_user):
        """軟刪除房間"""
        try:
            # 1. 驗證房間存在且為活躍狀態
            room = db.query(Room).filter(
                Room.id == room_id,
                Room.is_active == True
            ).first()

            if not room:
                return {
                    "success": False,
                    "error": "房間不存在或已被刪除",
                    "error_code": "ROOM_NOT_FOUND"
                }

            # 2. 檢查是否有活躍住戶
            active_residents = db.query(Resident).filter(
                Resident.room_id == room_id,
                Resident.is_active == True
            ).all()

            if active_residents:
                resident_names = [resident.name for resident in active_residents]
                return {
                    "success": False,
                    "error": f"無法刪除房間，請先處理以下住戶：{', '.join(resident_names)}",
                    "error_code": "HAS_ACTIVE_RESIDENTS",
                    "residents": resident_names
                }

            # 3. 執行軟刪除
            room.is_active = False
            room.status = "deleted"

            # 4. 記錄操作（如果有操作日誌表的話）
            # 這裡可以添加操作日誌記錄

            db.commit()

            return {
                "success": True,
                "message": f"房間 {room.room_number} 已成功刪除",
                "room_id": room_id,
                "room_number": room.room_number
            }

        except Exception as e:
            db.rollback()
            return {
                "success": False,
                "error": f"刪除房間時發生錯誤: {str(e)}",
                "error_code": "DELETE_ERROR"
            }

    @staticmethod
    def update_room(db: Session, room_id: int, update_data: dict):
        """更新房間資訊"""
        try:
            room = db.query(Room).filter(Room.id == room_id).first()
            if not room:
                return None

            # 更新房間資料
            for key, value in update_data.items():
                if hasattr(room, key) and value is not None:
                    setattr(room, key, value)

            db.commit()
            db.refresh(room)
            return room

        except Exception as e:
            db.rollback()
            raise e

    @staticmethod
    def update_room(db: Session, room_id: int, update_data: dict):
        """更新房間資訊"""
        try:
            room = db.query(Room).filter(Room.id == room_id).first()
            if not room:
                return None

            # 更新房間資料
            for key, value in update_data.items():
                if hasattr(room, key) and value is not None:
                    setattr(room, key, value)

            db.commit()
            db.refresh(room)
            return room

        except Exception as e:
            db.rollback()
            raise e

class ResidentService:
    """住戶服務"""
    
    @staticmethod
    def create_resident(db: Session, resident_data: dict):
        """創建新住戶"""
        # 檢查身份證是否已存在
        existing = db.query(Resident).filter(
            Resident.id_number == resident_data["id_number"],
            Resident.is_active == True
        ).first()
        if existing:
            raise ValueError("身份證號已存在")
        
        resident = Resident(**resident_data)
        db.add(resident)
        db.commit()
        db.refresh(resident)
        
        # 更新房間住戶數量
        RoomService.update_room_occupancy(db, resident.room_id)
        return resident
    
    @staticmethod
    def get_all_residents(db: Session, active_only: bool = True):
        """獲取所有住戶"""
        query = db.query(Resident)
        if active_only:
            query = query.filter(Resident.is_active == True)
        return query.all()
    
    @staticmethod
    def get_resident_by_id(db: Session, resident_id: int):
        """根據ID獲取住戶"""
        return db.query(Resident).filter(Resident.id == resident_id).first()
    
    @staticmethod
    def move_out_resident(db: Session, resident_id: int, move_out_date: datetime):
        """住戶退房"""
        resident = db.query(Resident).filter(Resident.id == resident_id).first()
        if resident:
            resident.move_out_date = move_out_date
            resident.is_active = False
            db.commit()
            
            # 更新房間住戶數量
            RoomService.update_room_occupancy(db, resident.room_id)
            return resident
        return None

    @staticmethod
    def update_resident(db: Session, resident_id: int, update_data: dict):
        """更新住戶資訊"""
        try:
            resident = db.query(Resident).filter(Resident.id == resident_id).first()
            if not resident:
                return None

            # 更新住戶資料
            for key, value in update_data.items():
                if hasattr(resident, key) and value is not None:
                    setattr(resident, key, value)

            db.commit()
            db.refresh(resident)
            return resident

        except Exception as e:
            db.rollback()
            raise e

class UtilityService:
    """公用事業服務"""

    @staticmethod
    def create_utility_rate(db: Session, rate_data: dict):
        """創建新費率"""
        try:
            rate = UtilityRate(**rate_data)
            db.add(rate)
            db.commit()
            db.refresh(rate)
            return rate
        except Exception as e:
            db.rollback()
            raise e

    @staticmethod
    def get_all_utility_rates(db: Session):
        """獲取所有費率"""
        return db.query(UtilityRate).order_by(UtilityRate.effective_date.desc()).all()

    @staticmethod
    def get_utility_rate_by_id(db: Session, rate_id: int):
        """根據ID獲取費率"""
        return db.query(UtilityRate).filter(UtilityRate.id == rate_id).first()

    @staticmethod
    def update_utility_rate(db: Session, rate_id: int, update_data: dict):
        """更新費率"""
        try:
            rate = db.query(UtilityRate).filter(UtilityRate.id == rate_id).first()
            if not rate:
                return None

            # 更新費率資料
            for key, value in update_data.items():
                if hasattr(rate, key) and value is not None:
                    setattr(rate, key, value)

            db.commit()
            db.refresh(rate)
            return rate

        except Exception as e:
            db.rollback()
            raise e

    @staticmethod
    def get_rate_for_date(db: Session, target_date):
        """根據指定日期獲取合適的費率

        選擇生效日期最接近目標日期且不超過目標日期的費率

        Args:
            db: 資料庫會話
            target_date: 目標日期（可以是 datetime, date 或 str）

        Returns:
            UtilityRate: 合適的費率記錄，如果沒有找到則返回 None
        """
        from datetime import datetime, date

        # 將輸入轉換為datetime對象
        if isinstance(target_date, str):
            if 'T' in target_date:
                parsed_date = datetime.fromisoformat(target_date.replace('Z', '+00:00'))
            else:
                parsed_date = datetime.strptime(target_date, '%Y-%m-%d')
        elif isinstance(target_date, date) and not isinstance(target_date, datetime):
            parsed_date = datetime.combine(target_date, datetime.min.time())
        elif isinstance(target_date, datetime):
            parsed_date = target_date
        else:
            raise ValueError(f"不支援的日期格式: {type(target_date)}")

        # 確保時間部分為23:59:59，這樣可以包含當天的所有時間
        target_datetime = parsed_date.replace(hour=23, minute=59, second=59, microsecond=999999)

        # 查詢生效日期≤目標日期的所有活躍費率，按生效日期降序排列
        # 這樣第一個結果就是最接近且不超過目標日期的費率
        return db.query(UtilityRate).filter(
            UtilityRate.is_active == True,
            UtilityRate.effective_date <= target_datetime
        ).order_by(UtilityRate.effective_date.desc()).first()

    @staticmethod
    def get_current_utility_rate(db: Session):
        """獲取當前費率（基於今天的日期）"""
        from datetime import datetime

        return UtilityService.get_rate_for_date(db, datetime.now())

    @staticmethod
    def get_rate_by_date(db: Session, effective_date):
        """根據生效日期獲取費率（向後兼容方法）

        現在使用 get_rate_for_date 方法來實現正確的費率選擇邏輯
        """
        return UtilityService.get_rate_for_date(db, effective_date)

    @staticmethod
    def get_previous_reading(db: Session, room_id: int, year: int, month: int):
        """獲取上月電表讀數"""
        # 計算上月
        if month == 1:
            prev_year, prev_month = year - 1, 12
        else:
            prev_year, prev_month = year, month - 1

        # 查詢上月記錄
        prev_record = db.query(UtilityRecord).filter(
            UtilityRecord.room_id == room_id,
            UtilityRecord.billing_year == prev_year,
            UtilityRecord.billing_month == prev_month
        ).first()

        return prev_record.current_electricity_reading if prev_record else 0.0

    @staticmethod
    def calculate_monthly_bill(db: Session, room_id: int, year: int, month: int,
                             current_reading: float):
        """計算月度帳單"""
        from datetime import datetime, date

        room = db.query(Room).filter(Room.id == room_id).first()
        if not room:
            raise ValueError("房間不存在")

        # 計算帳單日期（該月的最後一天）
        if month == 12:
            next_month = datetime(year + 1, 1, 1)
        else:
            next_month = datetime(year, month + 1, 1)

        # 帳單日期為該月最後一天
        from datetime import timedelta
        bill_date = next_month - timedelta(days=1)

        # 根據帳單日期獲取合適的費率
        rate = UtilityService.get_rate_for_date(db, bill_date)
        if not rate:
            raise ValueError(f"未找到適用於 {bill_date.strftime('%Y-%m-%d')} 的費率")

        # 獲取上月讀數
        previous_reading = UtilityService.get_previous_reading(db, room_id, year, month)

        # 計算用電量
        electricity_usage = current_reading - previous_reading
        electricity_cost = electricity_usage * rate.electricity_rate

        # 水費計算（根據住戶數量分攤）
        water_fee = rate.monthly_water_fee / room.current_occupants if room.current_occupants > 0 else rate.monthly_water_fee

        total_amount = electricity_cost + water_fee

        # 建立記錄
        record = UtilityRecord(
            room_id=room_id,
            billing_year=year,
            billing_month=month,
            previous_electricity_reading=previous_reading,
            current_electricity_reading=current_reading,
            electricity_usage=electricity_usage,
            electricity_rate=rate.electricity_rate,
            electricity_cost=electricity_cost,
            water_fee=water_fee,
            total_amount=total_amount
        )

        db.add(record)
        db.commit()
        db.refresh(record)
        return record

    @staticmethod
    def get_monthly_bills(db: Session, year: int, month: int):
        """獲取月度帳單"""
        return db.query(UtilityRecord).filter(
            UtilityRecord.billing_year == year,
            UtilityRecord.billing_month == month
        ).all()

    @staticmethod
    def update_payment_status(db: Session, bill_id: int, payment_status: str, payment_date: datetime = None):
        """更新付款狀態"""
        bill = db.query(UtilityRecord).filter(UtilityRecord.id == bill_id).first()
        if bill:
            bill.payment_status = payment_status
            if payment_date:
                bill.payment_date = payment_date
            db.commit()
            return bill
        return None

class RentService:
    """租金服務"""

    @staticmethod
    def create_rent_record(db: Session, rent_data: dict):
        """創建租金記錄"""
        try:
            record = RentRecord(**rent_data)
            db.add(record)
            db.commit()
            db.refresh(record)
            return record
        except Exception:
            db.rollback()
            raise

    @staticmethod
    def get_rent_records(db: Session, year: int = None, month: int = None,
                        room_id: int = None, payment_status: str = None):
        """獲取租金記錄"""
        query = db.query(RentRecord)

        if year:
            query = query.filter(RentRecord.rent_year == year)
        if month:
            query = query.filter(RentRecord.rent_month == month)
        if room_id:
            query = query.filter(RentRecord.room_id == room_id)
        if payment_status:
            query = query.filter(RentRecord.payment_status == payment_status)

        return query.order_by(RentRecord.rent_year.desc(), RentRecord.rent_month.desc()).all()

    @staticmethod
    def get_rent_record_by_id(db: Session, record_id: int):
        """根據ID獲取租金記錄"""
        return db.query(RentRecord).filter(RentRecord.id == record_id).first()

    @staticmethod
    def get_rent_record_by_period(db: Session, room_id: int, resident_id: int, year: int, month: int):
        """根據期間獲取租金記錄"""
        return db.query(RentRecord).filter(
            RentRecord.room_id == room_id,
            RentRecord.resident_id == resident_id,
            RentRecord.rent_year == year,
            RentRecord.rent_month == month
        ).first()

    @staticmethod
    def update_rent_record(db: Session, record_id: int, update_data: dict):
        """更新租金記錄"""
        try:
            record = db.query(RentRecord).filter(RentRecord.id == record_id).first()
            if not record:
                return None

            for key, value in update_data.items():
                if hasattr(record, key) and value is not None:
                    setattr(record, key, value)

            db.commit()
            db.refresh(record)
            return record
        except Exception:
            db.rollback()
            raise

    @staticmethod
    def delete_rent_record(db: Session, record_id: int) -> bool:
        """刪除租金記錄"""
        try:
            record = db.query(RentRecord).filter(RentRecord.id == record_id).first()
            if not record:
                return False

            db.delete(record)
            db.commit()
            return True
        except Exception:
            db.rollback()
            return False

    @staticmethod
    def get_rent_statistics(db: Session, year: int = None, month: int = None):
        """獲取租金統計"""
        try:
            query = db.query(RentRecord)

            if year:
                query = query.filter(RentRecord.rent_year == year)
            if month:
                query = query.filter(RentRecord.rent_month == month)

            records = query.all()

            total_records = len(records)
            total_amount = sum(record.rent_amount for record in records)
            paid_records = [r for r in records if r.payment_status == '已付款']
            paid_amount = sum(record.rent_amount for record in paid_records)
            unpaid_records = [r for r in records if r.payment_status == '待付款']
            overdue_records = [r for r in records if r.payment_status == '逾期']

            return {
                "total_records": total_records,
                "total_amount": total_amount,
                "paid_records": len(paid_records),
                "paid_amount": paid_amount,
                "unpaid_records": len(unpaid_records),
                "overdue_records": len(overdue_records),
                "collection_rate": (paid_amount / total_amount * 100) if total_amount > 0 else 0
            }
        except Exception:
            return {
                "total_records": 0,
                "total_amount": 0,
                "paid_records": 0,
                "paid_amount": 0,
                "unpaid_records": 0,
                "overdue_records": 0,
                "collection_rate": 0
            }
