#!/usr/bin/env python3
"""檢查資料庫中的用戶"""

import sqlite3

def check_users():
    """檢查 backend 資料庫中的用戶"""
    # 檢查 backend 資料庫中的用戶
    conn = sqlite3.connect('backend/rental_management.db')
    cursor = conn.cursor()

    print('📋 檢查 backend 資料庫中的用戶:')
    print('=' * 40)

    try:
        cursor.execute('SELECT username, role, email FROM users')
        users = cursor.fetchall()

        if users:
            print('✅ 找到以下用戶:')
            for user in users:
                username, role, email = user
                print(f'  - {username} ({role}) - {email}')
        else:
            print('❌ 沒有找到任何用戶')
            
    except Exception as e:
        print(f'❌ 查詢用戶時發生錯誤: {e}')
        
        # 檢查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print('✅ users 表存在')
        else:
            print('❌ users 表不存在')

    conn.close()

if __name__ == "__main__":
    check_users()
