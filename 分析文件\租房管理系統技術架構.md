# 租房管理系統簡捷架構分析

## 核心架構設計原則

### 系統設計哲學

**簡潔性導向架構：**
```
設計核心原則
├── 單一職責模組化
├── 最小可行產品(MVP)思維
├── 直觀的程式碼結構
└── 低耦合高內聚設計
```

**技術棧協同效能：**
- **Python統一生態**：減少語言切換成本
- **FastAPI輕量後端**：自動文檔、類型檢查、快速開發
- **Streamlit簡化前端**：專注業務邏輯，免除複雜UI框架
- **SQLite零配置**：檔案型資料庫，部署簡單
- **JWT無狀態認證**：stateless設計，易於擴展

### 精簡模組架構

#### 1. 核心資料模型設計

**SQLAlchemy簡化實體：**
```python
# 核心業務實體
資料模型架構
├── BaseModel
│   ├── 通用欄位 (id, created_at, updated_at)
│   └── 軟刪除標記 (is_active)
├── User
│   ├── 認證資訊 (username, password_hash, role)
│   └── 基本資料 (email, created_at)
├── Room
│   ├── 房間資訊 (room_number, rent_single, rent_double)
│   ├── 狀態管理 (current_occupants, max_occupants=2)
│   └── 關聯住戶 (residents關係)
├── Resident
│   ├── 個人資料 (name, phone, id_number)
│   ├── 住宿資訊 (room_id, move_in_date, move_out_date)
│   └── 狀態追蹤 (is_active)
├── UtilityRate
│   ├── 費率設定 (electricity_rate, monthly_water_fee)
│   └── 生效時間 (effective_date)
└── MonthlyBill
    ├── 計費資訊 (room_id, billing_month, billing_year)
    ├── 用量記錄 (electricity_usage, water_fee)
    └── 費用計算 (total_amount, payment_status)
```

#### 2. FastAPI後端精簡架構

**模組化API設計：**
```python
# 後端架構組織
API結構設計
├── main.py
│   ├── 應用程式初始化
│   ├── 路由註冊
│   ├── CORS設定
│   └── 基本中間件
├── models.py
│   ├── SQLAlchemy模型定義
│   ├── Pydantic請求/回應模型
│   └── 資料驗證規則
├── database.py
│   ├── SQLite連接設定
│   ├── 資料庫初始化
│   └── 會話管理
├── auth.py
│   ├── JWT處理函數
│   ├── 密碼加密/驗證
│   ├── 權限檢查裝飾器
│   └── 認證相關路由
├── routers/
│   ├── residents.py (住戶管理API)
│   ├── rooms.py (房間管理API)
│   ├── utilities.py (費用管理API)
│   └── reports.py (報表API)
├── services.py
│   ├── 業務邏輯封裝
│   ├── 租金計算函數
│   ├── 費用分攤邏輯
│   └── 資料處理工具
└── config.py
    ├── 環境設定
    ├── 資料庫配置
    └── JWT設定
```

#### 3. Streamlit前端簡化設計

**頁面導向架構：**
```python
# 前端組織結構
Streamlit應用架構
├── app.py
│   ├── 主程式入口
│   ├── 頁面路由邏輯
│   ├── 會話狀態管理
│   └── 全域配置
├── pages/
│   ├── login.py (登入頁面)
│   ├── dashboard.py (主控台)
│   ├── residents.py (住戶管理)
│   ├── rooms.py (房間管理)
│   ├── utilities.py (費用管理)
│   └── reports.py (報表檢視)
├── components/
│   ├── forms.py (表單元件)
│   ├── tables.py (資料表格)
│   ├── charts.py (圖表元件)
│   └── navigation.py (導航元件)
├── api_client.py
│   ├── HTTP請求封裝
│   ├── 認證處理
│   ├── 錯誤處理
│   └── 資料轉換
├── utils.py
│   ├── 常用工具函數
│   ├── 資料格式化
│   ├── 驗證函數
│   └── 常量定義
└── config.py
    ├── API端點配置
    ├── 頁面設定
    └── 樣式配置
```

## 實用開發策略

### 1. 認證與授權簡化實現

**JWT整合方案：**
```python
# 認證系統核心要素
認證架構設計
├── 用戶登入流程
│   ├── 使用者名稱/密碼驗證
│   ├── bcrypt密碼雜湊
│   ├── JWT token生成
│   └── token回傳給前端
├── 權限控制機制
│   ├── 角色定義 (admin, manager, user)
│   ├── 裝飾器權限檢查
│   ├── API端點保護
│   └── 前端頁面訪問控制
├── 會話管理
│   ├── Streamlit session_state
│   ├── token自動續期
│   ├── 登出處理
│   └── 過期檢查
└── 安全性考量
    ├── 密碼強度檢查
    ├── 登入失敗限制
    ├── token過期時間設定
    └── 敏感操作二次驗證
```

### 2. 業務邏輯核心實現

**租金計算引擎：**
```python
# 費用計算核心邏輯
計算系統架構
├── 租金計算模組
│   ├── 基礎租金獲取 (room.rent_single/rent_double)
│   ├── 住戶數量判斷 (current_occupants)
│   ├── 按日比例計算 (入住/退房日期)
│   └── 特殊情況處理 (月中入住/退房)
├── 公用事業費用分攤
│   ├── 電費計算 (usage * electricity_rate)
│   ├── 水費分攤 (monthly_water_fee / occupants)
│   ├── 房間費用分配
│   └── 個人費用計算
├── 月度帳單生成
│   ├── 自動計費觸發
│   ├── 費用項目彙總
│   ├── 帳單狀態管理
│   └── 付款記錄追蹤
└── 資料驗證機制
    ├── 輸入資料檢查
    ├── 計算結果驗證
    ├── 異常情況處理
    └── 錯誤回饋機制
```

### 3. 資料庫操作簡化

**SQLite最佳實踐：**
```python
# 資料庫操作架構
資料存取層設計
├── 模型定義標準化
│   ├── 統一BaseModel繼承
│   ├── 標準化欄位命名
│   ├── 關聯關係定義
│   └── 索引策略設計
├── CRUD操作封裝
│   ├── 通用查詢函數
│   ├── 批量操作支援
│   ├── 事務處理包裝
│   └── 異常處理機制
├── 查詢優化策略
│   ├── 適當索引建立
│   ├── 查詢條件優化
│   ├── 分頁查詢實現
│   └── 關聯查詢最佳化
└── 資料維護工具
    ├── 資料庫初始化腳本
    ├── 種子資料建立
    ├── 備份恢復機制
    └── 版本遷移管理
```

## 開發實施路徑

### 階段化開發策略

#### Phase 1: 基礎架構建立
```python
# 第一階段開發重點
基礎建設階段
├── 專案結構建立
│   ├── 目錄結構規劃
│   ├── 虛擬環境設定
│   ├── 依賴套件安裝
│   └── 基本配置檔案
├── 資料庫架構實現
│   ├── SQLAlchemy模型定義
│   ├── 資料庫連接設定
│   ├── 初始化腳本建立
│   └── 基本資料建立
├── 認證系統開發
│   ├── JWT處理函數
│   ├── 用戶模型實現
│   ├── 登入API開發
│   └── 權限檢查機制
└── 基礎API架構
    ├── FastAPI應用設定
    ├── 路由結構建立
    ├── 基本CRUD操作
    └── 錯誤處理機制
```

#### Phase 2: 核心功能開發
```python
# 第二階段開發重點
核心功能階段
├── 住戶管理系統
│   ├── 住戶資料CRUD
│   ├── 入住/退房處理
│   ├── 住戶狀態管理
│   └── 資料驗證機制
├── 房間管理系統
│   ├── 房間資料管理
│   ├── 租金設定功能
│   ├── 住戶分配邏輯
│   └── 房間狀態追蹤
├── 費用管理基礎
│   ├── 費率設定功能
│   ├── 基礎計費邏輯
│   ├── 帳單生成機制
│   └── 付款狀態管理
└── Streamlit前端開發
    ├── 基礎頁面建立
    ├── 表單元件開發
    ├── 資料展示介面
    └── 用戶互動邏輯
```

#### Phase 3: 系統整合與完善
```python
# 第三階段開發重點
系統整合階段
├── 前後端整合
│   ├── API客戶端完善
│   ├── 認證流程整合
│   ├── 錯誤處理統一
│   └── 用戶體驗優化
├── 業務邏輯完善
│   ├── 複雜計費邏輯
│   ├── 特殊情況處理
│   ├── 資料一致性檢查
│   └── 業務規則驗證
├── 報表與統計
│   ├── 基礎報表功能
│   ├── 統計圖表實現
│   ├── 資料匯出功能
│   └── 查詢篩選機制
└── 系統測試與部署
    ├── 功能測試執行
    ├── 整合測試驗證
    ├── 部署腳本準備
    └── 文檔整理完善
```

### 維護性設計考量

#### 程式碼組織原則

**可維護性架構：**
```python
# 維護性導向設計
可維護架構原則
├── 模組職責清晰
│   ├── 單一職責原則
│   ├── 功能內聚性高
│   ├── 模組間低耦合
│   └── 介面設計簡潔
├── 程式碼可讀性
│   ├── 命名規範統一
│   ├── 註釋文檔完整
│   ├── 程式碼結構清晰
│   └── 邏輯流程明確
├── 錯誤處理機制
│   ├── 異常處理標準化
│   ├── 日誌記錄完整
│   ├── 用戶友好提示
│   └── 系統恢復能力
└── 測試覆蓋策略
    ├── 單元測試覆蓋
    ├── 整合測試驗證
    ├── 手動測試清單
    └── 回歸測試機制
```

#### 部署與運維簡化

**輕量化部署策略：**
```python
# 部署運維架構
部署策略設計
├── 本地開發環境
│   ├── 虛擬環境管理
│   ├── 開發工具配置
│   ├── 本地資料庫設定
│   └── 除錯工具整合
├── 測試環境部署
│   ├── 測試資料準備
│   ├── 功能驗證流程
│   ├── 性能基準測試
│   └── 安全性檢查
├── 生產環境部署
│   ├── 伺服器環境準備
│   ├── 依賴套件安裝
│   ├── 資料庫遷移執行
│   └── 服務啟動配置
└── 監控與維護
    ├── 日誌監控機制
    ├── 錯誤告警系統
    ├── 備份恢復程序
    └── 版本更新流程
```

這個簡化架構分析專注於實用性和可維護性，避免過度設計，確保系統能夠快速開發、容易維護，並滿足租房管理的核心業務需求。