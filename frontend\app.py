import streamlit as st
from config import config
from utils import init_session_state, logout_user
from pages.login import show_login_page
from pages.dashboard import show_dashboard_page
from pages.rooms import show_rooms_page
from pages.residents import show_residents_page
from pages.utilities import show_utilities_page
from pages.reports import show_reports_page
from pages.profile import show_profile_page
from pages.user_management import show_user_management_page


# 頁面配置
st.set_page_config(**config.PAGE_CONFIG)

# 初始化會話狀態
init_session_state()

def show_sidebar():
    """顯示側邊欄導航"""
    with st.sidebar:
        st.title(f"{config.APP_ICON} {config.APP_TITLE}")

        if st.session_state.authenticated:
            st.success(f"歡迎, {st.session_state.user_info['username']}")
            st.caption(f"角色: {st.session_state.user_info['role']}")

            # 導航選單
            st.markdown("### 📋 功能選單")

            pages = {
                "📊 儀表板": "dashboard",
                "🏠 房間管理": "rooms",
                "👥 住戶管理": "residents",
                "💰 費用管理": "utilities",
                "📈 報表統計": "reports"
            }

            # 只有admin用戶才能看到管理功能
            user_role = st.session_state.user_info.get('role', '')
            if user_role == 'admin':
                pages["👥 帳號管理"] = "user_management"

            # 設定預設頁面
            if 'page' not in st.session_state:
                st.session_state.page = "dashboard"

            # 頁面按鈕
            for page_name, page_key in pages.items():
                if st.button(page_name, key=f"sidebar_{page_key}", use_container_width=True):
                    st.session_state.page = page_key
                    st.rerun()

            st.markdown("---")

            # 個人資料按鈕
            if st.button("👤 個人資料", key="sidebar_profile", use_container_width=True):
                st.session_state.page = "profile"
                st.rerun()

            # 登出按鈕
            if st.button("🚪 登出", key="sidebar_logout", use_container_width=True):
                logout_user()
                st.rerun()

            # 系統資訊
            with st.expander("ℹ️ 系統資訊"):
                st.markdown(f"""
                **版本**: 1.0.0
                **狀態**: 運行中 🟢
                **API**: 已連接
                **用戶**: {st.session_state.user_info['username']}
                **角色**: {st.session_state.user_info['role']}
                """)

            return st.session_state.page
        else:
            st.info("請先登入系統")
            return "login"

def main():
    """主程式"""
    # 顯示導航
    current_page = show_sidebar()

    # 路由處理
    if current_page == "login":
        show_login_page()
    elif current_page == "dashboard":
        show_dashboard_page()
    elif current_page == "rooms":
        show_rooms_page()
    elif current_page == "residents":
        show_residents_page()
    elif current_page == "utilities":
        show_utilities_page()

    elif current_page == "reports":
        show_reports_page()
    elif current_page == "profile":
        show_profile_page()
    elif current_page == "user_management":
        show_user_management_page()
    else:
        st.error("頁面不存在")

if __name__ == "__main__":
    main()