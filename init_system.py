#!/usr/bin/env python3
"""
租房管理系統初始化腳本
用於創建數據庫、初始化數據和創建預設管理員帳號
"""

import sys
import os
from datetime import datetime

# 添加後端路徑到Python路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.app.database import create_tables, SessionLocal
from backend.app.models import User, UtilityRate
from backend.app.auth import get_password_hash
from backend.app.config import settings

def create_default_admin():
    """創建預設管理員帳號"""
    db = SessionLocal()
    try:
        # 檢查是否已存在管理員
        existing_admin = db.query(User).filter(User.username == "admin").first()
        if existing_admin:
            print("✅ 管理員帳號已存在")
            return
        
        # 創建管理員帳號
        admin_user = User(
            username="admin",
            password_hash=get_password_hash("admin123"),
            email="<EMAIL>",
            role="admin"
        )
        
        db.add(admin_user)
        db.commit()
        print("✅ 預設管理員帳號創建成功")
        print("   用戶名: admin")
        print("   密碼: admin123")
        
    except Exception as e:
        print(f"❌ 創建管理員帳號失敗: {e}")
        db.rollback()
    finally:
        db.close()

def create_default_utility_rate():
    """創建預設費率設定"""
    db = SessionLocal()
    try:
        # 檢查是否已存在費率設定
        existing_rate = db.query(UtilityRate).first()
        if existing_rate:
            print("✅ 費率設定已存在")
            return
        
        # 創建預設費率
        default_rate = UtilityRate(
            electricity_rate=settings.default_electricity_rate,
            monthly_water_fee=settings.default_water_fee,
            effective_date=datetime.utcnow(),
            notes="系統預設費率"
        )
        
        db.add(default_rate)
        db.commit()
        print("✅ 預設費率設定創建成功")
        print(f"   電費: ${settings.default_electricity_rate}/度")
        print(f"   水費: ${settings.default_water_fee}/月")
        
    except Exception as e:
        print(f"❌ 創建費率設定失敗: {e}")
        db.rollback()
    finally:
        db.close()

def create_sample_data():
    """創建範例數據（可選）"""
    from backend.app.models import Room
    
    db = SessionLocal()
    try:
        # 檢查是否已有房間數據
        existing_rooms = db.query(Room).count()
        if existing_rooms > 0:
            print("✅ 房間數據已存在")
            return
        
        # 創建範例房間
        sample_rooms = [
            Room(room_number="A101", floor=1, area=10.0, rent_single=12000, rent_double=18000, description="雅房，有窗戶"),
            Room(room_number="A102", floor=1, area=12.0, rent_single=13000, rent_double=19000, description="套房，有獨立衛浴"),
            Room(room_number="A201", floor=2, area=10.0, rent_single=12000, rent_double=18000, description="雅房，採光良好"),
            Room(room_number="A202", floor=2, area=15.0, rent_single=15000, rent_double=22000, description="大套房，有陽台"),
            Room(room_number="B101", floor=1, area=8.0, rent_single=10000, rent_double=15000, description="小雅房"),
        ]
        
        for room in sample_rooms:
            db.add(room)
        
        db.commit()
        print("✅ 範例房間數據創建成功")
        print(f"   創建了 {len(sample_rooms)} 間範例房間")
        
    except Exception as e:
        print(f"❌ 創建範例數據失敗: {e}")
        db.rollback()
    finally:
        db.close()

def main():
    """主初始化流程"""
    print("🏠 租房管理系統初始化")
    print("=" * 50)
    
    try:
        # 1. 創建數據庫表格
        print("📊 創建數據庫表格...")
        create_tables()
        print("✅ 數據庫表格創建成功")
        
        # 2. 創建預設管理員
        print("\n👤 創建預設管理員帳號...")
        create_default_admin()
        
        # 3. 創建預設費率設定
        print("\n💰 創建預設費率設定...")
        create_default_utility_rate()
        
        # 4. 詢問是否創建範例數據
        print("\n📋 是否創建範例數據？")
        create_sample = input("輸入 'y' 創建範例房間數據，其他鍵跳過: ").lower().strip()
        
        if create_sample == 'y':
            print("\n🏠 創建範例數據...")
            create_sample_data()
        else:
            print("⏭️ 跳過範例數據創建")
        
        print("\n" + "=" * 50)
        print("🎉 系統初始化完成！")
        print("\n📝 接下來的步驟：")
        print("1. 啟動後端服務: python run_backend.py")
        print("2. 啟動前端服務: python run_frontend.py")
        print("3. 在瀏覽器中訪問: http://localhost:8501")
        print("4. 使用管理員帳號登入: admin / admin123")
        
    except Exception as e:
        print(f"\n❌ 初始化失敗: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
