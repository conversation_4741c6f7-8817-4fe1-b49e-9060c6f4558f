from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel, validator
from typing import List, Optional, Union
from datetime import datetime, date
from ..database import get_db
from ..models import User
from ..services import ComprehensiveBillService
from ..auth import get_current_user

router = APIRouter(prefix="/comprehensive-bills", tags=["comprehensive-bills"])

class ComprehensiveBillCreate(BaseModel):
    room_id: int
    billing_year: int
    billing_month: int
    current_electricity_reading: float
    due_date: Optional[Union[date, str]] = None
    notes: Optional[str] = None
    
    @validator('due_date', pre=True)
    def parse_due_date(cls, v):
        """解析到期日期格式"""
        if v is None:
            return None
        if isinstance(v, str):
            try:
                return datetime.strptime(v, '%Y-%m-%d').date()
            except ValueError:
                try:
                    return datetime.fromisoformat(v.replace('Z', '+00:00')).date()
                except ValueError:
                    raise ValueError('到期日期格式錯誤，請使用 YYYY-MM-DD 格式')
        elif isinstance(v, datetime):
            return v.date()
        elif isinstance(v, date):
            return v
        else:
            raise ValueError('到期日期格式錯誤，請使用 YYYY-MM-DD 格式')
    
    @validator('current_electricity_reading')
    def validate_electricity_reading(cls, v):
        if v < 0:
            raise ValueError('電表讀數不能為負數')
        return v
    
    @validator('billing_year')
    def validate_billing_year(cls, v):
        if v < 2020 or v > 2050:
            raise ValueError('計費年份必須在2020-2050之間')
        return v
    
    @validator('billing_month')
    def validate_billing_month(cls, v):
        if v < 1 or v > 12:
            raise ValueError('計費月份必須在1-12之間')
        return v

class PaymentStatusUpdate(BaseModel):
    payment_status: str
    payment_date: Optional[Union[date, str]] = None
    
    @validator('payment_status')
    def validate_payment_status(cls, v):
        valid_statuses = ['pending', 'paid', 'overdue']
        if v not in valid_statuses:
            raise ValueError(f'付款狀態必須是以下之一: {", ".join(valid_statuses)}')
        return v
    
    @validator('payment_date', pre=True)
    def parse_payment_date(cls, v):
        """解析付款日期格式"""
        if v is None:
            return None
        if isinstance(v, str):
            try:
                # 處理包含時間的ISO格式
                if 'T' in v:
                    return datetime.fromisoformat(v.replace('Z', '+00:00')).date()
                else:
                    return datetime.strptime(v, '%Y-%m-%d').date()
            except ValueError:
                raise ValueError('付款日期格式錯誤，請使用 YYYY-MM-DD 或 ISO 格式')
        elif isinstance(v, datetime):
            return v.date()
        elif isinstance(v, date):
            return v
        else:
            raise ValueError('付款日期格式錯誤')

class ComprehensiveBillUpdate(BaseModel):
    rent_amount: Optional[float] = None
    water_fee: Optional[float] = None
    current_electricity_reading: Optional[float] = None
    previous_electricity_reading: Optional[float] = None
    electricity_rate: Optional[float] = None
    electricity_usage: Optional[float] = None
    electricity_cost: Optional[float] = None
    total_amount: Optional[float] = None
    payment_status: Optional[str] = None
    payment_date: Optional[Union[date, str]] = None
    due_date: Optional[Union[date, str]] = None
    notes: Optional[str] = None

    @validator('payment_status')
    def validate_payment_status(cls, v):
        if v is not None:
            valid_statuses = ['pending', 'paid', 'overdue']
            if v not in valid_statuses:
                raise ValueError(f'付款狀態必須是以下之一: {", ".join(valid_statuses)}')
        return v

    @validator('payment_date', pre=True)
    def parse_payment_date(cls, v):
        """解析付款日期格式"""
        if v is None:
            return None
        if isinstance(v, str):
            try:
                # 處理包含時間的ISO格式
                if 'T' in v:
                    return datetime.fromisoformat(v.replace('Z', '+00:00')).date()
                else:
                    return datetime.strptime(v, '%Y-%m-%d').date()
            except ValueError:
                raise ValueError('付款日期格式錯誤，請使用 YYYY-MM-DD 或 ISO 格式')
        elif isinstance(v, datetime):
            return v.date()
        elif isinstance(v, date):
            return v
        else:
            raise ValueError('付款日期格式錯誤')

    @validator('due_date', pre=True)
    def parse_due_date(cls, v):
        """解析到期日期格式"""
        if v is None:
            return None
        if isinstance(v, str):
            try:
                # 處理包含時間的ISO格式
                if 'T' in v:
                    return datetime.fromisoformat(v.replace('Z', '+00:00')).date()
                else:
                    return datetime.strptime(v, '%Y-%m-%d').date()
            except ValueError:
                raise ValueError('到期日期格式錯誤，請使用 YYYY-MM-DD 或 ISO 格式')
        elif isinstance(v, datetime):
            return v.date()
        elif isinstance(v, date):
            return v
        else:
            raise ValueError('到期日期格式錯誤')

    @validator('rent_amount', 'water_fee', 'electricity_rate', 'total_amount')
    def validate_positive_amounts(cls, v):
        if v is not None and v < 0:
            raise ValueError('金額不能為負數')
        return v

    @validator('current_electricity_reading', 'previous_electricity_reading')
    def validate_electricity_readings(cls, v):
        if v is not None and v < 0:
            raise ValueError('電表讀數不能為負數')
        return v

@router.post("/", response_model=dict)
async def create_comprehensive_bill(
    bill_data: ComprehensiveBillCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """創建綜合帳單"""
    try:
        bill = ComprehensiveBillService.calculate_comprehensive_bill(
            db=db,
            room_id=bill_data.room_id,
            year=bill_data.billing_year,
            month=bill_data.billing_month,
            current_electricity_reading=bill_data.current_electricity_reading,
            due_date=bill_data.due_date
        )
        
        # 如果有備註，更新備註
        if bill_data.notes:
            bill.notes = bill_data.notes
            db.commit()
            db.refresh(bill)
        
        return {
            "message": "綜合帳單創建成功",
            "bill": bill.to_dict()
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"創建綜合帳單失敗: {str(e)}")

@router.get("/", response_model=List[dict])
async def get_comprehensive_bills(
    year: Optional[int] = Query(None, ge=2020, le=2050),
    month: Optional[int] = Query(None, ge=1, le=12),
    room_id: Optional[int] = Query(None),
    payment_status: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """獲取綜合帳單列表"""
    try:
        bills = ComprehensiveBillService.get_comprehensive_bills(
            db=db,
            year=year,
            month=month,
            room_id=room_id,
            payment_status=payment_status
        )
        
        return [bill.to_dict() for bill in bills]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"獲取綜合帳單失敗: {str(e)}")

@router.get("/{bill_id}", response_model=dict)
async def get_comprehensive_bill(
    bill_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """獲取單個綜合帳單"""
    try:
        bill = ComprehensiveBillService.get_comprehensive_bill_by_id(db, bill_id)
        if not bill:
            raise HTTPException(status_code=404, detail="綜合帳單不存在")
        
        return bill.to_dict()
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"獲取綜合帳單失敗: {str(e)}")

@router.put("/{bill_id}/payment", response_model=dict)
async def update_payment_status(
    bill_id: int,
    payment_data: PaymentStatusUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新付款狀態"""
    try:
        bill = ComprehensiveBillService.update_payment_status(
            db=db,
            bill_id=bill_id,
            payment_status=payment_data.payment_status,
            payment_date=payment_data.payment_date
        )
        
        return {
            "message": "付款狀態更新成功",
            "bill": bill.to_dict()
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新付款狀態失敗: {str(e)}")

@router.get("/summary/{year}/{month}", response_model=dict)
async def get_monthly_summary(
    year: int,
    month: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """獲取月度綜合帳單摘要"""
    try:
        if year < 2020 or year > 2050:
            raise HTTPException(status_code=400, detail="年份必須在2020-2050之間")
        if month < 1 or month > 12:
            raise HTTPException(status_code=400, detail="月份必須在1-12之間")
        
        summary = ComprehensiveBillService.get_monthly_summary(db, year, month)
        return {
            "year": year,
            "month": month,
            "summary": summary
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"獲取月度摘要失敗: {str(e)}")

@router.put("/{bill_id}", response_model=dict)
async def update_comprehensive_bill(
    bill_id: int,
    bill_data: ComprehensiveBillUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新綜合帳單"""
    try:
        # 準備更新數據
        update_data = {}

        # 只更新提供的欄位
        if bill_data.rent_amount is not None:
            update_data['rent_amount'] = bill_data.rent_amount
        if bill_data.water_fee is not None:
            update_data['water_fee'] = bill_data.water_fee
        if bill_data.current_electricity_reading is not None:
            update_data['current_electricity_reading'] = bill_data.current_electricity_reading
        if bill_data.previous_electricity_reading is not None:
            update_data['previous_electricity_reading'] = bill_data.previous_electricity_reading
        if bill_data.electricity_rate is not None:
            update_data['electricity_rate'] = bill_data.electricity_rate
        if bill_data.electricity_usage is not None:
            update_data['electricity_usage'] = bill_data.electricity_usage
        if bill_data.electricity_cost is not None:
            update_data['electricity_cost'] = bill_data.electricity_cost
        if bill_data.total_amount is not None:
            update_data['total_amount'] = bill_data.total_amount
        if bill_data.payment_status is not None:
            update_data['payment_status'] = bill_data.payment_status
        if bill_data.payment_date is not None:
            update_data['payment_date'] = bill_data.payment_date
        if bill_data.due_date is not None:
            update_data['due_date'] = bill_data.due_date
        if bill_data.notes is not None:
            update_data['notes'] = bill_data.notes

        # 執行更新
        result = ComprehensiveBillService.update_comprehensive_bill(db, bill_id, update_data)

        if not result:
            raise HTTPException(status_code=404, detail="綜合帳單不存在")

        return {
            "message": "綜合帳單更新成功",
            "bill": result
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新綜合帳單失敗: {str(e)}")
