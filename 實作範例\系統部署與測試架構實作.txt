# =================== tests/conftest.py ===================
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from backend.app.database import Base, get_db
from backend.app.main import app
from backend.app.models import User, Room, Resident, UtilityRate, UtilityRecord
from backend.app.auth import get_password_hash
from datetime import datetime

# 測試資料庫設定
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False}
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

@pytest.fixture(scope="session")
def db():
    """測試資料庫固定資料"""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)

@pytest.fixture(scope="function")
def db_session(db):
    """資料庫會話"""
    connection = engine.connect()
    transaction = connection.begin()
    session = TestingSessionLocal(bind=connection)
    yield session
    session.close()
    transaction.rollback()
    connection.close()

@pytest.fixture(scope="function")
def client(db_session):
    """測試客戶端"""
    def override_get_db():
        yield db_session
    
    app.dependency_overrides[get_db] = override_get_db
    yield TestClient(app)
    app.dependency_overrides.clear()

@pytest.fixture
def test_user(db_session):
    """測試用戶"""
    user = User(
        username="testuser",
        password_hash=get_password_hash("testpass"),
        email="<EMAIL>",
        role="user"
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user

@pytest.fixture
def test_admin(db_session):
    """測試管理員"""
    admin = User(
        username="admin",
        password_hash=get_password_hash("admin123"),
        email="<EMAIL>",
        role="admin"
    )
    db_session.add(admin)
    db_session.commit()
    db_session.refresh(admin)
    return admin

@pytest.fixture
def test_room(db_session):
    """測試房間"""
    room = Room(
        room_number="A101",
        floor=1,
        area=20.0,
        rent_single=10000,
        rent_double=15000,
        description="測試房間"
    )
    db_session.add(room)
    db_session.commit()
    db_session.refresh(room)
    return room

@pytest.fixture
def test_resident(db_session, test_room):
    """測試住戶"""
    resident = Resident(
        name="測試住戶",
        id_number="A123456789",
        phone="0912345678",
        room_id=test_room.id,
        move_in_date=datetime.now(),
        deposit=10000.0
    )
    db_session.add(resident)
    db_session.commit()
    db_session.refresh(resident)
    return resident

@pytest.fixture
def test_utility_rate(db_session):
    """測試費率"""
    rate = UtilityRate(
        electricity_rate=5.5,
        monthly_water_fee=200.0,
        effective_date=datetime.now(),
        notes="測試費率"
    )
    db_session.add(rate)
    db_session.commit()
    db_session.refresh(rate)
    return rate

@pytest.fixture
def auth_headers(client, test_user):
    """認證標頭"""
    response = client.post("/auth/login", data={
        "username": test_user.username,
        "password": "testpass"
    })
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}

@pytest.fixture
def admin_headers(client, test_admin):
    """管理員認證標頭"""
    response = client.post("/auth/login", data={
        "username": test_admin.username,
        "password": "admin123"
    })
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}

# =================== tests/test_auth.py ===================
import pytest
from fastapi import status

def test_login_success(client, test_user):
    """測試登入成功"""
    response = client.post("/auth/login", data={
        "username": test_user.username,
        "password": "testpass"
    })
    
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"

def test_login_invalid_credentials(client):
    """測試登入失敗"""
    response = client.post("/auth/login", data={
        "username": "invalid",
        "password": "invalid"
    })
    
    assert response.status_code == status.HTTP_401_UNAUTHORIZED

def test_get_current_user(client, auth_headers):
    """測試獲取當前用戶"""
    response = client.get("/auth/me", headers=auth_headers)
    
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["username"] == "testuser"
    assert data["role"] == "user"

def test_register_user(client):
    """測試用戶註冊"""
    response = client.post("/auth/register", json={
        "username": "newuser",
        "password": "newpass",
        "email": "<EMAIL>"
    })
    
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["message"] == "用戶註冊成功"

def test_register_duplicate_username(client, test_user):
    """測試重複用戶名註冊"""
    response = client.post("/auth/register", json={
        "username": test_user.username,
        "password": "password"
    })
    
    assert response.status_code == status.HTTP_400_BAD_REQUEST

# =================== tests/test_rooms.py ===================
import pytest
from fastapi import status

def test_get_rooms(client, auth_headers, test_room):
    """測試獲取房間列表"""
    response = client.get("/rooms/", headers=auth_headers)
    
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert len(data) > 0
    assert data[0]["room_number"] == test_room.room_number

def test_create_room(client, admin_headers):
    """測試創建房間"""
    room_data = {
        "room_number": "B101",
        "floor": 1,
        "area": 25.0,
        "rent_single": 12000,
        "rent_double": 18000,
        "description": "新測試房間"
    }
    
    response = client.post("/rooms/", json=room_data, headers=admin_headers)
    
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["message"] == "房間創建成功"

def test_create_room_permission_denied(client, auth_headers):
    """測試權限不足創建房間"""
    room_data = {
        "room_number": "C101",
        "rent_single": 10000,
        "rent_double": 15000
    }
    
    response = client.post("/rooms/", json=room_data, headers=auth_headers)
    
    assert response.status_code == status.HTTP_403_FORBIDDEN

def test_get_available_rooms(client, auth_headers, test_room):
    """測試獲取可用房間"""
    response = client.get("/rooms/available", headers=auth_headers)
    
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert len(data) > 0

def test_get_room_by_id(client, auth_headers, test_room):
    """測試根據ID獲取房間"""
    response = client.get(f"/rooms/{test_room.id}", headers=auth_headers)
    
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["room_number"] == test_room.room_number

def test_get_nonexistent_room(client, auth_headers):
    """測試獲取不存在的房間"""
    response = client.get("/rooms/999", headers=auth_headers)
    
    assert response.status_code == status.HTTP_404_NOT_FOUND

# =================== tests/test_residents.py ===================
import pytest
from fastapi import status
from datetime import date

def test_get_residents(client, auth_headers, test_resident):
    """測試獲取住戶列表"""
    response = client.get("/residents/", headers=auth_headers)
    
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert len(data) > 0
    assert data[0]["name"] == test_resident.name

def test_create_resident(client, admin_headers, test_room):
    """測試創建住戶"""
    resident_data = {
        "name": "新住戶",
        "id_number": "B123456789",
        "phone": "0987654321",
        "room_id": test_room.id,
        "move_in_date": date.today().isoformat(),
        "deposit": 15000.0
    }
    
    response = client.post("/residents/", json=resident_data, headers=admin_headers)
    
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["message"] == "住戶創建成功"

def test_create_resident_duplicate_id(client, admin_headers, test_resident, test_room):
    """測試重複身份證號創建住戶"""
    resident_data = {
        "name": "另一住戶",
        "id_number": test_resident.id_number,
        "room_id": test_room.id,
        "move_in_date": date.today().isoformat()
    }
    
    response = client.post("/residents/", json=resident_data, headers=admin_headers)
    
    assert response.status_code == status.HTTP_400_BAD_REQUEST

def test_move_out_resident(client, admin_headers, test_resident):
    """測試住戶退房"""
    response = client.post(
        f"/residents/{test_resident.id}/move-out",
        json={"move_out_date": date.today().isoformat()},
        headers=admin_headers
    )
    
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["message"] == "退房成功"

def test_get_resident_by_id(client, auth_headers, test_resident):
    """測試根據ID獲取住戶"""
    response = client.get(f"/residents/{test_resident.id}", headers=auth_headers)
    
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["name"] == test_resident.name

# =================== tests/test_utilities.py ===================
import pytest
from fastapi import status
from datetime import date

def test_create_utility_rate(client, admin_headers):
    """測試創建費率"""
    rate_data = {
        "electricity_rate": 6.0,
        "monthly_water_fee": 250.0,
        "effective_date": date.today().isoformat(),
        "notes": "新費率"
    }
    
    response = client.post("/utilities/rates", json=rate_data, headers=admin_headers)
    
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["message"] == "費率創建成功"

def test_get_current_utility_rate(client, auth_headers, test_utility_rate):
    """測試獲取當前費率"""
    response = client.get("/utilities/rates/current", headers=auth_headers)
    
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["electricity_rate"] == test_utility_rate.electricity_rate

def test_create_meter_reading(client, admin_headers, test_room, test_utility_rate):
    """測試創建電表抄錄"""
    reading_data = {
        "room_id": test_room.id,
        "billing_year": 2024,
        "billing_month": 1,
        "current_electricity_reading": 1000.0
    }
    
    response = client.post("/utilities/readings", json=reading_data, headers=admin_headers)
    
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["message"] == "抄錄記錄創建成功"

def test_get_utility_bills(client, auth_headers):
    """測試獲取費用帳單"""
    response = client.get("/utilities/bills", params={"year": 2024, "month": 1}, headers=auth_headers)
    
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert isinstance(data, list)

# =================== tests/test_reports.py ===================
import pytest
from fastapi import status

def test_get_dashboard_stats(client, auth_headers):
    """測試獲取儀表板統計"""
    response = client.get("/reports/dashboard", headers=auth_headers)
    
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert "total_rooms" in data
    assert "total_residents" in data
    assert "occupancy_rate" in data

def test_get_income_summary(client, auth_headers):
    """測試獲取收入統計"""
    response = client.get("/reports/income-summary", params={"year": 2024, "month": 1}, headers=auth_headers)
    
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert "total_income" in data
    assert "paid_amount" in data

def test_get_occupancy_trend(client, auth_headers):
    """測試獲取入住率趨勢"""
    response = client.get("/reports/occupancy-trend", headers=auth_headers)
    
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert isinstance(data, list)

# =================== scripts/setup.py ===================
#!/usr/bin/env python3
"""
租房管理系統安裝腳本
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, cwd=None):
    """執行命令"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd,
            capture_output=True, 
            text=True
        )
        if result.returncode != 0:
            print(f"錯誤: {result.stderr}")
            return False
        return True
    except Exception as e:
        print(f"執行命令失敗: {e}")
        return False

def setup_backend():
    """設置後端環境"""
    print("🔧 設置後端環境...")
    
    # 檢查Python版本
    if sys.version_info < (3, 9):
        print("❌ 需要Python 3.9或更高版本")
        return False
    
    # 創建虛擬環境
    backend_dir = Path("backend")
    
    if not backend_dir.exists():
        print("❌ backend目錄不存在")
        return False
    
    # 安裝依賴
    print("📦 安裝後端依賴...")
    if not run_command("pip install -r requirements.txt", cwd=backend_dir):
        return False
    
    # 創建環境配置文件
    env_file = backend_dir / ".env"
    if not env_file.exists():
        print("📝 創建環境配置文件...")
        with open(env_file, "w", encoding="utf-8") as f:
            f.write("""DATABASE_URL=sqlite:///./rental_management.db
SECRET_KEY=your-secret-key-change-this-in-production-make-it-long-and-random
DEBUG=True
ACCESS_TOKEN_EXPIRE_MINUTES=30
DEFAULT_ELECTRICITY_RATE=5.5
DEFAULT_WATER_FEE=200.0
""")
    
    print("✅ 後端環境設置完成")
    return True

def setup_frontend():
    """設置前端環境"""
    print("🔧 設置前端環境...")
    
    frontend_dir = Path("frontend")
    
    if not frontend_dir.exists():
        print("❌ frontend目錄不存在")
        return False
    
    # 安裝依賴
    print("📦 安裝前端依賴...")
    if not run_command("pip install -r requirements.txt", cwd=frontend_dir):
        return False
    
    print("✅ 前端環境設置完成")
    return True

def initialize_database():
    """初始化資料庫"""
    print("🗄️ 初始化資料庫...")
    
    backend_dir = Path("backend")
    if not run_command("python init_db.py", cwd=backend_dir):
        return False
    
    print("✅ 資料庫初始化完成")
    return True

def run_tests():
    """運行測試"""
    print("🧪 運行測試...")
    
    # 安裝測試依賴
    if not run_command("pip install pytest pytest-cov"):
        return False
    
    # 運行測試
    if not run_command("python -m pytest tests/ -v --cov=backend/app"):
        print("⚠️ 部分測試失敗，但系統仍可運行")
    
    print("✅ 測試完成")
    return True

def main():
    """主安裝流程"""
    print("🏠 租房管理系統安裝程序")
    print("=" * 50)
    
    try:
        # 設置後端
        if not setup_backend():
            print("❌ 後端設置失敗")
            return False
        
        # 設置前端
        if not setup_frontend():
            print("❌ 前端設置失敗")
            return False
        
        # 初始化資料庫
        if not initialize_database():
            print("❌ 資料庫初始化失敗")
            return False
        
        # 運行測試
        run_tests()
        
        print("\n" + "=" * 50)
        print("🎉 安裝完成!")
        print("\n📋 啟動指令:")
        print("後端: cd backend && python run_server.py")
        print("前端: cd frontend && python run_frontend.py")
        print("\n🌐 訪問地址:")
        print("前端界面: http://localhost:8501")
        print("API文檔: http://localhost:8000/docs")
        print("預設帳號: admin / admin123")
        
        return True
        
    except KeyboardInterrupt:
        print("\n\n⏹️ 安裝已取消")
        return False
    except Exception as e:
        print(f"\n❌ 安裝失敗: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

# =================== scripts/start_system.py ===================
#!/usr/bin/env python3
"""
租房管理系統啟動腳本
"""

import subprocess
import sys
import time
import threading
import signal
from pathlib import Path

class SystemManager:
    """系統管理器"""
    
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.running = False
    
    def start_backend(self):
        """啟動後端服務"""
        print("🚀 啟動後端API服務...")
        
        backend_dir = Path("backend")
        if not backend_dir.exists():
            print("❌ backend目錄不存在")
            return False
        
        try:
            self.backend_process = subprocess.Popen(
                [sys.executable, "run_server.py"],
                cwd=backend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT
            )
            
            # 等待後端啟動
            time.sleep(3)
            
            if self.backend_process.poll() is None:
                print("✅ 後端服務已啟動 (http://localhost:8000)")
                return True
            else:
                print("❌ 後端服務啟動失敗")
                return False
                
        except Exception as e:
            print(f"❌ 啟動後端服務失敗: {e}")
            return False
    
    def start_frontend(self):
        """啟動前端服務"""
        print("🚀 啟動前端界面...")
        
        frontend_dir = Path("frontend")
        if not frontend_dir.exists():
            print("❌ frontend目錄不存在")
            return False
        
        try:
            self.frontend_process = subprocess.Popen(
                [sys.executable, "run_frontend.py"],
                cwd=frontend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT
            )
            
            # 等待前端啟動
            time.sleep(3)
            
            if self.frontend_process.poll() is None:
                print("✅ 前端服務已啟動 (http://localhost:8501)")
                return True
            else:
                print("❌ 前端服務啟動失敗")
                return False
                
        except Exception as e:
            print(f"❌ 啟動前端服務失敗: {e}")
            return False
    
    def stop_services(self):
        """停止所有服務"""
        print("\n🛑 停止系統服務...")
        
        if self.backend_process:
            self.backend_process.terminate()
            self.backend_process.wait()
            print("✅ 後端服務已停止")
        
        if self.frontend_process:
            self.frontend_process.terminate()
            self.frontend_process.wait()
            print("✅ 前端服務已停止")
        
        self.running = False
    
    def signal_handler(self, signum, frame):
        """信號處理器"""
        self.stop_services()
        sys.exit(0)
    
    def start_system(self):
        """啟動整個系統"""
        print("🏠 租房管理系統啟動中...")
        print("=" * 50)
        
        # 註冊信號處理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        try:
            # 啟動後端
            if not self.start_backend():
                return False
            
            # 啟動前端
            if not self.start_frontend():
                self.stop_services()
                return False
            
            self.running = True
            
            print("\n" + "=" * 50)
            print("🎉 系統啟動成功!")
            print("🌐 前端界面: http://localhost:8501")
            print("📖 API文檔: http://localhost:8000/docs")
            print("🔑 預設帳號: admin / admin123")
            print("按 Ctrl+C 停止系統")
            print("=" * 50)
            
            # 保持運行
            while self.running:
                time.sleep(1)
                
                # 檢查服務狀態
                if self.backend_process and self.backend_process.poll() is not None:
                    print("❌ 後端服務意外停止")
                    self.stop_services()
                    return False
                
                if self.frontend_process and self.frontend_process.poll() is not None:
                    print("❌ 前端服務意外停止")
                    self.stop_services()
                    return False
            
            return True
            
        except Exception as e:
            print(f"❌ 系統啟動失敗: {e}")
            self.stop_services()
            return False

def main():
    """主程式"""
    manager = SystemManager()
    
    try:
        success = manager.start_system()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n\n⏹️ 系統已停止")
        return 0
    except Exception as e:
        print(f"\n❌ 系統錯誤: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())

# =================== scripts/backup.py ===================
#!/usr/bin/env python3
"""
租房管理系統備份腳本
"""

import shutil
import sqlite3
import os
from datetime import datetime
from pathlib import Path

class BackupManager:
    """備份管理器"""
    
    def __init__(self, db_path="backend/rental_management.db", backup_dir="backups"):
        self.db_path = Path(db_path)
        self.backup_dir = Path(backup_dir)
        self.backup_dir.mkdir(exist_ok=True)
    
    def create_backup(self):
        """創建備份"""
        if not self.db_path.exists():
            print("❌ 資料庫文件不存在")
            return False
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"rental_db_backup_{timestamp}.db"
        backup_path = self.backup_dir / backup_filename
        
        try:
            shutil.copy2(self.db_path, backup_path)
            print(f"✅ 備份創建成功: {backup_path}")
            return True
        except Exception as e:
            print(f"❌ 備份創建失敗: {e}")
            return False
    
    def list_backups(self):
        """列出所有備份"""
        backups = []
        
        for file in self.backup_dir.glob("rental_db_backup_*.db"):
            stat = file.stat()
            backups.append({
                "filename": file.name,
                "path": file,
                "size": stat.st_size,
                "created": datetime.fromtimestamp(stat.st_ctime)
            })
        
        return sorted(backups, key=lambda x: x["created"], reverse=True)
    
    def restore_backup(self, backup_filename):
        """恢復備份"""
        backup_path = self.backup_dir / backup_filename
        
        if not backup_path.exists():
            print(f"❌ 備份文件不存在: {backup_filename}")
            return False
        
        try:
            # 創建當前資料庫的備份
            self.create_backup()
            
            # 恢復備份
            shutil.copy2(backup_path, self.db_path)
            print(f"✅ 備份恢復成功: {backup_filename}")
            return True
        except Exception as e:
            print(f"❌ 備份恢復失敗: {e}")
            return False
    
    def clean_old_backups(self, keep_days=30):
        """清理舊備份"""
        cutoff_time = datetime.now().timestamp() - (keep_days * 24 * 60 * 60)
        cleaned_count = 0
        
        for file in self.backup_dir.glob("rental_db_backup_*.db"):
            if file.stat().st_ctime < cutoff_time:
                try:
                    file.unlink()
                    cleaned_count += 1
                except Exception as e:
                    print(f"⚠️ 無法刪除舊備份 {file.name}: {e}")
        
        if cleaned_count > 0:
            print(f"✅ 已清理 {cleaned_count} 個舊備份文件")
        else:
            print("ℹ️ 沒有需要清理的舊備份")

def main():
    """主程式"""
    import argparse
    
    parser = argparse.ArgumentParser(description="租房管理系統備份工具")
    parser.add_argument("action", choices=["create", "list", "restore", "clean"])
    parser.add_argument("--backup", help="要恢復的備份文件名")
    parser.add_argument("--keep-days", type=int, default=30, help="保留備份的天數")
    
    args = parser.parse_args()
    
    manager = BackupManager()
    
    if args.action == "create":
        manager.create_backup()
    
    elif args.action == "list":
        backups = manager.list_backups()
        if backups:
            print("📋 備份列表:")
            for backup in backups:
                size_mb = backup["size"] / (1024 * 1024)
                print(f"  {backup['filename']} ({size_mb:.1f}MB) - {backup['created']}")
        else:
            print("ℹ️ 沒有備份文件")
    
    elif args.action == "restore":
        if not args.backup:
            print("❌ 請指定要恢復的備份文件名")
            return 1
        manager.restore_backup(args.backup)
    
    elif args.action == "clean":
        manager.clean_old_backups(args.keep_days)
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())

# =================== scripts/health_check.py ===================
#!/usr/bin/env python3
"""
租房管理系統健康檢查腳本
"""

import requests
import sqlite3
import sys
from pathlib import Path
from datetime import datetime

class HealthChecker:
    """健康檢查器"""
    
    def __init__(self):
        self.api_url = "http://localhost:8000"
        self.frontend_url = "http://localhost:8501"
        self.db_path = Path("backend/rental_management.db")
    
    def check_api_health(self):
        """檢查API健康狀態"""
        try:
            response = requests.get(f"{self.api_url}/health", timeout=5)
            if response.status_code == 200:
                print("✅ API服務正常")
                return True
            else:
                print(f"❌ API服務異常: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ API服務無法連接: {e}")
            return False
    
    def check_frontend_health(self):
        """檢查前端健康狀態"""
        try:
            response = requests.get(self.frontend_url, timeout=5)
            if response.status_code == 200:
                print("✅ 前端服務正常")
                return True
            else:
                print(f"❌ 前端服務異常: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ 前端服務無法連接: {e}")
            return False
    
    def check_database_health(self):
        """檢查資料庫健康狀態"""
        try:
            if not self.db_path.exists():
                print("❌ 資料庫文件不存在")
                return False
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 檢查表格存在性
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            expected_tables = ["users", "rooms", "residents", "utility_rates", "utility_records"]
            existing_tables = [table[0] for table in tables]
            
            for table in expected_tables:
                if table not in existing_tables:
                    print(f"❌ 缺少資料表: {table}")
                    return False
            
            # 檢查資料庫完整性
            cursor.execute("PRAGMA integrity_check")
            result = cursor.fetchone()
            
            if result[0] == "ok":
                print("✅ 資料庫完整性正常")
                return True
            else:
                print(f"❌ 資料庫完整性異常: {result[0]}")
                return False
                
        except Exception as e:
            print(f"❌ 資料庫檢查失敗: {e}")
            return False
        finally:
            if 'conn' in locals():
                conn.close()
    
    def check_system_resources(self):
        """檢查系統資源"""
        try:
            import psutil
            
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > 80:
                print(f"⚠️ CPU使用率偏高: {cpu_percent}%")
            else:
                print(f"✅ CPU使用率正常: {cpu_percent}%")
            
            # 記憶體使用率
            memory = psutil.virtual_memory()
            if memory.percent > 80:
                print(f"⚠️ 記憶體使用率偏高: {memory.percent}%")
            else:
                print(f"✅ 記憶體使用率正常: {memory.percent}%")
            
            # 磁盤使用率
            disk = psutil.disk_usage('.')
            disk_percent = (disk.used / disk.total) * 100
            if disk_percent > 80:
                print(f"⚠️ 磁盤使用率偏高: {disk_percent:.1f}%")
            else:
                print(f"✅ 磁盤使用率正常: {disk_percent:.1f}%")
            
            return True
            
        except ImportError:
            print("ℹ️ 系統資源檢查需要安裝psutil: pip install psutil")
            return True
        except Exception as e:
            print(f"⚠️ 系統資源檢查失敗: {e}")
            return True
    
    def run_full_check(self):
        """運行完整健康檢查"""
        print("🏥 系統健康檢查")
        print("=" * 40)
        print(f"檢查時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 40)
        
        results = {
            "API服務": self.check_api_health(),
            "前端服務": self.check_frontend_health(),
            "資料庫": self.check_database_health(),
            "系統資源": self.check_system_resources()
        }
        
        print("\n" + "=" * 40)
        print("📊 檢查結果:")
        
        all_healthy = True
        for service, status in results.items():
            status_icon = "✅" if status else "❌"
            print(f"{status_icon} {service}: {'正常' if status else '異常'}")
            if not status:
                all_healthy = False
        
        print("=" * 40)
        if all_healthy:
            print("🎉 系統整體狀態: 正常")
            return True
        else:
            print("⚠️ 系統整體狀態: 異常")
            return False

def main():
    """主程式"""
    checker = HealthChecker()
    
    try:
        healthy = checker.run_full_check()
        return 0 if healthy else 1
    except KeyboardInterrupt:
        print("\n\n⏹️ 健康檢查已取消")
        return 1
    except Exception as e:
        print(f"\n❌ 健康檢查失敗: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())

# =================== README.md ===================
# 🏠 租房管理系統

現代化的租房管理解決方案，基於 FastAPI 和 Streamlit 構建。

## 🚀 快速開始

### 自動安裝（推薦）
```bash
python scripts/setup.py
```

### 手動安裝
```bash
# 後端設置
cd backend
pip install -r requirements.txt
cp .env.example .env
python init_db.py

# 前端設置
cd ../frontend
pip install -r requirements.txt
```

## 🎯 系統啟動

### 一鍵啟動
```bash
python scripts/start_system.py
```

### 分別啟動
```bash
# 後端API服務
cd backend && python run_server.py

# 前端界面
cd frontend && python run_frontend.py
```

## 🌐 訪問地址

- **前端界面**: http://localhost:8501
- **API文檔**: http://localhost:8000/docs
- **預設帳號**: admin / admin123

## 🛠️ 系統工具

### 備份管理
```bash
# 創建備份
python scripts/backup.py create

# 列出備份
python scripts/backup.py list

# 恢復備份
python scripts/backup.py restore --backup filename.db

# 清理舊備份
python scripts/backup.py clean --keep-days 30
```

### 健康檢查
```bash
python scripts/health_check.py
```

### 運行測試
```bash
cd backend && python -m pytest tests/ -v
```

## 📋 功能特性

### 核心功能
- 🏠 房間管理（新增、編輯、狀態追蹤）
- 👥 住戶管理（入住、退房、資料維護）
- 💰 費用管理（電費、水費、帳單生成）
- 📊 統計報表（收入分析、入住率統計）
- 🔐 用戶認證（JWT認證、角色權限）

### 技術特性
- 🎨 響應式設計
- 🔄 實時數據更新
- 🛡️ 安全認證機制
- 📱 移動端友好
- 🗄️ 輕量級資料庫

## 🏗️ 技術架構

### 後端技術
- **框架**: FastAPI 0.104.1
- **資料庫**: SQLite + SQLAlchemy
- **認證**: JWT + bcrypt
- **API設計**: RESTful API

### 前端技術
- **框架**: Streamlit 1.28.1
- **圖表**: Plotly
- **數據處理**: Pandas
- **HTTP客戶端**: Requests

### 部署方案
- **環境**: Python 3.9+
- **依賴管理**: requirements.txt
- **配置管理**: .env環境變數
- **進程管理**: 直接Python進程

## 📁 項目結構

```
rental_management_system/
├── backend/                # 後端API服務
│   ├── app/               # 應用程式核心
│   │   ├── models.py      # 資料模型
│   │   ├── services.py    # 業務邏輯
│   │   ├── auth.py        # 認證系統
│   │   └── routers/       # API路由
│   └── requirements.txt   # 後端依賴
├── frontend/              # 前端界面
│   ├── pages/            # 頁面組件
│   ├── api_client.py     # API客戶端
│   └── requirements.txt  # 前端依賴
├── scripts/              # 工具腳本
│   ├── setup.py         # 安裝腳本
│   ├── start_system.py  # 啟動腳本
│   ├── backup.py        # 備份工具
│   └── health_check.py  # 健康檢查
└── tests/               # 測試文件
    ├── test_auth.py     # 認證測試
    ├── test_rooms.py    # 房間測試
    └── test_residents.py # 住戶測試
```

## 🔧 開發指南

### 新增功能
1. 在 `backend/app/models.py` 中定義資料模型
2. 在 `backend/app/services.py` 中實現業務邏輯
3. 在 `backend/app/routers/` 中添加API路由
4. 在 `frontend/pages/` 中創建前端頁面
5. 在 `tests/` 中添加相應測試

### 資料庫遷移
```bash
# 清除現有資料庫
rm backend/rental_management.db

# 重新初始化
cd backend && python init_db.py
```

### 配置修改
編輯 `backend/.env` 文件來修改系統配置：
```env
DATABASE_URL=sqlite:///./rental_management.db
SECRET_KEY=your-secret-key-here
DEBUG=True
ACCESS_TOKEN_EXPIRE_MINUTES=30
```

## 🐛 故障排除

### 常見問題

1. **端口被占用**
   ```bash
   # 查看端口使用情況
   netstat -an | grep :8000
   netstat -an | grep :8501
   ```

2. **資料庫鎖定**
   ```bash
   # 停止所有服務後重新啟動
   python scripts/start_system.py
   ```

3. **依賴問題**
   ```bash
   # 重新安裝依賴
   pip install -r requirements.txt --upgrade
   ```

### 日誌查看
- 後端日誌：查看控制台輸出
- 前端日誌：查看 Streamlit 控制台
- 資料庫日誌：SQLite 無單獨日誌文件

## 📞 技術支援

如需技術支援，請：
1. 運行健康檢查：`python scripts/health_check.py`
2. 查看系統日誌輸出
3. 檢查環境配置文件

## 📄 授權說明

本項目為開源軟體，請遵循相關開源協議使用。

---

**快速開始命令**：
```bash
git clone <repository-url>
cd rental_management_system
python scripts/setup.py
python scripts/start_system.py
```

訪問 http://localhost:8501 開始使用！