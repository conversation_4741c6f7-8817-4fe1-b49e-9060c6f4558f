#!/usr/bin/env python3
"""
系統一鍵啟動腳本
同時啟動後端和前端服務
"""

import subprocess
import sys
import os
import time
import threading
import signal

class SystemRunner:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.running = True
    
    def start_backend(self):
        """啟動後端服務"""
        print("🚀 啟動後端服務...")
        backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
        
        cmd = [
            ".venv\\Scripts\\python", "-m", "uvicorn",
            "app.main:app",
            "--host", "0.0.0.0",
            "--port", "8080",
            "--reload"
        ]
        
        try:
            self.backend_process = subprocess.Popen(
                cmd, 
                cwd=backend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            print("✅ 後端服務已啟動 (PID: {})".format(self.backend_process.pid))
        except Exception as e:
            print(f"❌ 啟動後端服務失敗: {e}")
            return False
        
        return True
    
    def start_frontend(self):
        """啟動前端服務"""
        print("🚀 啟動前端服務...")
        frontend_dir = os.path.join(os.path.dirname(__file__), 'frontend')
        
        cmd = [
            ".venv\\Scripts\\python", "-m", "streamlit", "run", "app.py",
            "--server.port", "8501",
            "--server.headless", "true",
            "--browser.gatherUsageStats", "false"
        ]
        
        try:
            self.frontend_process = subprocess.Popen(
                cmd,
                cwd=frontend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            print("✅ 前端服務已啟動 (PID: {})".format(self.frontend_process.pid))
        except Exception as e:
            print(f"❌ 啟動前端服務失敗: {e}")
            return False
        
        return True
    
    def wait_for_backend(self):
        """等待後端服務就緒"""
        import requests
        
        print("⏳ 等待後端服務就緒...")
        max_attempts = 30
        
        for attempt in range(max_attempts):
            try:
                response = requests.get("http://localhost:8080/health", timeout=2)
                if response.status_code == 200:
                    print("✅ 後端服務就緒")
                    return True
            except:
                pass
            
            time.sleep(1)
            print(f"   嘗試 {attempt + 1}/{max_attempts}...")
        
        print("❌ 後端服務啟動超時")
        return False
    
    def stop_services(self):
        """停止所有服務"""
        print("\n🛑 正在停止服務...")
        
        if self.frontend_process:
            try:
                self.frontend_process.terminate()
                self.frontend_process.wait(timeout=5)
                print("✅ 前端服務已停止")
            except:
                self.frontend_process.kill()
                print("🔪 強制停止前端服務")
        
        if self.backend_process:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=5)
                print("✅ 後端服務已停止")
            except:
                self.backend_process.kill()
                print("🔪 強制停止後端服務")
        
        self.running = False
    
    def signal_handler(self, signum, frame):
        """處理中斷信號"""
        self.stop_services()
        sys.exit(0)
    
    def run(self):
        """運行系統"""
        # 註冊信號處理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        print("🏠 租房管理系統 - 一鍵啟動")
        print("=" * 50)
        
        try:
            # 啟動後端
            if not self.start_backend():
                return False
            
            # 等待後端就緒
            if not self.wait_for_backend():
                self.stop_services()
                return False
            
            # 啟動前端
            if not self.start_frontend():
                self.stop_services()
                return False
            
            # 等待前端就緒
            time.sleep(3)
            
            print("\n" + "=" * 50)
            print("🎉 系統啟動完成！")
            print("📱 前端地址: http://localhost:8501")
            print("🔧 後端API: http://localhost:8080")
            print("📚 API文檔: http://localhost:8080/docs")
            print("👤 預設帳號: admin / admin123")
            print("\n按 Ctrl+C 停止系統")
            print("=" * 50)
            
            # 保持運行
            while self.running:
                # 檢查進程狀態
                if self.backend_process and self.backend_process.poll() is not None:
                    print("❌ 後端服務意外停止")
                    break
                
                if self.frontend_process and self.frontend_process.poll() is not None:
                    print("❌ 前端服務意外停止")
                    break
                
                time.sleep(1)
        
        except KeyboardInterrupt:
            pass
        finally:
            self.stop_services()
        
        return True

def check_dependencies():
    """檢查系統依賴"""
    print("🔍 檢查系統依賴...")
    
    missing_deps = []
    
    # 檢查後端依賴
    try:
        import fastapi, uvicorn, sqlalchemy, pydantic, passlib, jose
    except ImportError as e:
        missing_deps.append(f"後端依賴: {e}")
    
    # 檢查前端依賴
    try:
        import streamlit, pandas, plotly, requests
    except ImportError as e:
        missing_deps.append(f"前端依賴: {e}")
    
    if missing_deps:
        print("❌ 缺少依賴:")
        for dep in missing_deps:
            print(f"   {dep}")
        print("\n請執行以下命令安裝依賴:")
        print("   python -m venv .venv")
        print("   .venv\\Scripts\\activate")
        print("   pip install -r backend/requirements.txt")
        print("   pip install -r frontend/requirements.txt")
        return False
    
    print("✅ 依賴檢查通過")
    return True

def main():
    """主程式"""
    # 檢查依賴
    if not check_dependencies():
        sys.exit(1)
    
    # 檢查是否已初始化
    db_file = "rental_management.db"
    if not os.path.exists(db_file):
        print("⚠️  系統尚未初始化，請先執行:")
        print("   python init_system.py")
        sys.exit(1)
    
    # 啟動系統
    runner = SystemRunner()
    success = runner.run()
    
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
