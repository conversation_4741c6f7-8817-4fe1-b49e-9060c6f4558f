import streamlit as st
from api_client import api_client
from utils import init_session_state, login_user, show_success_message, show_error_message, show_info_message

def show_login_page():
    """顯示登入頁面"""
    st.title("🏠 租房管理系統")
    st.markdown("---")
    
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.subheader("用戶登入")

        # 檢查是否有密碼變更成功的提示
        if st.session_state.get('password_changed', False):
            show_success_message("密碼變更成功！請使用新密碼登入")
            # 清除標記
            del st.session_state.password_changed

        with st.form("login_form"):
            username = st.text_input("用戶名", placeholder="請輸入用戶名")
            password = st.text_input("密碼", type="password", placeholder="請輸入密碼")
            
            submitted = st.form_submit_button("登入", use_container_width=True)
            
            if submitted:
                if not username or not password:
                    show_error_message("請輸入用戶名和密碼")
                else:
                    with st.spinner("登入中..."):
                        login_result = api_client.login(username, password)

                        if login_result:
                            login_user(
                                login_result['token'],
                                login_result['user_info']
                            )
                            show_success_message("登入成功！")
                            st.rerun()
        
        st.markdown("---")

        # 登入說明
        show_info_message("請使用您的帳號登入系統，如需協助請聯絡系統管理員")

        # 系統資訊
        with st.expander("系統資訊"):
            st.markdown("""
            ### 功能特色
            - 🏠 房間管理
            - 👥 住戶管理
            - 💰 費用管理
            - 📊 統計報表
            
            ### 技術架構
            - 後端：FastAPI + SQLAlchemy
            - 前端：Streamlit
            - 資料庫：SQLite
            - 認證：JWT
            """)
