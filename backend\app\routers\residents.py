from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel, validator
from typing import List, Optional, Union
from datetime import datetime, date
from ..database import get_db
from ..auth import get_current_user
from ..services import ResidentService, RoomService
from ..models import User, Resident

router = APIRouter(prefix="/residents", tags=["住戶管理"])

class ResidentCreate(BaseModel):
    name: str
    phone: Optional[str] = None
    id_number: str
    emergency_contact: Optional[str] = None
    emergency_phone: Optional[str] = None
    room_id: int
    move_in_date: Union[date, str]
    lease_end_date: Optional[Union[date, str]] = None
    deposit: Optional[float] = 0.0

    @validator('move_in_date', pre=True)
    def parse_move_in_date(cls, v):
        """解析入住日期格式，支援多種輸入格式"""
        if isinstance(v, str):
            try:
                # 嘗試解析 YYYY-MM-DD 格式
                return datetime.strptime(v, '%Y-%m-%d').date()
            except ValueError:
                try:
                    # 嘗試解析 ISO 格式
                    return datetime.fromisoformat(v.replace('Z', '+00:00')).date()
                except ValueError:
                    raise ValueError('入住日期格式錯誤，請使用 YYYY-MM-DD 格式')
        elif isinstance(v, datetime):
            return v.date()
        elif isinstance(v, date):
            return v
        else:
            raise ValueError('入住日期格式錯誤，請使用 YYYY-MM-DD 格式')

    @validator('lease_end_date', pre=True)
    def parse_lease_end_date(cls, v):
        """解析租約到期日格式，支援多種輸入格式"""
        if v is None:
            return None
        if isinstance(v, str):
            try:
                # 嘗試解析 YYYY-MM-DD 格式
                return datetime.strptime(v, '%Y-%m-%d').date()
            except ValueError:
                try:
                    # 嘗試解析 ISO 格式
                    return datetime.fromisoformat(v.replace('Z', '+00:00')).date()
                except ValueError:
                    raise ValueError('租約到期日格式錯誤，請使用 YYYY-MM-DD 格式')
        elif isinstance(v, datetime):
            return v.date()
        elif isinstance(v, date):
            return v
        else:
            raise ValueError('租約到期日格式錯誤，請使用 YYYY-MM-DD 格式')

    @validator('name')
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('住戶姓名不能為空')
        return v.strip()

    @validator('id_number')
    def validate_id_number(cls, v):
        if not v or not v.strip():
            raise ValueError('身分證號不能為空')
        return v.strip()

    @validator('room_id')
    def validate_room_id(cls, v):
        if v <= 0:
            raise ValueError('房間ID必須大於0')
        return v

class ResidentUpdate(BaseModel):
    name: Optional[str] = None
    phone: Optional[str] = None
    emergency_contact: Optional[str] = None
    emergency_phone: Optional[str] = None
    lease_end_date: Optional[Union[date, str]] = None
    deposit: Optional[float] = None

    @validator('name')
    def validate_name(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('住戶姓名不能為空')
        return v.strip() if v else v

    @validator('phone')
    def validate_phone(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('電話號碼不能為空')
        return v.strip() if v else v

    @validator('deposit')
    def validate_deposit(cls, v):
        if v is not None and v < 0:
            raise ValueError('押金不能為負數')
        return v

    @validator('lease_end_date', pre=True)
    def parse_lease_end_date(cls, v):
        """解析租約到期日期格式"""
        if v is None or v == '':
            return None
        if isinstance(v, str):
            try:
                # 嘗試解析 YYYY-MM-DD 格式
                return datetime.strptime(v, '%Y-%m-%d').date()
            except ValueError:
                try:
                    # 嘗試解析 YYYY/MM/DD 格式
                    return datetime.strptime(v, '%Y/%m/%d').date()
                except ValueError:
                    raise ValueError('日期格式錯誤，請使用 YYYY-MM-DD 或 YYYY/MM/DD 格式')
        return v

class MoveOutRequest(BaseModel):
    move_out_date: datetime

class ResidentResponse(BaseModel):
    id: int
    name: str
    phone: Optional[str]
    id_number: str
    emergency_contact: Optional[str]
    emergency_phone: Optional[str]
    room_id: int
    move_in_date: str
    move_out_date: Optional[str]
    lease_end_date: Optional[str]
    deposit: float
    is_active: bool
    room: Optional[dict]

@router.get("/", response_model=List[ResidentResponse])
async def get_residents(
    active_only: bool = True,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """獲取住戶列表"""
    residents = ResidentService.get_all_residents(db, active_only)
    return [resident.to_dict() for resident in residents]

@router.post("/", response_model=ResidentResponse)
async def create_resident(
    resident: ResidentCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """創建新住戶"""
    # 檢查房間是否存在且可用
    room = RoomService.get_room_by_id(db, resident.room_id)
    if not room:
        raise HTTPException(status_code=404, detail="房間不存在")
    
    if room.current_occupants >= room.max_occupants:
        raise HTTPException(status_code=400, detail="房間已滿")
    
    try:
        resident_data = resident.dict()

        # 確保 move_in_date 轉換為 datetime 格式
        if isinstance(resident_data['move_in_date'], date):
            resident_data['move_in_date'] = datetime.combine(resident_data['move_in_date'], datetime.min.time())

        new_resident = ResidentService.create_resident(db, resident_data)
        return new_resident.to_dict()
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"創建住戶失敗: {str(e)}"
        )

@router.get("/{resident_id}", response_model=ResidentResponse)
async def get_resident(
    resident_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """獲取住戶詳情"""
    resident = ResidentService.get_resident_by_id(db, resident_id)
    if not resident:
        raise HTTPException(status_code=404, detail="住戶不存在")
    return resident.to_dict()

@router.put("/{resident_id}", response_model=ResidentResponse)
async def update_resident(
    resident_id: int,
    resident_update: ResidentUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新住戶資訊"""
    try:
        resident = ResidentService.get_resident_by_id(db, resident_id)
        if not resident:
            raise HTTPException(status_code=404, detail="住戶不存在")

        # 驗證更新資料
        update_data = resident_update.dict(exclude_unset=True)

        # 如果沒有要更新的資料
        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="沒有提供要更新的資料"
            )

        # 使用ResidentService更新住戶
        updated_resident = ResidentService.update_resident(db, resident_id, update_data)
        if not updated_resident:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="更新住戶資料失敗"
            )

        return updated_resident.to_dict()

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新住戶失敗: {str(e)}"
        )

@router.post("/{resident_id}/move-out", response_model=ResidentResponse)
async def move_out_resident(
    resident_id: int,
    move_out_request: MoveOutRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """住戶退房"""
    resident = ResidentService.move_out_resident(db, resident_id, move_out_request.move_out_date)
    if not resident:
        raise HTTPException(status_code=404, detail="住戶不存在")
    return resident.to_dict()

class LeaseExtensionRequest(BaseModel):
    new_lease_end_date: Union[date, str]
    extension_reason: Optional[str] = None

    @validator('new_lease_end_date', pre=True)
    def parse_new_lease_end_date(cls, v):
        """解析新租約到期日期格式"""
        if isinstance(v, str):
            try:
                return datetime.strptime(v, '%Y-%m-%d').date()
            except ValueError:
                try:
                    return datetime.strptime(v, '%Y/%m/%d').date()
                except ValueError:
                    raise ValueError('日期格式錯誤，請使用 YYYY-MM-DD 或 YYYY/MM/DD 格式')
        return v

@router.post("/{resident_id}/extend-lease")
async def extend_resident_lease(
    resident_id: int,
    extension_request: LeaseExtensionRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """住戶租期展期"""
    try:
        # 獲取住戶資訊
        resident = db.query(Resident).filter(Resident.id == resident_id).first()
        if not resident:
            raise HTTPException(status_code=404, detail="住戶不存在")

        # 檢查住戶是否為活躍狀態
        if not resident.is_active:
            raise HTTPException(status_code=400, detail="住戶已退房，無法進行租期展期")

        # 檢查新租約到期日是否晚於原到期日
        if resident.lease_end_date:
            # 確保比較的是 date 對象
            original_date = resident.lease_end_date
            if isinstance(original_date, datetime):
                original_date = original_date.date()

            if extension_request.new_lease_end_date <= original_date:
                raise HTTPException(
                    status_code=400,
                    detail=f"新租約到期日必須晚於原到期日 ({original_date})"
                )

        # 檢查新租約到期日是否晚於今天（統一檢查）
        if extension_request.new_lease_end_date <= date.today():
            raise HTTPException(
                status_code=400,
                detail="新租約到期日必須晚於今天"
            )

        # 記錄原始資訊
        original_lease_end_date = resident.lease_end_date
        if isinstance(original_lease_end_date, datetime):
            original_lease_end_date = original_lease_end_date.date()

        # 更新租約到期日
        resident.lease_end_date = extension_request.new_lease_end_date

        # 記錄展期操作（可以考慮添加到歷史記錄表）
        # 這裡先簡單更新，後續可以擴展為完整的歷史記錄系統

        db.commit()

        # 返回更新後的住戶資訊和展期詳情
        return {
            "message": "租期展期成功",
            "resident": resident.to_dict(),
            "extension_details": {
                "original_lease_end_date": original_lease_end_date.isoformat() if original_lease_end_date else None,
                "new_lease_end_date": extension_request.new_lease_end_date.isoformat(),
                "extension_reason": extension_request.extension_reason,
                "extended_by_days": (extension_request.new_lease_end_date - original_lease_end_date).days if original_lease_end_date else None,
                "operation_date": datetime.now().isoformat()
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"租期展期失敗: {str(e)}")

@router.get("/{resident_id}/lease-info")
async def get_resident_lease_info(
    resident_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """獲取住戶租約資訊"""
    resident = db.query(Resident).filter(Resident.id == resident_id).first()
    if not resident:
        raise HTTPException(status_code=404, detail="住戶不存在")

    # 計算租約相關資訊
    today = date.today()
    lease_info = {
        "resident_id": resident.id,
        "name": resident.name,
        "move_in_date": resident.move_in_date.isoformat() if resident.move_in_date else None,
        "lease_end_date": resident.lease_end_date.isoformat() if resident.lease_end_date else None,
        "is_active": resident.is_active,
        "days_until_expiry": None,
        "lease_status": "unknown"
    }

    if resident.lease_end_date:
        days_until_expiry = (resident.lease_end_date - today).days
        lease_info["days_until_expiry"] = days_until_expiry

        if days_until_expiry < 0:
            lease_info["lease_status"] = "expired"
        elif days_until_expiry <= 30:
            lease_info["lease_status"] = "expiring_soon"
        else:
            lease_info["lease_status"] = "active"
    else:
        lease_info["lease_status"] = "no_end_date"

    return lease_info
