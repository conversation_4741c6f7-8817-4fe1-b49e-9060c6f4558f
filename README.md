# 🏠 租房管理系統

一個簡潔易維護的租房管理系統，使用 Python 技術棧開發，不依賴 Docker，支援房間管理、住戶管理、費用管理等核心功能。

## ✨ 系統特色

- **簡潔易維護**: 使用 Python 統一技術棧，代碼結構清晰
- **無 Docker 依賴**: 直接 Python 進程啟動，部署簡單
- **動態租金**: 支援同房間 1 人/2 人不同租金設定
- **費用管理**: 電費按度數計算，水費按月固定
- **完整功能**: 住戶管理、房間管理、費用管理、報表統計

## 🏗️ 技術架構

### 後端
- **FastAPI**: 現代化 Web API 框架
- **SQLAlchemy**: ORM 數據庫操作
- **SQLite**: 輕量級檔案型資料庫
- **JWT**: 無狀態身份認證
- **bcrypt**: 密碼加密

### 前端
- **Streamlit**: 快速 Web 應用開發
- **Plotly**: 互動式圖表
- **Pandas**: 數據處理和展示

## 📋 核心功能

### 🏠 房間管理
- 房間基本資訊管理（房號、樓層、坪數）
- 動態租金設定（單人/雙人不同價格）
- 房間狀態追蹤（可用/已住/部分住/維護中）
- 住戶容量控制（最多 2 人）

### 👥 住戶管理
- 住戶基本資料（姓名、身份證、聯絡方式）
- 入住/退房流程管理
- 緊急聯絡人資訊
- 押金管理

### 💰 費用管理
- 電費費率設定（每度價格）
- 水費設定（每月固定費用）
- 電表抄錄和自動計費
- 帳單生成和付款狀態管理

### 📊 報表統計
- 營運概況儀表板
- 收入分析和趨勢
- 住戶分析和統計
- 數據匯出功能

### 🔐 帳號管理
- 用戶認證和授權
- 角色權限管理
- 安全的密碼處理

## 🚀 快速開始

### 環境需求
- Python 3.8+
- uv 套件管理器 (推薦) 或 pip

### 安裝步驟

1. **安裝uv套件管理器**
   ```bash
   # Windows
   powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

   # macOS/Linux
   curl -LsSf https://astral.sh/uv/install.sh | sh
   ```

2. **克隆專案**
   ```bash
   git clone <repository-url>
   cd 租屋管理系統
   ```

3. **安裝依賴**
   ```bash
   # 使用uv安裝所有依賴
   uv sync

   # 或使用傳統pip方式
   # pip install -r backend/requirements.txt
   # pip install -r frontend/requirements.txt
   ```

4. **初始化系統**
   ```bash
   .venv\Scripts\python init_system.py
   ```
   
   這將會：
   - 創建資料庫表格
   - 創建預設管理員帳號 (admin/admin123)
   - 設定預設費率
   - 可選擇創建範例數據

5. **啟動系統**

   **方式一：一鍵啟動（推薦）**
   ```bash
   .venv\Scripts\python run_system.py
   ```

   **方式二：分別啟動**
   ```bash
   # 終端 1：啟動後端
   .venv\Scripts\python run_backend.py

   # 終端 2：啟動前端
   .venv\Scripts\python run_frontend.py
   ```

6. **訪問系統**
   - 前端界面: http://localhost:8501
   - 後端API: http://localhost:8080
   - API文檔: http://localhost:8080/docs

### 預設帳號
- **用戶名**: admin
- **密碼**: admin123

## 📁 專案結構

```
租屋管理系統/
├── backend/                 # 後端服務
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py         # FastAPI 主應用
│   │   ├── config.py       # 配置管理
│   │   ├── database.py     # 資料庫連接
│   │   ├── models.py       # 數據模型
│   │   ├── auth.py         # 認證系統
│   │   ├── services.py     # 業務邏輯
│   │   └── routers/        # API 路由
│   │       ├── auth.py
│   │       ├── rooms.py
│   │       ├── residents.py
│   │       ├── utilities.py
│   │       └── reports.py
│   ├── requirements.txt    # 後端依賴
│   └── .env.example       # 環境變數範例
├── frontend/               # 前端服務
│   ├── app.py             # Streamlit 主應用
│   ├── config.py          # 前端配置
│   ├── api_client.py      # API 客戶端
│   ├── utils.py           # 工具函數
│   ├── pages/             # 頁面模組
│   │   ├── login.py
│   │   ├── dashboard.py
│   │   ├── rooms.py
│   │   ├── residents.py
│   │   ├── utilities.py
│   │   └── reports.py
│   └── requirements.txt   # 前端依賴
├── init_system.py         # 系統初始化腳本
├── run_backend.py         # 後端啟動腳本
├── run_frontend.py        # 前端啟動腳本
├── run_system.py          # 一鍵啟動腳本
└── README.md              # 專案說明
```

## ⚙️ 配置說明

### 環境變數
複製 `backend/.env.example` 為 `backend/.env` 並修改配置：

```env
DATABASE_URL=sqlite:///./rental_management.db
SECRET_KEY=your-secret-key-change-this-in-production
DEBUG=True
ACCESS_TOKEN_EXPIRE_MINUTES=30
DEFAULT_ELECTRICITY_RATE=5.5
DEFAULT_WATER_FEE=200.0
```

### 重要配置項
- `SECRET_KEY`: JWT 簽名密鑰（生產環境請更換）
- `DEFAULT_ELECTRICITY_RATE`: 預設電費費率（元/度）
- `DEFAULT_WATER_FEE`: 預設月度水費（元/月）

## 🔧 開發指南

### 後端開發
- API 路由位於 `backend/app/routers/`
- 業務邏輯位於 `backend/app/services.py`
- 數據模型位於 `backend/app/models.py`

### 前端開發
- 頁面組件位於 `frontend/pages/`
- API 調用位於 `frontend/api_client.py`
- 工具函數位於 `frontend/utils.py`

### 添加新功能
1. 後端：在相應的 router 中添加 API 端點
2. 前端：在相應的 page 中添加界面邏輯
3. 更新 API 客戶端調用

## 🐛 故障排除

### 常見問題

1. **無法啟動服務**
   - 檢查 Python 版本 (需要 3.8+)
   - 確認已安裝所有依賴
   - 檢查端口 8000 和 8501 是否被佔用

2. **資料庫錯誤**
   - 重新執行 `python init_system.py`
   - 檢查資料庫檔案權限

3. **API 連接失敗**
   - 確認後端服務正在運行
   - 檢查防火牆設定

### 日誌查看
- 後端日誌：在啟動後端的終端中查看
- 前端日誌：在瀏覽器開發者工具中查看

## 📝 使用說明

### 基本操作流程

1. **系統初始化**
   - 使用管理員帳號登入
   - 設定電費和水費費率

2. **房間管理**
   - 新增房間資訊
   - 設定單人/雙人租金

3. **住戶管理**
   - 新增住戶資料
   - 分配房間
   - 辦理入住/退房

4. **費用管理**
   - 每月進行電表抄錄
   - 系統自動計算費用
   - 管理付款狀態

5. **報表查看**
   - 查看營運概況
   - 分析收入趨勢
   - 匯出數據報表

## 🤝 貢獻指南

歡迎提交 Issue 和 Pull Request 來改善這個專案！

## 📄 授權條款

本專案採用 MIT 授權條款。

## 📞 技術支援

如有問題或建議，請提交 Issue 或聯繫開發團隊。
