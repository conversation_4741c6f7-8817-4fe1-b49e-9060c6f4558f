#!/usr/bin/env python3
"""
前端服務啟動腳本
"""

import subprocess
import sys
import os
import time
import requests

def check_dependencies():
    """檢查前端依賴"""
    print("🔍 檢查前端依賴...")

    try:
        import streamlit
        import pandas
        import plotly
        import requests
        print("✅ 前端依賴檢查通過")
        return True
    except ImportError as e:
        print(f"❌ 缺少依賴: {e}")
        print("請執行: uv sync")
        return False

def check_backend():
    """檢查後端服務是否運行"""
    print("🔍 檢查後端服務...")
    
    try:
        response = requests.get("http://localhost:8080/health", timeout=5)
        if response.status_code == 200:
            print("✅ 後端服務運行正常")
            return True
        else:
            print("❌ 後端服務響應異常")
            return False
    except requests.exceptions.RequestException:
        print("❌ 無法連接到後端服務")
        print("請先啟動後端服務: python run_backend.py")
        return False

def start_frontend():
    """啟動前端服務"""
    print("🚀 啟動前端服務...")
    
    # 切換到前端目錄
    frontend_dir = os.path.join(os.path.dirname(__file__), 'frontend')
    
    try:
        # 啟動Streamlit服務
        env = os.environ.copy()
        env["PYTHONIOENCODING"] = "utf-8"
        env["PYTHONPATH"] = "."

        cmd = [
            ".venv\\Scripts\\python", "-m", "streamlit", "run", "app.py",
            "--server.port", "8501",
            "--server.headless", "true",
            "--browser.gatherUsageStats", "false"
        ]
        
        print(f"執行命令: {' '.join(cmd)}")
        print("前端服務將在 http://localhost:8501 啟動")
        print("按 Ctrl+C 停止服務")
        print("-" * 50)
        
        # 在前端目錄中執行
        subprocess.run(cmd, cwd=frontend_dir, env=env)
        
    except KeyboardInterrupt:
        print("\n🛑 前端服務已停止")
    except Exception as e:
        print(f"❌ 啟動前端服務失敗: {e}")
        return False
    
    return True

def main():
    """主程式"""
    print("🏠 租房管理系統 - 前端服務")
    print("=" * 50)
    
    # 檢查依賴
    if not check_dependencies():
        sys.exit(1)
    
    # 檢查後端服務
    if not check_backend():
        print("\n💡 提示: 請在另一個終端窗口中啟動後端服務")
        choice = input("是否繼續啟動前端服務？(y/n): ").lower().strip()
        if choice != 'y':
            sys.exit(1)
    
    # 啟動前端服務
    start_frontend()

if __name__ == "__main__":
    main()
