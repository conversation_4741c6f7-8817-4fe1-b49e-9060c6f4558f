# =================== frontend/pages/residents.py ===================
import streamlit as st
import pandas as pd
from datetime import datetime, date
from api_client import api_client
from utils import (
    format_currency, format_date, validate_id_number, validate_phone_number,
    show_success_message, show_error_message, show_warning_message, show_info_message
)

def show_residents_page():
    """顯示住戶管理頁面"""
    st.title("👥 住戶管理")
    
    # 頁籤設計
    tab1, tab2, tab3, tab4 = st.tabs([
        "住戶列表", "新增住戶", "住戶統計", "入住/退房"
    ])
    
    with tab1:
        show_residents_list()
    
    with tab2:
        show_create_resident_form()
    
    with tab3:
        show_residents_statistics()
    
    with tab4:
        show_move_in_out_management()

def show_residents_list():
    """顯示住戶列表"""
    st.subheader("住戶列表")
    
    # 搜尋和篩選控制項
    col1, col2, col3, col4 = st.columns([2, 1, 1, 1])
    
    with col1:
        search_term = st.text_input("🔍 搜尋住戶", placeholder="姓名、身份證、電話")
    
    with col2:
        status_filter = st.selectbox("狀態篩選", ["全部", "在住", "已退房"])
    
    with col3:
        room_filter = st.selectbox("房間篩選", ["全部"] + get_room_options())
    
    with col4:
        if st.button("🔄 刷新數據"):
            st.rerun()
    
    # 獲取住戶數據
    residents = api_client.get_residents(active_only=(status_filter != "已退房"))
    
    if not residents:
        show_info_message("暫無住戶資料")
        return
    
    # 數據篩選
    filtered_residents = filter_residents(residents, search_term, status_filter, room_filter)
    
    # 數據表格展示
    if filtered_residents:
        display_residents_table(filtered_residents)
        
        # 住戶操作區域
        show_resident_operations(filtered_residents)
    else:
        show_info_message("沒有符合條件的住戶")

def get_room_options():
    """獲取房間選項"""
    try:
        rooms = api_client.get_rooms()
        return [room['room_number'] for room in rooms]
    except:
        return []

def filter_residents(residents, search_term, status_filter, room_filter):
    """篩選住戶數據"""
    filtered = residents
    
    # 搜尋篩選
    if search_term:
        search_lower = search_term.lower()
        filtered = [r for r in filtered if (
            search_lower in r['name'].lower() or
            search_lower in r['id_number'].lower() or
            (r['phone'] and search_lower in r['phone'].lower())
        )]
    
    # 狀態篩選
    if status_filter == "在住":
        filtered = [r for r in filtered if r['is_active']]
    elif status_filter == "已退房":
        filtered = [r for r in filtered if not r['is_active']]
    
    # 房間篩選
    if room_filter != "全部":
        filtered = [r for r in filtered if (
            r['room'] and r['room']['room_number'] == room_filter
        )]
    
    return filtered

def display_residents_table(residents):
    """顯示住戶表格"""
    # 準備顯示數據
    display_data = []
    for resident in residents:
        room_info = resident['room']
        display_data.append({
            "姓名": resident['name'],
            "身份證號": resident['id_number'],
            "電話": resident['phone'] or 'N/A',
            "房間": room_info['room_number'] if room_info else 'N/A',
            "入住日期": format_date(resident['move_in_date']),
            "退房日期": format_date(resident['move_out_date']) if resident['move_out_date'] else 'N/A',
            "押金": format_currency(resident['deposit']),
            "狀態": "🟢 在住" if resident['is_active'] else "🔴 已退房",
            "緊急聯絡人": resident['emergency_contact'] or 'N/A'
        })
    
    df = pd.DataFrame(display_data)
    
    # 設定表格樣式
    styled_df = df.style.applymap(
        lambda x: 'color: green' if '🟢' in str(x) else 'color: red' if '🔴' in str(x) else '',
        subset=['狀態']
    )
    
    st.dataframe(styled_df, use_container_width=True)

def show_resident_operations(residents):
    """顯示住戶操作"""
    st.subheader("住戶操作")
    
    if not residents:
        return
    
    # 選擇住戶
    resident_options = [f"{r['name']} ({r['id_number']}) - {r['room']['room_number'] if r['room'] else 'N/A'}" 
                       for r in residents]
    
    selected_idx = st.selectbox("選擇住戶", range(len(resident_options)), 
                               format_func=lambda x: resident_options[x])
    
    if selected_idx is not None:
        selected_resident = residents[selected_idx]
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("📋 查看詳情", use_container_width=True):
                show_resident_details(selected_resident)
        
        with col2:
            if st.button("✏️ 編輯資料", use_container_width=True):
                show_edit_resident_form(selected_resident)
        
        with col3:
            if selected_resident['is_active']:
                if st.button("🚪 辦理退房", use_container_width=True):
                    show_move_out_form(selected_resident)

def show_resident_details(resident):
    """顯示住戶詳情"""
    st.subheader(f"住戶詳情 - {resident['name']}")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### 👤 基本資料")
        st.text(f"姓名: {resident['name']}")
        st.text(f"身份證號: {resident['id_number']}")
        st.text(f"電話: {resident['phone'] or 'N/A'}")
        st.text(f"緊急聯絡人: {resident['emergency_contact'] or 'N/A'}")
        st.text(f"緊急聯絡電話: {resident['emergency_phone'] or 'N/A'}")
    
    with col2:
        st.markdown("### 🏠 住宿資料")
        room_info = resident['room']
        if room_info:
            st.text(f"房間號: {room_info['room_number']}")
            st.text(f"樓層: {room_info['floor'] or 'N/A'}")
            st.text(f"房間狀態: {room_info['status']}")
            st.text(f"目前住戶: {room_info['current_occupants']}/{room_info['max_occupants']}")
        
        st.text(f"入住日期: {format_date(resident['move_in_date'])}")
        st.text(f"退房日期: {format_date(resident['move_out_date']) if resident['move_out_date'] else 'N/A'}")
        st.text(f"押金: {format_currency(resident['deposit'])}")
        st.text(f"狀態: {'在住' if resident['is_active'] else '已退房'}")

def show_create_resident_form():
    """顯示新增住戶表單"""
    st.subheader("新增住戶")
    
    with st.form("create_resident_form"):
        # 基本資料區塊
        st.markdown("### 📋 基本資料")
        col1, col2 = st.columns(2)
        
        with col1:
            name = st.text_input("姓名*", placeholder="請輸入完整姓名")
            id_number = st.text_input("身份證號*", placeholder="A123456789", max_chars=10)
            phone = st.text_input("聯絡電話", placeholder="0912-345-678")
        
        with col2:
            emergency_contact = st.text_input("緊急聯絡人", placeholder="家人或朋友姓名")
            emergency_phone = st.text_input("緊急聯絡電話", placeholder="0987-654-321")
            deposit = st.number_input("押金金額", min_value=0, value=10000, step=1000)
        
        # 住宿資料區塊
        st.markdown("### 🏠 住宿資料")
        col3, col4 = st.columns(2)
        
        with col3:
            available_rooms = api_client.get_available_rooms()
            if available_rooms:
                room_options = [
                    f"{r['room_number']} (樓層{r['floor'] or 'N/A'}, 可住{r['max_occupants'] - r['current_occupants']}人)"
                    for r in available_rooms
                ]
                selected_room_idx = st.selectbox("選擇房間*", range(len(room_options)), 
                                                format_func=lambda x: room_options[x])
                selected_room = available_rooms[selected_room_idx]
            else:
                st.error("❌ 目前沒有可用房間")
                selected_room = None
        
        with col4:
            move_in_date = st.date_input("入住日期*", value=date.today())
            
            # 顯示租金資訊
            if selected_room:
                future_occupants = selected_room['current_occupants'] + 1
                rent = selected_room['rent_double'] if future_occupants == 2 else selected_room['rent_single']
                st.info(f"💰 預計租金: {format_currency(rent)} ({future_occupants}人住)")
        
        # 表單提交
        submitted = st.form_submit_button("👥 新增住戶", use_container_width=True)
        
        if submitted:
            # 表單驗證
            errors = validate_resident_form_data(name, id_number, phone, selected_room, move_in_date)
            
            if errors:
                for error in errors:
                    show_error_message(error)
            else:
                # 創建住戶
                resident_data = {
                    "name": name,
                    "id_number": id_number,
                    "phone": phone if phone else None,
                    "emergency_contact": emergency_contact if emergency_contact else None,
                    "emergency_phone": emergency_phone if emergency_phone else None,
                    "room_id": selected_room['id'],
                    "move_in_date": move_in_date.isoformat(),
                    "deposit": deposit
                }
                
                with st.spinner("正在新增住戶..."):
                    result = api_client.create_resident(resident_data)
                    
                    if result:
                        show_success_message(f"住戶 {name} 新增成功！")
                        st.rerun()

def validate_resident_form_data(name, id_number, phone, selected_room, move_in_date):
    """驗證住戶表單資料"""
    errors = []
    
    if not name or len(name.strip()) < 2:
        errors.append("姓名至少需要2個字符")
    
    if not validate_id_number(id_number):
        errors.append("請輸入有效的身份證號")
    
    if phone and not validate_phone_number(phone):
        errors.append("請輸入有效的電話號碼")
    
    if not selected_room:
        errors.append("請選擇房間")
    
    if move_in_date > date.today():
        errors.append("入住日期不能超過今天")
    
    return errors

def show_move_in_out_management():
    """顯示入住/退房管理"""
    st.subheader("入住/退房管理")
    
    # 頁籤
    sub_tab1, sub_tab2 = st.tabs(["退房管理", "入住記錄"])
    
    with sub_tab1:
        show_move_out_management()
    
    with sub_tab2:
        show_move_in_history()

def show_move_out_management():
    """退房管理"""
    st.markdown("### 🚪 住戶退房")
    
    # 獲取在住住戶
    active_residents = api_client.get_residents(active_only=True)
    
    if not active_residents:
        show_info_message("目前沒有住戶需要辦理退房")
        return
    
    # 選擇住戶
    resident_options = [f"{r['name']} - {r['room']['room_number'] if r['room'] else 'N/A'}" 
                       for r in active_residents]
    
    selected_idx = st.selectbox("選擇退房住戶", range(len(resident_options)), 
                               format_func=lambda x: resident_options[x])
    
    if selected_idx is not None:
        selected_resident = active_residents[selected_idx]
        
        # 顯示住戶資訊
        st.markdown("#### 住戶資訊")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.text(f"姓名: {selected_resident['name']}")
            st.text(f"身份證: {selected_resident['id_number']}")
        
        with col2:
            st.text(f"房間: {selected_resident['room']['room_number'] if selected_resident['room'] else 'N/A'}")
            st.text(f"入住日期: {format_date(selected_resident['move_in_date'])}")
        
        with col3:
            st.text(f"押金: {format_currency(selected_resident['deposit'])}")
            
            # 計算住宿天數
            if selected_resident['move_in_date']:
                move_in = datetime.fromisoformat(selected_resident['move_in_date'].replace('Z', '+00:00'))
                days_stayed = (datetime.now() - move_in).days
                st.text(f"住宿天數: {days_stayed}天")
        
        # 退房表單
        with st.form("move_out_form"):
            move_out_date = st.date_input("退房日期", value=date.today())
            notes = st.text_area("退房備註", placeholder="退房原因、房間狀況等")
            
            submitted = st.form_submit_button("🚪 確認退房", use_container_width=True)
            
            if submitted:
                if move_out_date < datetime.fromisoformat(selected_resident['move_in_date'].replace('Z', '+00:00')).date():
                    show_error_message("退房日期不能早於入住日期")
                else:
                    with st.spinner("正在處理退房..."):
                        result = api_client.move_out_resident(
                            selected_resident['id'], 
                            move_out_date.isoformat()
                        )
                        
                        if result:
                            show_success_message(f"住戶 {selected_resident['name']} 退房成功！")
                            st.rerun()

def show_move_in_history():
    """入住記錄"""
    st.markdown("### 📊 入住記錄統計")
    
    residents = api_client.get_residents(active_only=False)
    
    if not residents:
        show_info_message("暫無入住記錄")
        return
    
    # 統計資訊
    total_residents = len(residents)
    active_residents = len([r for r in residents if r['is_active']])
    moved_out_residents = total_residents - active_residents
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("總住戶數", total_residents)
    
    with col2:
        st.metric("在住住戶", active_residents)
    
    with col3:
        st.metric("已退房住戶", moved_out_residents)
    
    # 最近入住記錄
    st.markdown("### 📋 最近入住記錄")
    
    # 按入住日期排序
    recent_residents = sorted(residents, 
                            key=lambda x: x['move_in_date'] if x['move_in_date'] else '', 
                            reverse=True)[:10]
    
    if recent_residents:
        display_data = []
        for resident in recent_residents:
            display_data.append({
                "姓名": resident['name'],
                "房間": resident['room']['room_number'] if resident['room'] else 'N/A',
                "入住日期": format_date(resident['move_in_date']),
                "押金": format_currency(resident['deposit']),
                "狀態": "🟢 在住" if resident['is_active'] else "🔴 已退房"
            })
        
        df = pd.DataFrame(display_data)
        st.dataframe(df, use_container_width=True)

def show_residents_statistics():
    """顯示住戶統計"""
    st.subheader("住戶統計")
    
    residents = api_client.get_residents(active_only=False)
    
    if not residents:
        show_info_message("暫無統計資料")
        return
    
    # 基本統計
    total_residents = len(residents)
    active_residents = len([r for r in residents if r['is_active']])
    moved_out_residents = total_residents - active_residents
    
    # 押金統計
    total_deposits = sum(r['deposit'] for r in residents if r['is_active'])
    avg_deposit = total_deposits / active_residents if active_residents > 0 else 0
    
    # 顯示統計指標
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("總住戶數", total_residents)
    
    with col2:
        st.metric("在住住戶", active_residents)
    
    with col3:
        st.metric("已退房住戶", moved_out_residents)
    
    with col4:
        st.metric("平均押金", format_currency(avg_deposit))
    
    # 圖表展示
    col1, col2 = st.columns(2)
    
    with col1:
        # 住戶狀態分布
        import plotly.express as px
        
        status_data = {
            "狀態": ["在住", "已退房"],
            "人數": [active_residents, moved_out_residents]
        }
        
        fig1 = px.pie(
            status_data,
            values="人數",
            names="狀態",
            title="住戶狀態分布",
            color_discrete_map={"在住": "#2E8B57", "已退房": "#CD5C5C"}
        )
        
        st.plotly_chart(fig1, use_container_width=True)
    
    with col2:
        # 房間住戶分布
        room_stats = {}
        for resident in residents:
            if resident['is_active'] and resident['room']:
                room_number = resident['room']['room_number']
                room_stats[room_number] = room_stats.get(room_number, 0) + 1
        
        if room_stats:
            room_data = {
                "房間": list(room_stats.keys()),
                "住戶數": list(room_stats.values())
            }
            
            fig2 = px.bar(
                room_data,
                x="房間",
                y="住戶數",
                title="各房間住戶分布",
                color="住戶數",
                color_continuous_scale="viridis"
            )
            
            st.plotly_chart(fig2, use_container_width=True)
    
    # 押金統計表
    st.markdown("### 💰 押金統計")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("總押金", format_currency(total_deposits))
    
    with col2:
        max_deposit = max([r['deposit'] for r in residents if r['is_active']], default=0)
        st.metric("最高押金", format_currency(max_deposit))
    
    with col3:
        min_deposit = min([r['deposit'] for r in residents if r['is_active'] and r['deposit'] > 0], default=0)
        st.metric("最低押金", format_currency(min_deposit))

def show_edit_resident_form(resident):
    """顯示編輯住戶表單"""
    st.subheader(f"編輯住戶 - {resident['name']}")
    
    with st.form("edit_resident_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            name = st.text_input("姓名", value=resident['name'])
            phone = st.text_input("聯絡電話", value=resident['phone'] or "")
            emergency_contact = st.text_input("緊急聯絡人", value=resident['emergency_contact'] or "")
        
        with col2:
            id_number = st.text_input("身份證號", value=resident['id_number'], disabled=True)
            emergency_phone = st.text_input("緊急聯絡電話", value=resident['emergency_phone'] or "")
            deposit = st.number_input("押金金額", value=resident['deposit'], min_value=0, step=1000)
        
        submitted = st.form_submit_button("💾 保存修改", use_container_width=True)
        
        if submitted:
            # 驗證資料
            errors = []
            if not name or len(name.strip()) < 2:
                errors.append("姓名至少需要2個字符")
            if phone and not validate_phone_number(phone):
                errors.append("請輸入有效的電話號碼")
            
            if errors:
                for error in errors:
                    show_error_message(error)
            else:
                show_info_message("編輯功能開發中...")

def show_move_out_form(resident):
    """顯示退房表單"""
    st.subheader(f"辦理退房 - {resident['name']}")
    
    # 顯示住戶基本資訊
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.text(f"姓名: {resident['name']}")
        st.text(f"身份證: {resident['id_number']}")
    
    with col2:
        st.text(f"房間: {resident['room']['room_number'] if resident['room'] else 'N/A'}")
        st.text(f"入住日期: {format_date(resident['move_in_date'])}")
    
    with col3:
        st.text(f"押金: {format_currency(resident['deposit'])}")
        if resident['move_in_date']:
            move_in = datetime.fromisoformat(resident['move_in_date'].replace('Z', '+00:00'))
            days_stayed = (datetime.now() - move_in).days
            st.text(f"住宿天數: {days_stayed}天")
    
    # 退房表單
    with st.form("quick_move_out_form"):
        move_out_date = st.date_input("退房日期", value=date.today())
        
        submitted = st.form_submit_button("🚪 確認退房", use_container_width=True)
        
        if submitted:
            with st.spinner("正在處理退房..."):
                result = api_client.move_out_resident(
                    resident['id'], 
                    move_out_date.isoformat()
                )
                
                if result:
                    show_success_message(f"住戶 {resident['name']} 退房成功！")
                    st.rerun()