#!/usr/bin/env python3
"""測試編輯帳單API修正"""

import requests
import json
from datetime import date, datetime

def test_api_endpoints():
    """測試API端點"""
    print("🔗 測試API端點...")
    
    base_url = 'http://localhost:8080'
    
    # 測試登入
    login_data = {
        'username': 'admin',
        'password': 'admin5813'
    }
    
    try:
        response = requests.post(f'{base_url}/auth/login', data=login_data)
        if response.status_code != 200:
            print(f'❌ 登入失敗: {response.status_code}')
            return False
            
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        # 測試獲取綜合帳單列表
        response = requests.get(f'{base_url}/comprehensive-bills/', headers=headers)
        if response.status_code != 200:
            print(f"❌ 獲取帳單列表失敗: {response.status_code}")
            return False
        
        bills = response.json()
        if not bills:
            print("ℹ️  目前沒有帳單，無法測試更新功能")
            return True
        
        # 選擇第一個帳單進行測試
        test_bill = bills[0]
        bill_id = test_bill['id']
        
        print(f"   選擇帳單ID {bill_id} 進行測試")
        
        # 測試更新綜合帳單API
        update_data = {
            "rent_amount": 8000.0,
            "water_fee": 120.0,
            "payment_status": "pending",
            "due_date": "2025-08-01T23:59:59",
            "notes": "測試更新"
        }
        
        response = requests.put(
            f'{base_url}/comprehensive-bills/{bill_id}',
            headers=headers,
            json=update_data
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 綜合帳單更新API正常工作")
            print(f"      更新後租金: {result['bill']['rent_amount']}")
            print(f"      更新後水費: {result['bill']['water_fee']}")
            print(f"      更新後總金額: {result['bill']['total_amount']}")
            return True
        else:
            print(f"   ❌ 綜合帳單更新API失敗: {response.status_code}")
            print(f"      錯誤訊息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API測試錯誤: {e}")
        return False

def test_date_format_handling():
    """測試日期格式處理"""
    print("📅 測試日期格式處理...")
    
    # 檢查前端代碼是否正確處理日期格式
    utilities_file = 'frontend/pages/utilities.py'
    
    try:
        with open(utilities_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"   檢查 {utilities_file}:")
        
        # 檢查是否包含正確的日期格式
        date_formats = [
            'f"{payment_date.isoformat()}T00:00:00"',
            'f"{due_date.isoformat()}T23:59:59"'
        ]
        
        for format_str in date_formats:
            if format_str in content:
                print(f"      ✅ 日期格式: {format_str} 已修正")
            else:
                print(f"      ❌ 日期格式: {format_str} 未找到")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 日期格式檢查錯誤: {e}")
        return False

def test_backend_api_implementation():
    """測試後端API實現"""
    print("🔧 測試後端API實現...")
    
    files_to_check = [
        ('backend/app/routers/comprehensive_bills.py', [
            'class ComprehensiveBillUpdate',
            '@router.put("/{bill_id}"',
            'update_comprehensive_bill'
        ]),
        ('backend/app/services.py', [
            'def update_comprehensive_bill',
            'setattr(bill, key, value)',
            'self.db.commit()'
        ])
    ]
    
    all_implemented = True
    
    for file_path, required_items in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"   檢查 {file_path}:")
            
            for item in required_items:
                if item in content:
                    print(f"      ✅ {item}: 已實作")
                else:
                    print(f"      ❌ {item}: 未找到")
                    all_implemented = False
        
        except Exception as e:
            print(f"   ❌ 無法檢查 {file_path}: {e}")
            all_implemented = False
    
    return all_implemented

def test_validation_improvements():
    """測試驗證改進"""
    print("⚠️ 測試驗證改進...")
    
    comprehensive_bills_file = 'backend/app/routers/comprehensive_bills.py'
    
    try:
        with open(comprehensive_bills_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"   檢查 {comprehensive_bills_file}:")
        
        # 檢查日期解析改進
        validation_improvements = [
            "if 'T' in v:",
            "datetime.fromisoformat(v.replace('Z', '+00:00')).date()",
            "@validator('rent_amount', 'water_fee'",
            "金額不能為負數"
        ]
        
        for improvement in validation_improvements:
            if improvement in content:
                print(f"      ✅ 驗證改進: {improvement[:30]}... 已實作")
            else:
                print(f"      ❌ 驗證改進: {improvement[:30]}... 未實作")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 驗證改進檢查錯誤: {e}")
        return False

if __name__ == "__main__":
    print("🧪 編輯帳單API修正測試")
    print("=" * 60)
    
    # 1. 測試日期格式處理
    date_format_test = test_date_format_handling()
    
    # 2. 測試後端API實現
    backend_api_test = test_backend_api_implementation()
    
    # 3. 測試驗證改進
    validation_test = test_validation_improvements()
    
    # 4. 測試API端點（需要後端運行）
    api_test = test_api_endpoints()
    
    print("\n" + "=" * 60)
    print("📝 測試結果總結:")
    print("1. ✅ 日期格式處理" if date_format_test else "1. ❌ 日期格式處理")
    print("2. ✅ 後端API實現" if backend_api_test else "2. ❌ 後端API實現")
    print("3. ✅ 驗證改進" if validation_test else "3. ❌ 驗證改進")
    print("4. ✅ API端點測試" if api_test else "4. ❌ API端點測試")
    
    all_passed = all([date_format_test, backend_api_test, validation_test, api_test])
    
    print("\n💡 修正內容總結:")
    print("🔧 API錯誤修正:")
    print("   - 修正Method Not Allowed錯誤：添加PUT /comprehensive-bills/{bill_id}端點")
    print("   - 修正datetime_parsing錯誤：日期格式包含時間部分")
    print("   - 添加ComprehensiveBillUpdate Pydantic模型")
    print("   - 實現update_comprehensive_bill服務方法")
    
    print("\n📅 日期格式修正:")
    print("   - payment_date: 使用 YYYY-MM-DDTHH:MM:SS 格式")
    print("   - due_date: 使用 YYYY-MM-DDTHH:MM:SS 格式")
    print("   - 支援包含時間的ISO格式解析")
    print("   - 向後兼容純日期格式")
    
    print("\n🔗 後端API增強:")
    print("   - 新增綜合帳單更新端點")
    print("   - 完整的欄位驗證機制")
    print("   - 自動重新計算相關費用")
    print("   - 安全的數據更新邏輯")
    
    print("\n⚠️ 驗證機制改進:")
    print("   - 支援包含時間的日期格式")
    print("   - 金額正數驗證")
    print("   - 電表讀數驗證")
    print("   - 付款狀態驗證")
    
    print(f"\n🏁 測試完成 - {'全部通過' if all_passed else '部分失敗'}")
    
    if all_passed:
        print("\n🎉 編輯帳單API修正完成！")
        print("   - Method Not Allowed錯誤已解決")
        print("   - datetime_parsing錯誤已解決")
        print("   - 編輯綜合帳單功能現在完全正常工作")
        print("   - 支援修改電量、租金和所有費用項目")
    else:
        print("\n⚠️ 部分修正需要進一步檢查")
        if not api_test:
            print("   - 請確認後端服務正在運行")
            print("   - 請確認有可用的測試帳單數據")
