#!/usr/bin/env python3
"""
API端點檢查腳本
對比前端API客戶端與後端實際提供的API端點
"""

# 後端實際提供的API端點
BACKEND_ENDPOINTS = {
    # 認證API
    "auth": {
        "POST /auth/login": "用戶登入",
        "GET /auth/me": "獲取當前用戶資訊"
    },
    
    # 房間管理API
    "rooms": {
        "GET /rooms": "獲取房間列表",
        "POST /rooms": "創建新房間",
        "GET /rooms/available": "獲取可用房間",
        "GET /rooms/{room_id}": "獲取特定房間",
        "PUT /rooms/{room_id}": "更新房間資訊",
        "DELETE /rooms/{room_id}": "刪除房間"
    },
    
    # 住戶管理API
    "residents": {
        "GET /residents": "獲取住戶列表",
        "POST /residents": "創建新住戶",
        "GET /residents/{resident_id}": "獲取特定住戶", 
        "PUT /residents/{resident_id}": "更新住戶資訊",
        "POST /residents/{resident_id}/move-out": "住戶退房"
    },
    
    # 費用管理API
    "utilities": {
        "GET /utilities/rates": "獲取所有費率",
        "GET /utilities/rates/current": "獲取當前費率",
        "GET /utilities/rates/{rate_id}": "獲取特定費率",
        "POST /utilities/rates": "創建新費率",
        "PUT /utilities/rates/{rate_id}": "更新費率",
        "DELETE /utilities/rates/{rate_id}": "刪除費率",
        "POST /utilities/readings": "創建電表抄錄",
        "GET /utilities/bills": "獲取月度帳單",
        "PUT /utilities/bills/{bill_id}/payment": "更新付款狀態"
    },
    
    # 報表API
    "reports": {
        "GET /reports/dashboard": "獲取儀表板統計",
        "GET /reports/income-summary": "獲取收入統計"
    }
}

# 前端API客戶端呼叫的端點
FRONTEND_API_CALLS = {
    # 從 api_client.py 中提取的API呼叫
    "auth": {
        "POST /auth/login": "login()",
        "GET /auth/me": "get_current_user()"
    },
    
    "rooms": {
        "GET /rooms": "get_rooms()",
        "POST /rooms": "create_room()",
        "GET /rooms/available": "get_available_rooms()",
        "GET /rooms/{room_id}": "get_room_by_id()",
        "PUT /rooms/{room_id}": "update_room()",
        "DELETE /rooms/{room_id}": "delete_room()"
    },
    
    "residents": {
        "GET /residents": "get_residents()",
        "POST /residents": "create_resident()",
        "GET /residents/{resident_id}": "get_resident_by_id()",
        "PUT /residents/{resident_id}": "update_resident()",
        "POST /residents/{resident_id}/move-out": "move_out_resident()"
    },
    
    "utilities": {
        "GET /utilities/rates": "get_utility_rates()",
        "GET /utilities/rates/current": "get_current_utility_rate()",
        "GET /utilities/rates/{rate_id}": "get_utility_rate_by_id()",
        "POST /utilities/rates": "create_utility_rate()",
        "PUT /utilities/rates/{rate_id}": "update_utility_rate()",
        "DELETE /utilities/rates/{rate_id}": "delete_utility_rate()",
        "POST /utilities/readings": "create_meter_reading()",
        "GET /utilities/bills": "get_utility_bills()",
        "PUT /utilities/bills/{bill_id}/payment": "update_payment_status()"
    },
    
    "reports": {
        "GET /reports/dashboard": "get_dashboard_stats()",
        "GET /reports/income-summary": "get_income_summary()"
    }
}

def check_api_endpoints():
    """檢查API端點一致性"""
    print("🔍 API端點一致性檢查")
    print("=" * 60)
    
    all_match = True
    
    for category in BACKEND_ENDPOINTS:
        print(f"\n📂 {category.upper()} API")
        print("-" * 40)
        
        backend_endpoints = set(BACKEND_ENDPOINTS[category].keys())
        frontend_endpoints = set(FRONTEND_API_CALLS.get(category, {}).keys())
        
        # 檢查前端缺少的端點
        missing_in_frontend = backend_endpoints - frontend_endpoints
        if missing_in_frontend:
            all_match = False
            print("❌ 前端缺少的端點:")
            for endpoint in missing_in_frontend:
                print(f"   - {endpoint}: {BACKEND_ENDPOINTS[category][endpoint]}")
        
        # 檢查前端多餘的端點
        extra_in_frontend = frontend_endpoints - backend_endpoints
        if extra_in_frontend:
            all_match = False
            print("⚠️  前端多餘的端點:")
            for endpoint in extra_in_frontend:
                print(f"   - {endpoint}")
        
        # 檢查匹配的端點
        matching_endpoints = backend_endpoints & frontend_endpoints
        if matching_endpoints:
            print("✅ 匹配的端點:")
            for endpoint in matching_endpoints:
                frontend_method = FRONTEND_API_CALLS[category][endpoint]
                print(f"   - {endpoint} → {frontend_method}")
    
    print("\n" + "=" * 60)
    if all_match:
        print("🎉 所有API端點完全匹配！")
    else:
        print("⚠️  發現API端點不一致，需要修正")
    
    return all_match

if __name__ == "__main__":
    check_api_endpoints()
