#!/usr/bin/env python3
"""驗證所有修正的測試文件"""

import requests
import sys
import os
from datetime import date, datetime, timedelta

def test_resident_update_fix():
    """測試住戶更新API的SQLite DateTime錯誤修正"""
    print("🔧 測試住戶更新API修正...")
    
    base_url = 'http://localhost:8080'
    login_data = {
        'username': 'admin',
        'password': 'admin5813'
    }
    
    try:
        # 登入
        response = requests.post(f'{base_url}/auth/login', data=login_data)
        if response.status_code != 200:
            print(f'❌ 登入失敗: {response.status_code}')
            return False
            
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        # 獲取住戶列表
        response = requests.get(f'{base_url}/residents/', headers=headers)
        if response.status_code != 200:
            print("❌ 無法獲取住戶列表")
            return False
            
        residents = response.json()
        if not residents:
            print("❌ 沒有住戶")
            return False
        
        # 選擇第一個住戶進行測試
        test_resident = residents[0]
        resident_id = test_resident['id']
        
        # 測試更新住戶資訊（包含日期）
        update_data = {
            "name": test_resident['name'],
            "phone": test_resident.get('phone', '0912345678'),
            "lease_end_date": "2025-12-31"  # 字符串格式日期
        }
        
        response = requests.put(f'{base_url}/residents/{resident_id}', 
                              json=update_data, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 住戶更新成功")
            print(f"      住戶: {result['name']}")
            print(f"      租約到期日: {result.get('lease_end_date', 'N/A')}")
            return True
        else:
            print(f"   ❌ 住戶更新失敗: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 住戶更新測試錯誤: {e}")
        return False

def test_roc_year_display():
    """測試民國年顯示功能"""
    print("📅 測試民國年顯示功能...")
    
    try:
        # 檢查前端文件是否正確修改
        files_to_check = [
            ('frontend/pages/utilities.py', ['民國年', '計費年份（民國年）']),
            ('frontend/pages/comprehensive_bills.py', ['民國年', '計費年份（民國年）'])
        ]
        
        for file_path, keywords in files_to_check:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                print(f"   檢查 {file_path}:")
                
                for keyword in keywords:
                    if keyword in content:
                        print(f"      ✅ {keyword}: 已更新")
                    else:
                        print(f"      ❌ {keyword}: 未找到")
                        return False
            else:
                print(f"   ❌ {file_path} 不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 民國年顯示測試錯誤: {e}")
        return False

def test_lease_extension_removal():
    """測試租期展期功能移除"""
    print("🚫 測試租期展期功能移除...")
    
    try:
        # 檢查前端文件是否移除了租期展期功能
        file_path = 'frontend/pages/residents.py'
        
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"   檢查 {file_path}:")
            
            # 檢查是否移除了租期展期相關功能
            removed_functions = [
                'show_lease_extension_form',
                'show_lease_extension_confirmation',
                '租期展期',
                'extend_lease'
            ]
            
            all_removed = True
            for func in removed_functions:
                if func in content:
                    print(f"      ❌ {func}: 仍存在")
                    all_removed = False
                else:
                    print(f"      ✅ {func}: 已移除")
            
            return all_removed
        else:
            print(f"   ❌ {file_path} 不存在")
            return False
        
    except Exception as e:
        print(f"❌ 租期展期功能移除測試錯誤: {e}")
        return False

def test_bills_statistics():
    """測試帳單統計功能"""
    print("📊 測試帳單統計功能...")
    
    try:
        # 檢查綜合帳單統計功能是否實作
        file_path = 'frontend/pages/comprehensive_bills.py'
        
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"   檢查 {file_path}:")
            
            # 檢查統計功能是否實作
            statistics_functions = [
                'generate_statistics_report',
                'show_basic_statistics',
                'show_income_analysis',
                'show_payment_analysis',
                'show_monthly_trends'
            ]
            
            all_implemented = True
            for func in statistics_functions:
                if func in content:
                    print(f"      ✅ {func}: 已實作")
                else:
                    print(f"      ❌ {func}: 未實作")
                    all_implemented = False
            
            # 檢查是否移除了"開發中"提示
            if "統計功能開發中" in content:
                print(f"      ❌ 仍顯示開發中提示")
                all_implemented = False
            else:
                print(f"      ✅ 已移除開發中提示")
            
            return all_implemented
        else:
            print(f"   ❌ {file_path} 不存在")
            return False
        
    except Exception as e:
        print(f"❌ 帳單統計功能測試錯誤: {e}")
        return False

def test_comprehensive_bills_api():
    """測試綜合帳單API功能"""
    print("💰 測試綜合帳單API功能...")
    
    base_url = 'http://localhost:8080'
    login_data = {
        'username': 'admin',
        'password': 'admin5813'
    }
    
    try:
        # 登入
        response = requests.post(f'{base_url}/auth/login', data=login_data)
        if response.status_code != 200:
            print(f'❌ 登入失敗: {response.status_code}')
            return False
            
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        # 測試獲取綜合帳單列表
        response = requests.get(f'{base_url}/comprehensive-bills/', headers=headers)
        
        if response.status_code == 200:
            bills = response.json()
            print(f"   ✅ 綜合帳單API正常，共 {len(bills)} 筆帳單")
            
            # 如果有帳單，測試獲取單個帳單
            if bills:
                bill_id = bills[0]['id']
                response = requests.get(f'{base_url}/comprehensive-bills/{bill_id}', headers=headers)
                
                if response.status_code == 200:
                    bill = response.json()
                    print(f"      ✅ 單個帳單獲取成功: {bill['room']['room_number']}")
                    return True
                else:
                    print(f"      ❌ 單個帳單獲取失敗: {response.status_code}")
                    return False
            else:
                print(f"      ℹ️  目前沒有帳單數據")
                return True
        else:
            print(f"   ❌ 綜合帳單API失敗: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 綜合帳單API測試錯誤: {e}")
        return False

if __name__ == "__main__":
    print("🧪 修正驗證測試")
    print("=" * 60)
    
    # 1. 測試住戶更新API修正
    resident_update_test = test_resident_update_fix()
    
    # 2. 測試民國年顯示功能
    roc_year_test = test_roc_year_display()
    
    # 3. 測試租期展期功能移除
    lease_extension_test = test_lease_extension_removal()
    
    # 4. 測試帳單統計功能
    statistics_test = test_bills_statistics()
    
    # 5. 測試綜合帳單API功能
    api_test = test_comprehensive_bills_api()
    
    print("\n" + "=" * 60)
    print("📝 測試結果總結:")
    print("1. ✅ 住戶更新API修正" if resident_update_test else "1. ❌ 住戶更新API修正")
    print("2. ✅ 民國年顯示功能" if roc_year_test else "2. ❌ 民國年顯示功能")
    print("3. ✅ 租期展期功能移除" if lease_extension_test else "3. ❌ 租期展期功能移除")
    print("4. ✅ 帳單統計功能開發" if statistics_test else "4. ❌ 帳單統計功能開發")
    print("5. ✅ 綜合帳單API功能" if api_test else "5. ❌ 綜合帳單API功能")
    
    all_passed = all([
        resident_update_test,
        roc_year_test,
        lease_extension_test,
        statistics_test,
        api_test
    ])
    
    print("\n💡 修正內容總結:")
    print("🔧 住戶更新API修正:")
    print("   - 修正SQLite DateTime類型錯誤")
    print("   - 在ResidentService中添加日期格式轉換")
    print("   - 支援字符串日期自動轉換為date對象")
    
    print("\n📅 民國年顯示修正:")
    print("   - 電表抄錄年份改為民國年輸入")
    print("   - 綜合帳單年份改為民國年輸入")
    print("   - 帳單顯示改為民國年格式")
    
    print("\n🚫 租期展期功能移除:")
    print("   - 移除住戶詳情中的租期展期功能")
    print("   - 移除相關的展期表單和確認對話框")
    print("   - 保留基本的租約到期日編輯功能")
    
    print("\n📊 帳單統計功能開發:")
    print("   - 實作完整的統計報表功能")
    print("   - 基本統計：總帳單數、總金額、付款率")
    print("   - 收入分析：房間收入排行")
    print("   - 付款分析：按狀態統計")
    print("   - 趨勢分析：月度收入趨勢")
    
    print(f"\n🏁 測試完成 - {'全部通過' if all_passed else '部分失敗'}")
    
    if all_passed:
        print("\n🎉 所有修正都已成功實作並測試通過！")
        print("   系統現在具備完整的功能和正確的顯示格式。")
