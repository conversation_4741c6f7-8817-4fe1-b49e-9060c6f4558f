from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel, validator
from typing import List, Optional, Union
from datetime import datetime, date
from ..database import get_db
from ..auth import get_current_user
from ..services import RentService
from ..models import User, RentRecord

router = APIRouter(prefix="/rent", tags=["租金管理"])

class RentRecordCreate(BaseModel):
    room_id: int
    resident_id: int
    rent_year: int
    rent_month: int
    rent_amount: float
    due_date: Union[date, str]
    notes: Optional[str] = None
    
    @validator('due_date', pre=True)
    def parse_due_date(cls, v):
        """解析到期日期格式"""
        if isinstance(v, str):
            try:
                return datetime.strptime(v, '%Y-%m-%d').date()
            except ValueError:
                try:
                    return datetime.fromisoformat(v.replace('Z', '+00:00')).date()
                except ValueError:
                    raise ValueError('到期日期格式錯誤，請使用 YYYY-MM-DD 格式')
        elif isinstance(v, datetime):
            return v.date()
        elif isinstance(v, date):
            return v
        else:
            raise ValueError('到期日期格式錯誤，請使用 YYYY-MM-DD 格式')
    
    @validator('rent_amount')
    def validate_rent_amount(cls, v):
        if v <= 0:
            raise ValueError('租金金額必須大於0')
        return v
    
    @validator('rent_year')
    def validate_rent_year(cls, v):
        current_year = datetime.now().year
        if v < current_year - 1 or v > current_year + 1:
            raise ValueError(f'租金年份必須在 {current_year-1} 到 {current_year+1} 之間')
        return v
    
    @validator('rent_month')
    def validate_rent_month(cls, v):
        if v < 1 or v > 12:
            raise ValueError('租金月份必須在 1 到 12 之間')
        return v

class RentRecordUpdate(BaseModel):
    rent_amount: Optional[float] = None
    payment_status: Optional[str] = None
    payment_date: Optional[datetime] = None
    notes: Optional[str] = None

class RentRecordResponse(BaseModel):
    id: int
    room_id: int
    resident_id: int
    rent_year: int
    rent_month: int
    rent_amount: float
    payment_status: str
    payment_date: Optional[str]
    due_date: str
    notes: Optional[str]
    created_at: str
    room: Optional[dict]
    resident: Optional[dict]

@router.get("/", response_model=List[RentRecordResponse])
async def get_rent_records(
    year: Optional[int] = None,
    month: Optional[int] = None,
    room_id: Optional[int] = None,
    payment_status: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """獲取租金記錄列表"""
    try:
        records = RentService.get_rent_records(
            db, 
            year=year, 
            month=month, 
            room_id=room_id, 
            payment_status=payment_status
        )
        return [record.to_dict() for record in records]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"獲取租金記錄失敗: {str(e)}"
        )

@router.post("/", response_model=RentRecordResponse)
async def create_rent_record(
    rent: RentRecordCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """創建租金記錄"""
    try:
        rent_data = rent.dict()
        
        # 確保 due_date 轉換為 datetime 格式
        if isinstance(rent_data['due_date'], date):
            rent_data['due_date'] = datetime.combine(rent_data['due_date'], datetime.min.time())
        
        # 檢查是否已有相同年月的租金記錄
        existing_record = RentService.get_rent_record_by_period(
            db, rent_data['room_id'], rent_data['resident_id'], 
            rent_data['rent_year'], rent_data['rent_month']
        )
        if existing_record:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="該房間該月份已有租金記錄"
            )
        
        new_record = RentService.create_rent_record(db, rent_data)
        return new_record.to_dict()
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"創建租金記錄失敗: {str(e)}"
        )

@router.put("/{record_id}", response_model=RentRecordResponse)
async def update_rent_record(
    record_id: int,
    rent_update: RentRecordUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新租金記錄"""
    try:
        record = RentService.get_rent_record_by_id(db, record_id)
        if not record:
            raise HTTPException(status_code=404, detail="租金記錄不存在")
        
        # 驗證更新資料
        update_data = rent_update.dict(exclude_unset=True)
        
        if 'rent_amount' in update_data and update_data['rent_amount'] <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="租金金額必須大於0"
            )
        
        if 'payment_status' in update_data and update_data['payment_status'] not in ['待付款', '已付款', '逾期']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="付款狀態必須是 '待付款'、'已付款' 或 '逾期'"
            )
        
        updated_record = RentService.update_rent_record(db, record_id, update_data)
        return updated_record.to_dict()
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新租金記錄失敗: {str(e)}"
        )

@router.delete("/{record_id}")
async def delete_rent_record(
    record_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """刪除租金記錄"""
    try:
        record = RentService.get_rent_record_by_id(db, record_id)
        if not record:
            raise HTTPException(status_code=404, detail="租金記錄不存在")
        
        success = RentService.delete_rent_record(db, record_id)
        if success:
            return {"message": "租金記錄刪除成功"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="刪除租金記錄失敗"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"刪除租金記錄時發生錯誤: {str(e)}"
        )

@router.get("/statistics")
async def get_rent_statistics(
    year: Optional[int] = None,
    month: Optional[int] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """獲取租金統計資料"""
    try:
        stats = RentService.get_rent_statistics(db, year, month)
        return stats
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"獲取租金統計失敗: {str(e)}"
        )
