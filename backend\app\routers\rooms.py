from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel, validator
from typing import List, Optional
from ..database import get_db
from ..auth import get_current_user
from ..services import RoomService
from ..models import User, Room

router = APIRouter(prefix="/rooms", tags=["房間管理"])

class RoomCreate(BaseModel):
    room_number: str
    floor: Optional[int] = None
    area: Optional[float] = None
    rent_single: float
    rent_double: float
    description: Optional[str] = None

    @validator('area', pre=True)
    def validate_area(cls, v):
        # 處理 "N/A" 字符串，轉換為 0
        if isinstance(v, str):
            v_upper = v.strip().upper()
            if v_upper in ['N/A', 'NA']:
                return 0.0
            elif v_upper == '':
                return None  # 空字符串轉換為 None
            else:
                # 嘗試轉換其他字符串為數字
                try:
                    v = float(v)
                except (ValueError, TypeError):
                    return None  # 非數字字符串轉換為 None
        # 處理 None 值
        if v is None:
            return None
        # 轉換為浮點數
        try:
            v = float(v)
        except (ValueError, TypeError):
            return None
        # 驗證不能為負數
        if v < 0:
            raise ValueError('面積不能為負數')
        return v

    @validator('rent_single', pre=True)
    def validate_rent_single(cls, v):
        # 處理字符串輸入
        if isinstance(v, str):
            v_upper = v.strip().upper()
            if v_upper in ['N/A', 'NA']:
                return 0.0
            elif v_upper == '':
                raise ValueError('單人租金不能為空')
            else:
                try:
                    v = float(v)
                except (ValueError, TypeError):
                    raise ValueError('單人租金必須是有效數字')
        # 轉換為浮點數
        try:
            v = float(v)
        except (ValueError, TypeError):
            raise ValueError('單人租金必須是有效數字')
        # 驗證必須大於0
        if v <= 0:
            raise ValueError('單人租金必須大於0')
        return v

    @validator('rent_double', pre=True)
    def validate_rent_double(cls, v):
        # 處理字符串輸入
        if isinstance(v, str):
            v_upper = v.strip().upper()
            if v_upper in ['N/A', 'NA']:
                return 0.0
            elif v_upper == '':
                raise ValueError('雙人租金不能為空')
            else:
                try:
                    v = float(v)
                except (ValueError, TypeError):
                    raise ValueError('雙人租金必須是有效數字')
        # 轉換為浮點數
        try:
            v = float(v)
        except (ValueError, TypeError):
            raise ValueError('雙人租金必須是有效數字')
        # 驗證必須大於0
        if v <= 0:
            raise ValueError('雙人租金必須大於0')
        return v

class RoomUpdate(BaseModel):
    floor: Optional[int] = None
    area: Optional[float] = None
    rent_single: Optional[float] = None
    rent_double: Optional[float] = None
    max_occupants: Optional[int] = None
    description: Optional[str] = None
    status: Optional[str] = None

    @validator('floor')
    def validate_floor(cls, v):
        if v is not None and v <= 0:
            raise ValueError('樓層必須大於0')
        return v

    @validator('area', pre=True)
    def validate_area(cls, v):
        # 處理 "N/A" 字符串，轉換為 0
        if isinstance(v, str):
            v_upper = v.strip().upper()
            if v_upper in ['N/A', 'NA']:
                return 0.0
            elif v_upper == '':
                return None  # 空字符串轉換為 None
            else:
                # 嘗試轉換其他字符串為數字
                try:
                    v = float(v)
                except (ValueError, TypeError):
                    return None  # 非數字字符串轉換為 None
        # 處理 None 值
        if v is None:
            return None
        # 轉換為浮點數
        try:
            v = float(v)
        except (ValueError, TypeError):
            return None
        # 驗證不能為負數
        if v < 0:
            raise ValueError('面積不能為負數')
        return v

    @validator('rent_single', pre=True)
    def validate_rent_single(cls, v):
        # 處理字符串輸入
        if isinstance(v, str):
            v_upper = v.strip().upper()
            if v_upper in ['N/A', 'NA']:
                return 0.0
            elif v_upper == '':
                return None
            else:
                try:
                    v = float(v)
                except (ValueError, TypeError):
                    return None
        # 處理 None 值
        if v is None:
            return None
        # 轉換為浮點數
        try:
            v = float(v)
        except (ValueError, TypeError):
            return None
        # 驗證必須大於0（如果不是 None）
        if v is not None and v <= 0:
            raise ValueError('單人租金必須大於0')
        return v

    @validator('rent_double', pre=True)
    def validate_rent_double(cls, v):
        # 處理字符串輸入
        if isinstance(v, str):
            v_upper = v.strip().upper()
            if v_upper in ['N/A', 'NA']:
                return 0.0
            elif v_upper == '':
                return None
            else:
                try:
                    v = float(v)
                except (ValueError, TypeError):
                    return None
        # 處理 None 值
        if v is None:
            return None
        # 轉換為浮點數
        try:
            v = float(v)
        except (ValueError, TypeError):
            return None
        # 驗證必須大於0（如果不是 None）
        if v is not None and v <= 0:
            raise ValueError('雙人租金必須大於0')
        return v

    @validator('max_occupants')
    def validate_max_occupants(cls, v):
        if v is not None and (v <= 0 or v > 4):
            raise ValueError('最大住戶數必須在1-4之間')
        return v

    @validator('status')
    def validate_status(cls, v):
        if v is not None and v not in ['available', 'occupied', 'partial', 'maintenance']:
            raise ValueError('房間狀態必須是 available, occupied, partial 或 maintenance')
        return v

class RoomResponse(BaseModel):
    id: int
    room_number: str
    floor: Optional[int]
    area: Optional[float]
    rent_single: float
    rent_double: float
    current_occupants: int
    max_occupants: int
    status: str
    description: Optional[str]
    is_active: bool
    current_rent: float

@router.get("/", response_model=List[RoomResponse])
async def get_rooms(
    include_inactive: bool = False,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """獲取房間列表"""
    rooms = RoomService.get_all_rooms(db, include_inactive)
    return [room.to_dict() for room in rooms]

@router.post("/", response_model=RoomResponse)
async def create_room(
    room: RoomCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """創建新房間"""
    try:
        room_data = room.dict()
        new_room = RoomService.create_room(db, room_data)
        return new_room.to_dict()
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/available", response_model=List[RoomResponse])
async def get_available_rooms(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """獲取可用房間"""
    rooms = RoomService.get_available_rooms(db)
    return [room.to_dict() for room in rooms]

@router.get("/{room_id}", response_model=RoomResponse)
async def get_room(
    room_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """獲取房間詳情"""
    room = RoomService.get_room_by_id(db, room_id)
    if not room:
        raise HTTPException(status_code=404, detail="房間不存在")
    return room.to_dict()

@router.put("/{room_id}", response_model=RoomResponse)
async def update_room(
    room_id: int,
    room_update: RoomUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新房間資訊"""
    try:
        room = RoomService.get_room_by_id(db, room_id)
        if not room:
            raise HTTPException(status_code=404, detail="房間不存在")

        # 驗證更新資料
        update_data = room_update.dict(exclude_unset=True)

        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="沒有提供要更新的資料"
            )

        # 檢查租金邏輯
        current_rent_single = room.rent_single
        current_rent_double = room.rent_double
        new_rent_single = update_data.get('rent_single', current_rent_single)
        new_rent_double = update_data.get('rent_double', current_rent_double)

        if new_rent_double < new_rent_single:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="雙人租金不能小於單人租金"
            )

        # 使用RoomService更新房間
        updated_room = RoomService.update_room(db, room_id, update_data)
        if not updated_room:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="更新房間資料失敗"
            )

        return updated_room.to_dict()

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新房間失敗: {str(e)}"
        )

@router.delete("/{room_id}")
async def delete_room(
    room_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """刪除房間（軟刪除）"""
    # 檢查管理員權限（如果需要的話）
    # 這裡可以添加額外的權限檢查

    # 使用服務層的軟刪除方法
    result = RoomService.soft_delete_room(db, room_id, current_user)

    if not result["success"]:
        # 根據錯誤類型返回適當的HTTP狀態碼
        if result["error_code"] == "ROOM_NOT_FOUND":
            raise HTTPException(status_code=404, detail=result["error"])
        elif result["error_code"] == "HAS_ACTIVE_RESIDENTS":
            raise HTTPException(status_code=400, detail=result["error"])
        else:
            raise HTTPException(status_code=500, detail=result["error"])

    # 返回成功結果
    return {
        "success": True,
        "message": result["message"],
        "room_id": result["room_id"]
    }
