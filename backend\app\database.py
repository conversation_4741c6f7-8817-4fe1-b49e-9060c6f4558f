from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from contextlib import contextmanager
from .config import settings

# 創建資料庫引擎
engine = create_engine(
    settings.database_url,
    connect_args={"check_same_thread": False}
)

# 創建會話工廠
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 創建基礎模型類
Base = declarative_base()

def get_db():
    """資料庫依賴注入"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def create_tables():
    """創建資料庫表格"""
    Base.metadata.create_all(bind=engine)
