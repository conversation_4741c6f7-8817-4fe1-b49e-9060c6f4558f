#!/usr/bin/env python3
"""測試住戶租期展期功能（使用現有住戶）"""

import requests
import sys
import os
from datetime import date, datetime, timedelta

def get_existing_resident():
    """獲取現有活躍住戶進行測試"""
    print("🏠 獲取現有活躍住戶...")
    
    base_url = 'http://localhost:8080'
    login_data = {
        'username': 'admin',
        'password': 'admin5813'
    }
    
    try:
        # 登入
        response = requests.post(f'{base_url}/auth/login', data=login_data)
        if response.status_code != 200:
            print(f'❌ 登入失敗: {response.status_code}')
            return None
            
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        # 獲取活躍住戶
        response = requests.get(f'{base_url}/residents?active_only=true', headers=headers)
        if response.status_code != 200:
            print("❌ 無法獲取住戶列表")
            return None
            
        residents = response.json()
        if not residents:
            print("❌ 沒有活躍住戶")
            return None
        
        # 使用第一個住戶
        test_resident = residents[0]
        print(f"✅ 使用現有住戶進行測試")
        print(f"   姓名: {test_resident['name']}")
        print(f"   ID: {test_resident['id']}")
        print(f"   當前租約到期日: {test_resident.get('lease_end_date', 'N/A')}")
        
        return test_resident['id'], token, test_resident
            
    except Exception as e:
        print(f"❌ 獲取現有住戶錯誤: {e}")
        return None

def test_lease_extension_api(resident_id, token, original_resident):
    """測試租期展期 API"""
    print(f"\n📅 測試租期展期 API (住戶 ID: {resident_id})...")
    
    base_url = 'http://localhost:8080'
    headers = {'Authorization': f'Bearer {token}'}
    
    try:
        # 記錄原始租約到期日
        original_lease_end = original_resident.get('lease_end_date')
        print(f"   原始租約到期日: {original_lease_end}")
        
        # 1. 測試獲取租約資訊
        print("   測試獲取租約資訊...")
        response = requests.get(f'{base_url}/residents/{resident_id}/lease-info', headers=headers)
        
        if response.status_code == 200:
            lease_info = response.json()
            print(f"   ✅ 租約資訊獲取成功")
            print(f"      當前到期日: {lease_info.get('lease_end_date')}")
            print(f"      租約狀態: {lease_info.get('lease_status')}")
            print(f"      剩餘天數: {lease_info.get('days_until_expiry')}")
        else:
            print(f"   ❌ 獲取租約資訊失敗: {response.status_code}")
            return False, original_lease_end
        
        # 2. 測試正常展期
        print("   測試正常展期（延長6個月）...")
        
        # 計算新的到期日（基於當前到期日或今天）
        if original_lease_end:
            base_date = datetime.strptime(original_lease_end, '%Y-%m-%d').date()
        else:
            base_date = date.today()
        
        new_lease_end_date = (base_date + timedelta(days=180)).isoformat()
        extension_data = {
            "new_lease_end_date": new_lease_end_date,
            "extension_reason": "測試展期功能"
        }
        
        response = requests.post(f'{base_url}/residents/{resident_id}/extend-lease', 
                               json=extension_data, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 正常展期成功")
            print(f"      原到期日: {result['extension_details']['original_lease_end_date']}")
            print(f"      新到期日: {result['extension_details']['new_lease_end_date']}")
            print(f"      展期天數: {result['extension_details']['extended_by_days']}")
            
            # 記錄新的到期日，用於後續恢復
            new_lease_end = result['extension_details']['new_lease_end_date']
        else:
            print(f"   ❌ 正常展期失敗: {response.status_code} - {response.text}")
            return False, original_lease_end
        
        # 3. 測試無效展期（日期早於當前到期日）
        print("   測試無效展期（日期早於當前到期日）...")
        invalid_date = (date.today() - timedelta(days=30)).isoformat()
        invalid_extension_data = {
            "new_lease_end_date": invalid_date,
            "extension_reason": "測試無效展期"
        }
        
        response = requests.post(f'{base_url}/residents/{resident_id}/extend-lease', 
                               json=invalid_extension_data, headers=headers)
        
        if response.status_code == 400:
            print(f"   ✅ 無效展期正確被拒絕: {response.json().get('detail')}")
        else:
            print(f"   ❌ 無效展期未被正確拒絕: {response.status_code}")
            return False, new_lease_end
        
        # 4. 測試再次展期
        print("   測試再次展期（延長1年）...")
        current_date = datetime.strptime(new_lease_end, '%Y-%m-%d').date()
        new_lease_end_date_2 = (current_date + timedelta(days=365)).isoformat()
        extension_data_2 = {
            "new_lease_end_date": new_lease_end_date_2,
            "extension_reason": "第二次測試展期"
        }
        
        response = requests.post(f'{base_url}/residents/{resident_id}/extend-lease', 
                               json=extension_data_2, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 再次展期成功")
            print(f"      最新到期日: {result['extension_details']['new_lease_end_date']}")
            final_lease_end = result['extension_details']['new_lease_end_date']
        else:
            print(f"   ❌ 再次展期失敗: {response.status_code} - {response.text}")
            return False, new_lease_end
        
        return True, final_lease_end
        
    except Exception as e:
        print(f"❌ 租期展期 API 測試錯誤: {e}")
        return False, original_lease_end

def restore_original_lease_date(resident_id, token, original_lease_end):
    """恢復原始租約到期日"""
    print(f"\n🔄 恢復原始租約到期日...")
    
    if not original_lease_end:
        print("   ℹ️  原本沒有設定租約到期日，跳過恢復")
        return True
    
    base_url = 'http://localhost:8080'
    headers = {'Authorization': f'Bearer {token}'}
    
    try:
        # 直接更新住戶資訊來恢復原始日期
        update_data = {
            "lease_end_date": original_lease_end
        }
        
        response = requests.put(f'{base_url}/residents/{resident_id}', 
                              json=update_data, headers=headers)
        
        if response.status_code == 200:
            print(f"   ✅ 已恢復原始租約到期日: {original_lease_end}")
            return True
        else:
            print(f"   ⚠️  恢復原始租約到期日失敗: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 恢復原始租約到期日錯誤: {e}")
        return False

def test_frontend_integration():
    """測試前端整合"""
    print(f"\n🖥️ 測試前端整合...")
    
    try:
        # 檢查前端文件是否正確修改
        files_to_check = [
            'frontend/pages/residents.py',
            'frontend/api_client.py'
        ]
        
        for file_path in files_to_check:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                print(f"   檢查 {file_path}:")
                
                if file_path.endswith('residents.py'):
                    # 檢查住戶頁面是否添加了展期功能
                    features_to_check = [
                        'show_lease_extension_form',
                        'show_lease_extension_confirmation',
                        'extend_resident_lease',
                        'get_resident_lease_info',
                        'confirm_extension'
                    ]
                    
                    for feature in features_to_check:
                        if feature in content:
                            print(f"      ✅ {feature} 功能已添加")
                        else:
                            print(f"      ❌ {feature} 功能缺失")
                            return False
                
                elif file_path.endswith('api_client.py'):
                    # 檢查 API 客戶端是否添加了展期方法
                    methods_to_check = [
                        'extend_resident_lease',
                        'get_resident_lease_info'
                    ]
                    
                    for method in methods_to_check:
                        if method in content:
                            print(f"      ✅ {method} 方法已添加")
                        else:
                            print(f"      ❌ {method} 方法缺失")
                            return False
            else:
                print(f"   ❌ {file_path} 不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 前端整合測試錯誤: {e}")
        return False

if __name__ == "__main__":
    print("🧪 住戶租期展期功能測試（使用現有住戶）")
    print("=" * 70)
    
    # 1. 獲取現有住戶
    test_result = get_existing_resident()
    
    if test_result:
        resident_id, token, original_resident = test_result
        original_lease_end = original_resident.get('lease_end_date')
        
        # 2. 測試展期 API
        extension_test, final_lease_end = test_lease_extension_api(resident_id, token, original_resident)
        
        # 3. 恢復原始租約到期日
        restore_success = restore_original_lease_date(resident_id, token, original_lease_end)
        
    else:
        extension_test = False
        restore_success = False
    
    # 4. 測試前端整合
    frontend_test = test_frontend_integration()
    
    print("\n" + "=" * 70)
    print("📝 測試結果總結:")
    print("1. ✅ 租期展期 API" if extension_test else "1. ❌ 租期展期 API")
    print("2. ✅ 原始數據恢復" if restore_success else "2. ❌ 原始數據恢復")
    print("3. ✅ 前端整合" if frontend_test else "3. ❌ 前端整合")
    
    all_passed = all([extension_test, restore_success, frontend_test])
    
    print("\n💡 功能特點:")
    print("📅 租期展期功能:")
    print("   - ✅ 延長現有住戶的租約到期日")
    print("   - ✅ 提供快速展期選項（3個月、6個月、1年、2年）")
    print("   - ✅ 支援自定義展期日期")
    print("   - ✅ 顯示展期前後對比")
    print("   - ✅ 記錄展期原因和操作歷史")
    print("   - ✅ 使用民國年日曆選擇器")
    
    print("\n🔒 驗證邏輯:")
    print("   - ✅ 新租約到期日必須晚於原到期日")
    print("   - ✅ 新租約到期日必須晚於今天")
    print("   - ✅ 僅允許活躍住戶進行展期")
    print("   - ✅ 提供詳細的錯誤訊息和確認對話框")
    
    print("\n🎯 使用方式:")
    print("1. 前往「住戶管理」→「住戶列表」")
    print("2. 點擊住戶查看詳情")
    print("3. 在「租期展期」區域查看租約狀態")
    print("4. 點擊「租期展期」按鈕")
    print("5. 選擇快速展期選項或使用民國年日曆選擇自定義日期")
    print("6. 填寫展期原因（選填）")
    print("7. 確認展期資訊並提交")
    
    print(f"\n🏁 測試完成 - {'全部通過' if all_passed else '部分失敗'}")
    
    if all_passed:
        print("\n🎉 住戶租期展期功能已成功實作並測試通過！")
        print("   現在可以在住戶管理頁面使用租期展期功能。")
