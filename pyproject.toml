[project]
name = "rental-management-system"
version = "1.0.0"
description = "一個簡潔易維護的租房管理系統"
authors = [
    {name = "Rental Management Team"}
]
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    # 後端依賴
    "fastapi==0.104.1",
    "uvicorn==0.24.0",
    "sqlalchemy==2.0.23",
    "pydantic==2.4.2",
    "python-jose[cryptography]==3.3.0",
    "passlib[bcrypt]==1.7.4",
    "python-multipart==0.0.6",
    "python-dotenv==1.0.0",
    
    # 前端依賴
    "streamlit==1.28.1",
    "requests==2.31.0",
    "pandas==2.1.1",
    "plotly==5.17.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "httpx>=0.24.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["backend", "frontend"]

[tool.uv]
dev-dependencies = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "httpx>=0.24.0",
]
