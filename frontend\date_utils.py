"""
民國年日期轉換工具
"""

from datetime import datetime, date
from typing import Union, Optional
import streamlit as st
import calendar


def ad_to_roc_year(ad_year: int) -> int:
    """西元年轉民國年"""
    return ad_year - 1911


def roc_to_ad_year(roc_year: int) -> int:
    """民國年轉西元年"""
    return roc_year + 1911


def format_date_roc(date_input: Union[str, datetime, date, None], 
                   format_type: str = "full") -> str:
    """
    將日期格式化為民國年顯示
    
    Args:
        date_input: 日期輸入（可以是字符串、datetime或date對象）
        format_type: 格式類型
            - "full": 民國XXX年X月X日
            - "short": XXX/X/X
            - "year_month": 民國XXX年X月
    
    Returns:
        格式化後的民國年日期字符串
    """
    if not date_input:
        return "N/A"
    
    try:
        # 解析日期
        if isinstance(date_input, str):
            if 'T' in date_input:
                # ISO datetime格式
                parsed_date = datetime.fromisoformat(date_input.replace('Z', '+00:00')).date()
            else:
                # 日期格式
                parsed_date = datetime.strptime(date_input, '%Y-%m-%d').date()
        elif isinstance(date_input, datetime):
            parsed_date = date_input.date()
        elif isinstance(date_input, date):
            parsed_date = date_input
        else:
            return "N/A"
        
        # 轉換為民國年
        roc_year = ad_to_roc_year(parsed_date.year)
        month = parsed_date.month
        day = parsed_date.day
        
        # 根據格式類型返回
        if format_type == "full":
            return f"民國{roc_year}年{month}月{day}日"
        elif format_type == "short":
            return f"{roc_year}/{month}/{day}"
        elif format_type == "year_month":
            return f"民國{roc_year}年{month}月"
        else:
            return f"民國{roc_year}年{month}月{day}日"
    
    except Exception as e:
        return "N/A"


def parse_roc_date(roc_date_str: str) -> Optional[date]:
    """
    解析民國年日期字符串為date對象
    
    Args:
        roc_date_str: 民國年日期字符串（如：民國114年7月20日 或 114/7/20）
    
    Returns:
        date對象，解析失敗返回None
    """
    try:
        if "民國" in roc_date_str:
            # 解析 "民國XXX年X月X日" 格式
            import re
            match = re.match(r'民國(\d+)年(\d+)月(\d+)日', roc_date_str)
            if match:
                roc_year = int(match.group(1))
                month = int(match.group(2))
                day = int(match.group(3))
                ad_year = roc_to_ad_year(roc_year)
                return date(ad_year, month, day)
        elif "/" in roc_date_str:
            # 解析 "XXX/X/X" 格式
            parts = roc_date_str.split("/")
            if len(parts) == 3:
                roc_year = int(parts[0])
                month = int(parts[1])
                day = int(parts[2])
                ad_year = roc_to_ad_year(roc_year)
                return date(ad_year, month, day)
        
        return None
    except Exception:
        return None


def get_current_roc_year() -> int:
    """獲取當前民國年"""
    return ad_to_roc_year(datetime.now().year)


def get_current_roc_date() -> str:
    """獲取當前民國年日期（完整格式）"""
    return format_date_roc(date.today(), "full")


def roc_date_input(label: str,
                  value: Optional[date] = None,
                  min_value: Optional[date] = None,
                  max_value: Optional[date] = None,
                  key: Optional[str] = None,
                  help: Optional[str] = None) -> Optional[date]:
    """
    民國年日期選擇器

    Args:
        label: 標籤
        value: 預設值
        min_value: 最小值
        max_value: 最大值
        key: 唯一鍵
        help: 幫助文字

    Returns:
        選擇的日期（date對象）
    """
    # 使用原始標籤，不添加任何提示文字
    display_label = label

    # 使用原始幫助文字，不添加民國年相關提示
    help_text = help

    selected_date = st.date_input(
        label=display_label,
        value=value,
        min_value=min_value,
        max_value=max_value,
        key=key,
        help=help_text
    )

    return selected_date


def roc_calendar_input(label: str,
                      value: Optional[date] = None,
                      min_value: Optional[date] = None,
                      max_value: Optional[date] = None,
                      key: Optional[str] = None,
                      help: Optional[str] = None) -> Optional[date]:
    """
    民國年日曆選擇器（使用下拉選單方式）

    Args:
        label: 標籤
        value: 預設值
        min_value: 最小值
        max_value: 最大值
        key: 唯一鍵
        help: 幫助文字

    Returns:
        選擇的日期（date對象）
    """
    # 設定預設值
    if value is None:
        value = date.today()

    # 轉換為民國年
    current_roc_year = ad_to_roc_year(value.year)
    current_month = value.month
    current_day = value.day

    # 設定年份範圍
    if min_value:
        min_roc_year = ad_to_roc_year(min_value.year)
    else:
        min_roc_year = current_roc_year - 10

    if max_value:
        max_roc_year = ad_to_roc_year(max_value.year)
    else:
        max_roc_year = current_roc_year + 5

    # 創建唯一的 key，確保每個組件都有唯一標識
    import hashlib

    # 如果沒有提供 key，使用標籤和調用位置生成穩定的 key
    if not key:
        # 使用標籤和函數調用的堆疊信息生成穩定的 key
        import inspect
        frame = inspect.currentframe()
        caller_info = ""
        if frame and frame.f_back:
            caller_info = f"{frame.f_back.f_code.co_filename}:{frame.f_back.f_lineno}"

        # 生成基於標籤和調用位置的穩定 key
        key_source = f"{label}_{caller_info}"
        base_key = f"roc_cal_{hashlib.md5(key_source.encode()).hexdigest()[:8]}"
    else:
        base_key = key

    year_key = f"{base_key}_year"
    month_key = f"{base_key}_month"
    day_key = f"{base_key}_day"

    st.markdown(f"**{label}**")
    if help:
        st.caption(help)

    col1, col2, col3 = st.columns([2, 1, 1])

    with col1:
        # 民國年選擇
        roc_years = list(range(min_roc_year, max_roc_year + 1))
        try:
            year_index = roc_years.index(current_roc_year)
        except ValueError:
            year_index = 0
            current_roc_year = roc_years[0]

        selected_roc_year = st.selectbox(
            "民國年",
            options=roc_years,
            index=year_index,
            format_func=lambda x: f"民國{x}年",
            key=year_key
        )

    with col2:
        # 月份選擇
        months = list(range(1, 13))
        try:
            month_index = months.index(current_month)
        except ValueError:
            month_index = 0
            current_month = months[0]

        selected_month = st.selectbox(
            "月份",
            options=months,
            index=month_index,
            format_func=lambda x: f"{x}月",
            key=month_key
        )

    with col3:
        # 日期選擇
        selected_ad_year = roc_to_ad_year(selected_roc_year)
        max_day = calendar.monthrange(selected_ad_year, selected_month)[1]
        days = list(range(1, max_day + 1))

        # 確保當前日期在有效範圍內
        if current_day > max_day:
            current_day = max_day

        try:
            day_index = days.index(current_day)
        except ValueError:
            day_index = 0
            current_day = days[0]

        selected_day = st.selectbox(
            "日期",
            options=days,
            index=day_index,
            format_func=lambda x: f"{x}日",
            key=day_key
        )

    # 組合選擇的日期
    try:
        selected_date = date(selected_ad_year, selected_month, selected_day)

        # 檢查日期範圍
        if min_value and selected_date < min_value:
            st.error(f"選擇的日期不能早於 {format_date_roc(min_value, 'full')}")
            return min_value
        if max_value and selected_date > max_value:
            st.error(f"選擇的日期不能晚於 {format_date_roc(max_value, 'full')}")
            return max_value

        # 顯示選擇的日期（民國年格式）
        roc_display = format_date_roc(selected_date, 'full')
        st.info(f"📅 選擇日期：{roc_display}")

        return selected_date

    except ValueError as e:
        st.error(f"日期無效：{e}")
        return value


def validate_roc_date_range(start_date: date, end_date: date) -> bool:
    """
    驗證民國年日期範圍
    
    Args:
        start_date: 開始日期
        end_date: 結束日期
    
    Returns:
        是否有效
    """
    return start_date <= end_date


def get_roc_year_options(start_year: Optional[int] = None, 
                        end_year: Optional[int] = None) -> list:
    """
    獲取民國年選項列表
    
    Args:
        start_year: 開始年份（民國年）
        end_year: 結束年份（民國年）
    
    Returns:
        民國年選項列表
    """
    current_roc_year = get_current_roc_year()
    
    if start_year is None:
        start_year = current_roc_year - 10
    if end_year is None:
        end_year = current_roc_year + 5
    
    return list(range(start_year, end_year + 1))


def format_date_for_api(date_input: Union[date, datetime]) -> str:
    """
    將日期格式化為API需要的格式（西元年ISO格式）
    
    Args:
        date_input: 日期輸入
    
    Returns:
        ISO格式的日期字符串
    """
    if isinstance(date_input, datetime):
        return date_input.date().isoformat()
    elif isinstance(date_input, date):
        return date_input.isoformat()
    else:
        return str(date_input)


def create_roc_date_display(date_value: Union[str, datetime, date, None],
                           show_weekday: bool = False) -> str:
    """
    創建民國年日期顯示（帶樣式）
    
    Args:
        date_value: 日期值
        show_weekday: 是否顯示星期
    
    Returns:
        格式化的顯示字符串
    """
    if not date_value:
        return "📅 未設定"
    
    try:
        # 解析日期
        if isinstance(date_value, str):
            if 'T' in date_value:
                parsed_date = datetime.fromisoformat(date_value.replace('Z', '+00:00')).date()
            else:
                parsed_date = datetime.strptime(date_value, '%Y-%m-%d').date()
        elif isinstance(date_value, datetime):
            parsed_date = date_value.date()
        elif isinstance(date_value, date):
            parsed_date = date_value
        else:
            return "📅 格式錯誤"
        
        # 基本格式
        roc_format = format_date_roc(parsed_date, "full")
        
        # 添加星期
        if show_weekday:
            weekdays = ["一", "二", "三", "四", "五", "六", "日"]
            weekday = weekdays[parsed_date.weekday()]
            return f"📅 {roc_format} (星期{weekday})"
        else:
            return f"📅 {roc_format}"
    
    except Exception:
        return "📅 格式錯誤"
