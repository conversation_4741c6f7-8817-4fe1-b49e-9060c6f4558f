#!/usr/bin/env python3
"""
使用curl進行完整的費率更新API測試
"""

import subprocess
import json
import time

BASE_URL = "http://localhost:8080"

def run_curl(command):
    """執行curl命令並返回結果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
        return result.returncode, result.stdout, result.stderr
    except Exception as e:
        return -1, "", str(e)

def test_api_connection():
    """測試API連接"""
    print("🧪 步驟1：測試API連接")
    print("=" * 50)
    
    cmd = f'curl -s -o /dev/null -w "%{{http_code}}" {BASE_URL}/'
    returncode, stdout, stderr = run_curl(cmd)
    
    print(f"執行命令: {cmd}")
    print(f"HTTP狀態碼: {stdout}")
    
    if stdout == "200":
        print("✅ API連接正常")
        return True
    else:
        print(f"❌ API連接失敗: {stdout}")
        return False

def test_login():
    """測試登入並獲取token"""
    print("\n🧪 步驟2：用戶登入")
    print("=" * 50)
    
    cmd = f'''curl -s -X POST "{BASE_URL}/auth/login" \
-H "Content-Type: application/x-www-form-urlencoded" \
-d "username=admin&password=admin123"'''
    
    print(f"執行命令: {cmd}")
    returncode, stdout, stderr = run_curl(cmd)
    
    print(f"回應: {stdout}")
    if stderr:
        print(f"錯誤: {stderr}")
    
    try:
        response = json.loads(stdout)
        if "access_token" in response:
            token = response["access_token"]
            print("✅ 登入成功")
            print(f"Token: {token[:20]}...")
            return token
        else:
            print("❌ 登入失敗：沒有獲得token")
            return None
    except json.JSONDecodeError:
        print("❌ 登入失敗：回應不是有效的JSON")
        return None

def test_get_rates(token):
    """獲取現有費率"""
    print("\n🧪 步驟3：獲取現有費率")
    print("=" * 50)
    
    cmd = f'''curl -s -X GET "{BASE_URL}/utilities/rates" \
-H "Authorization: Bearer {token}" \
-H "Content-Type: application/json"'''
    
    print(f"執行命令: {cmd}")
    returncode, stdout, stderr = run_curl(cmd)
    
    print(f"回應: {stdout}")
    if stderr:
        print(f"錯誤: {stderr}")
    
    try:
        rates = json.loads(stdout)
        if isinstance(rates, list):
            print(f"✅ 獲取到 {len(rates)} 個費率")
            for rate in rates:
                print(f"  - ID: {rate.get('id')}, 電費: ${rate.get('electricity_rate')}, 水費: ${rate.get('monthly_water_fee')}")
            return rates
        else:
            print("❌ 獲取費率失敗")
            return []
    except json.JSONDecodeError:
        print("❌ 獲取費率失敗：回應不是有效的JSON")
        return []

def test_create_rate(token):
    """創建測試費率"""
    print("\n🧪 步驟4：創建測試費率")
    print("=" * 50)
    
    rate_data = {
        "electricity_rate": 6.5,
        "monthly_water_fee": 320.0,
        "effective_date": "2025-07-19",
        "notes": "curl測試費率 - 原始版本"
    }
    
    cmd = f'''curl -s -X POST "{BASE_URL}/utilities/rates" \
-H "Authorization: Bearer {token}" \
-H "Content-Type: application/json" \
-d '{json.dumps(rate_data)}\''''
    
    print(f"創建費率資料: {json.dumps(rate_data, indent=2, ensure_ascii=False)}")
    print(f"執行命令: {cmd}")
    
    returncode, stdout, stderr = run_curl(cmd)
    
    print(f"回應: {stdout}")
    if stderr:
        print(f"錯誤: {stderr}")
    
    try:
        response = json.loads(stdout)
        if "id" in response:
            print(f"✅ 費率創建成功，ID: {response['id']}")
            return response
        else:
            print("❌ 費率創建失敗")
            return None
    except json.JSONDecodeError:
        print("❌ 費率創建失敗：回應不是有效的JSON")
        return None

def test_update_rate(token, rate_id):
    """測試更新費率 - 這是關鍵測試"""
    print(f"\n🧪 步驟5：更新費率 ID {rate_id}")
    print("=" * 50)
    
    update_data = {
        "electricity_rate": 8.5,
        "monthly_water_fee": 450.0,
        "notes": "curl測試費率 - 已通過API更新"
    }
    
    cmd = f'''curl -s -X PUT "{BASE_URL}/utilities/rates/{rate_id}" \
-H "Authorization: Bearer {token}" \
-H "Content-Type: application/json" \
-d '{json.dumps(update_data)}\''''
    
    print(f"更新資料: {json.dumps(update_data, indent=2, ensure_ascii=False)}")
    print(f"執行命令: {cmd}")
    
    returncode, stdout, stderr = run_curl(cmd)
    
    print(f"回應: {stdout}")
    if stderr:
        print(f"錯誤: {stderr}")
    
    try:
        response = json.loads(stdout)
        if "id" in response:
            print("✅ 費率更新API調用成功")
            print(f"  回應中電費: ${response.get('electricity_rate')}")
            print(f"  回應中水費: ${response.get('monthly_water_fee')}")
            print(f"  回應中備註: {response.get('notes')}")
            return response
        else:
            print("❌ 費率更新失敗")
            return None
    except json.JSONDecodeError:
        print("❌ 費率更新失敗：回應不是有效的JSON")
        return None

def test_verify_update(token, rate_id):
    """驗證更新是否真的生效"""
    print(f"\n🧪 步驟6：驗證更新是否生效")
    print("=" * 50)
    
    cmd = f'''curl -s -X GET "{BASE_URL}/utilities/rates/{rate_id}" \
-H "Authorization: Bearer {token}" \
-H "Content-Type: application/json"'''
    
    print(f"執行命令: {cmd}")
    returncode, stdout, stderr = run_curl(cmd)
    
    print(f"回應: {stdout}")
    if stderr:
        print(f"錯誤: {stderr}")
    
    try:
        response = json.loads(stdout)
        if "id" in response:
            print("✅ 成功獲取更新後的費率")
            print(f"  資料庫中電費: ${response.get('electricity_rate')}")
            print(f"  資料庫中水費: ${response.get('monthly_water_fee')}")
            print(f"  資料庫中備註: {response.get('notes')}")
            
            # 檢查是否正確更新
            if (response.get('electricity_rate') == 8.5 and 
                response.get('monthly_water_fee') == 450.0 and
                "已通過API更新" in response.get('notes', '')):
                print("\n🎉 費率更新完全成功！")
                print("✅ API回應正確")
                print("✅ 資料庫更新正確")
                print("✅ 資料持久化正常")
                return True
            else:
                print("\n❌ 費率更新有問題！")
                print("❌ 資料庫中的資料與預期不符")
                print(f"期望電費: 8.5, 實際: {response.get('electricity_rate')}")
                print(f"期望水費: 450.0, 實際: {response.get('monthly_water_fee')}")
                return False
        else:
            print("❌ 驗證失敗：無法獲取費率")
            return False
    except json.JSONDecodeError:
        print("❌ 驗證失敗：回應不是有效的JSON")
        return False

def test_cleanup(token, rate_id):
    """清理測試資料"""
    print(f"\n🧪 步驟7：清理測試資料")
    print("=" * 50)
    
    cmd = f'''curl -s -X DELETE "{BASE_URL}/utilities/rates/{rate_id}" \
-H "Authorization: Bearer {token}" \
-H "Content-Type: application/json"'''
    
    print(f"執行命令: {cmd}")
    returncode, stdout, stderr = run_curl(cmd)
    
    print(f"回應: {stdout}")
    if stderr:
        print(f"錯誤: {stderr}")
    
    try:
        response = json.loads(stdout)
        if "message" in response or "detail" in response:
            print("✅ 測試資料清理成功")
            return True
        else:
            print("⚠️ 清理可能失敗，但不影響測試結果")
            return False
    except json.JSONDecodeError:
        print("⚠️ 清理回應不是JSON，但可能已成功")
        return False

def main():
    """主測試函數"""
    print("🧪 使用curl進行完整的費率更新API測試")
    print("=" * 70)
    
    # 1. 測試API連接
    if not test_api_connection():
        print("❌ API服務不可用，測試終止")
        return
    
    # 2. 登入獲取token
    token = test_login()
    if not token:
        print("❌ 無法獲取認證token，測試終止")
        return
    
    # 3. 獲取現有費率
    existing_rates = test_get_rates(token)
    
    # 4. 創建測試費率
    test_rate = test_create_rate(token)
    if not test_rate:
        print("❌ 無法創建測試費率，測試終止")
        return
    
    rate_id = test_rate['id']
    
    # 5. 測試更新費率（關鍵測試）
    updated_rate = test_update_rate(token, rate_id)
    
    # 6. 驗證更新結果
    if updated_rate:
        success = test_verify_update(token, rate_id)
    else:
        success = False
    
    # 7. 清理測試資料
    test_cleanup(token, rate_id)
    
    print("\n" + "=" * 70)
    print("📊 最終測試結果:")
    if success:
        print("🎉 費率更新API完全正常運作！")
        print("✅ 所有curl測試都通過")
        print("✅ API端點正確響應")
        print("✅ 資料庫正確更新")
        print("✅ 資料持久化正常")
    else:
        print("❌ 費率更新API存在問題")
        print("❌ 需要進一步檢查後端實作")
    
    return success

if __name__ == "__main__":
    main()
