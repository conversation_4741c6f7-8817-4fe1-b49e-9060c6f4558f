#!/usr/bin/env python3
"""測試民國年日曆選擇器和數據處理修正"""

import requests
import sys
import os
from datetime import date

def test_data_processing_api():
    """測試數據處理 API"""
    print("📊 測試數據處理修正...")
    
    base_url = 'http://localhost:8080'
    login_data = {
        'username': 'admin',
        'password': 'admin5813'
    }
    
    try:
        # 登入
        response = requests.post(f'{base_url}/auth/login', data=login_data)
        if response.status_code != 200:
            print(f'❌ 登入失敗: {response.status_code}')
            return False
            
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        # 測試案例
        test_cases = [
            {
                'name': '測試 N/A 面積處理',
                'data': {
                    "room_number": "TEST_NA_AREA",
                    "floor": 1,
                    "area": "N/A",
                    "rent_single": 8000,
                    "rent_double": 12000,
                    "description": "測試 N/A 面積"
                },
                'expected_area': 0.0
            },
            {
                'name': '測試空字符串面積處理',
                'data': {
                    "room_number": "TEST_EMPTY_AREA",
                    "floor": 1,
                    "area": "",
                    "rent_single": 8000,
                    "rent_double": 12000,
                    "description": "測試空字符串面積"
                },
                'expected_area': None
            },
            {
                'name': '測試無效字符串面積處理',
                'data': {
                    "room_number": "TEST_INVALID_AREA",
                    "floor": 1,
                    "area": "invalid_string",
                    "rent_single": 8000,
                    "rent_double": 12000,
                    "description": "測試無效字符串面積"
                },
                'expected_area': None
            },
            {
                'name': '測試 N/A 租金處理',
                'data': {
                    "room_number": "TEST_NA_RENT",
                    "floor": 1,
                    "area": 10.0,
                    "rent_single": "N/A",
                    "rent_double": "N/A",
                    "description": "測試 N/A 租金"
                },
                'expected_rent_single': 0.0,
                'expected_rent_double': 0.0
            }
        ]
        
        created_rooms = []
        
        for test_case in test_cases:
            print(f"   {test_case['name']}...")
            
            response = requests.post(f'{base_url}/rooms/', json=test_case['data'], headers=headers)
            
            if response.status_code == 200:
                room = response.json()
                created_rooms.append(room['id'])
                
                # 驗證面積處理
                if 'expected_area' in test_case:
                    actual_area = room.get('area')
                    expected_area = test_case['expected_area']
                    if actual_area == expected_area:
                        print(f"      ✅ 面積處理正確: {actual_area}")
                    else:
                        print(f"      ❌ 面積處理錯誤: 期望 {expected_area}, 實際 {actual_area}")
                
                # 驗證租金處理
                if 'expected_rent_single' in test_case:
                    actual_rent_single = room.get('rent_single')
                    expected_rent_single = test_case['expected_rent_single']
                    if actual_rent_single == expected_rent_single:
                        print(f"      ✅ 單人租金處理正確: {actual_rent_single}")
                    else:
                        print(f"      ❌ 單人租金處理錯誤: 期望 {expected_rent_single}, 實際 {actual_rent_single}")
                
                if 'expected_rent_double' in test_case:
                    actual_rent_double = room.get('rent_double')
                    expected_rent_double = test_case['expected_rent_double']
                    if actual_rent_double == expected_rent_double:
                        print(f"      ✅ 雙人租金處理正確: {actual_rent_double}")
                    else:
                        print(f"      ❌ 雙人租金處理錯誤: 期望 {expected_rent_double}, 實際 {actual_rent_double}")
                        
            else:
                print(f"      ❌ 創建失敗: {response.status_code} - {response.text}")
        
        # 清理測試房間
        print("   清理測試房間...")
        for room_id in created_rooms:
            requests.delete(f'{base_url}/rooms/{room_id}', headers=headers)
        
        return True
        
    except Exception as e:
        print(f"❌ 數據處理測試錯誤: {e}")
        return False

def test_calendar_function():
    """測試民國年日曆選擇器函數"""
    print("\n📅 測試民國年日曆選擇器...")
    
    try:
        # 檢查前端文件修正
        sys.path.append('frontend')
        from date_utils import roc_calendar_input, ad_to_roc_year, roc_to_ad_year, format_date_roc
        
        print("   ✅ 日期工具函數可以正常導入")
        
        # 測試日期轉換函數
        test_date = date(2025, 7, 20)
        roc_year = ad_to_roc_year(test_date.year)
        ad_year = roc_to_ad_year(roc_year)
        formatted_date = format_date_roc(test_date, 'full')
        
        print(f"   ✅ 日期轉換測試:")
        print(f"      西元 {test_date.year} 年 → 民國 {roc_year} 年")
        print(f"      民國 {roc_year} 年 → 西元 {ad_year} 年")
        print(f"      格式化日期: {formatted_date}")
        
        # 檢查 key 生成邏輯修正
        date_utils_file = 'frontend/date_utils.py'
        if os.path.exists(date_utils_file):
            with open(date_utils_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 檢查是否修正了 key 生成邏輯
            if 'hashlib.md5' in content and 'inspect.currentframe' in content:
                print("   ✅ key 生成邏輯已修正為穩定方式")
            else:
                print("   ❌ key 生成邏輯未修正")
                return False
            
            # 檢查是否移除了隨機數生成
            if 'random.randint' not in content or 'time.time()' not in content:
                print("   ✅ 已移除不穩定的隨機 key 生成")
            else:
                print("   ⚠️  仍包含隨機 key 生成邏輯")
        
        return True
        
    except Exception as e:
        print(f"❌ 日曆選擇器測試錯誤: {e}")
        return False

def test_frontend_calendar_usage():
    """測試前端頁面中的日曆選擇器使用"""
    print("\n🖥️ 測試前端頁面日曆選擇器使用...")
    
    try:
        # 檢查各個前端頁面是否正確使用了 key 參數
        pages_to_check = [
            'frontend/pages/residents.py',
            'frontend/pages/utilities.py'
        ]
        
        for page_file in pages_to_check:
            if os.path.exists(page_file):
                with open(page_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 檢查 roc_calendar_input 調用是否有 key 參數
                import re
                roc_calls = re.findall(r'roc_calendar_input\([^)]+\)', content)
                
                print(f"   檢查 {page_file}:")
                for call in roc_calls:
                    if 'key=' in call:
                        print(f"      ✅ 找到帶 key 的調用: {call[:50]}...")
                    else:
                        print(f"      ⚠️  找到不帶 key 的調用: {call[:50]}...")
            else:
                print(f"   ❌ {page_file} 不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 前端頁面測試錯誤: {e}")
        return False

def test_backend_validation():
    """測試後端驗證邏輯"""
    print("\n🔧 測試後端驗證邏輯...")
    
    try:
        # 檢查後端文件修正
        rooms_file = 'backend/app/routers/rooms.py'
        
        if os.path.exists(rooms_file):
            with open(rooms_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 檢查是否添加了數據處理邏輯
            validation_features = [
                'pre=True',  # 預處理驗證器
                'v.strip().upper()',  # 字符串處理
                "['N/A', 'NA']",  # N/A 處理
                'return None',  # 返回 None
                'ValueError',  # 錯誤處理
            ]
            
            for feature in validation_features:
                if feature in content:
                    print(f"   ✅ {feature} 邏輯已添加")
                else:
                    print(f"   ❌ {feature} 邏輯缺失")
                    return False
            
            return True
        else:
            print(f"   ❌ {rooms_file} 不存在")
            return False
            
    except Exception as e:
        print(f"❌ 後端驗證測試錯誤: {e}")
        return False

if __name__ == "__main__":
    print("🧪 民國年日曆選擇器和數據處理修正測試")
    print("=" * 70)
    
    # 測試日曆選擇器修正
    calendar_test = test_calendar_function()
    
    # 測試前端頁面使用
    frontend_test = test_frontend_calendar_usage()
    
    # 測試後端驗證邏輯
    backend_test = test_backend_validation()
    
    # 測試數據處理 API
    data_api_test = test_data_processing_api()
    
    print("\n" + "=" * 70)
    print("📝 測試結果總結:")
    print("1. ✅ 民國年日曆選擇器修正" if calendar_test else "1. ❌ 民國年日曆選擇器修正")
    print("2. ✅ 前端頁面日曆使用" if frontend_test else "2. ❌ 前端頁面日曆使用")
    print("3. ✅ 後端驗證邏輯" if backend_test else "3. ❌ 後端驗證邏輯")
    print("4. ✅ 數據處理 API" if data_api_test else "4. ❌ 數據處理 API")
    
    all_passed = all([calendar_test, frontend_test, backend_test, data_api_test])
    
    print("\n💡 修正說明:")
    print("🔧 民國年日曆選擇器修正:")
    print("   - 修正 key 生成邏輯，使用穩定的基於標籤和調用位置的 key")
    print("   - 移除隨機數生成，避免每次重新渲染時 key 改變")
    print("   - 確保組件狀態在頁面重新渲染時保持穩定")
    
    print("\n📊 數據處理邏輯修正:")
    print("   - 'N/A' 字符串 → 數字 0")
    print("   - 空字符串 → None (null)")
    print("   - 其他非數字字符串 → None (null)")
    print("   - 應用於面積、租金等所有數字欄位")
    
    print("\n🎯 使用建議:")
    print("1. 重新啟動前端應用以載入修正")
    print("2. 測試住戶管理頁面的日期選擇器")
    print("3. 測試費用管理頁面的日期選擇器")
    print("4. 測試房間創建/編輯時的數據處理")
    
    print(f"\n🏁 測試完成 - {'全部通過' if all_passed else '部分失敗'}")
