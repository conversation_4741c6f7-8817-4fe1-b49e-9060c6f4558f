# =================== frontend/app.py ===================
import streamlit as st
from config import config
from utils import init_session_state, logout_user
from pages.login import show_login_page
from pages.dashboard import show_dashboard_page
from pages.rooms import show_rooms_page
from pages.residents import show_residents_page
from pages.utilities import show_utilities_page
from pages.reports import show_reports_page

# 頁面配置
st.set_page_config(**config.PAGE_CONFIG)

# 初始化會話狀態
init_session_state()

# 自定義CSS樣式
st.markdown("""
<style>
    /* 隱藏Streamlit預設元素 */
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}
    
    /* 自定義側邊欄樣式 */
    .css-1d391kg {
        padding-top: 1rem;
    }
    
    /* 自定義按鈕樣式 */
    .stButton > button {
        width: 100%;
        border-radius: 20px;
        border: none;
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    /* 自定義指標卡片 */
    [data-testid="metric-container"] {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    /* 自定義表格樣式 */
    .stDataFrame {
        border-radius: 10px;
        overflow: hidden;
    }
    
    /* 自定義標題樣式 */
    h1 {
        color: #2c3e50;
        border-bottom: 3px solid #3498db;
        padding-bottom: 0.5rem;
    }
    
    h2 {
        color: #34495e;
    }
    
    h3 {
        color: #7f8c8d;
    }
    
    /* 自定義訊息框樣式 */
    .stAlert {
        border-radius: 10px;
    }
    
    /* 自定義表單樣式 */
    .stForm {
        border: 1px solid #dee2e6;
        border-radius: 10px;
        padding: 1.5rem;
        background-color: #ffffff;
    }
</style>
""", unsafe_allow_html=True)

def show_sidebar():
    """顯示側邊欄導航"""
    with st.sidebar:
        # 系統標題和圖示
        st.markdown(f"""
        <div style="text-align: center; padding: 1rem;">
            <h1 style="color: #2c3e50; margin: 0; font-size: 1.8rem;">
                {config.APP_ICON} {config.APP_TITLE}
            </h1>
            <p style="color: #7f8c8d; margin: 0.5rem 0; font-size: 0.9rem;">
                現代化租房管理解決方案
            </p>
        </div>
        """, unsafe_allow_html=True)
        
        st.markdown("---")
        
        if st.session_state.authenticated:
            # 用戶資訊區域
            user_info = st.session_state.user_info
            
            st.markdown(f"""
            <div style="background-color: #e8f5e8; padding: 1rem; border-radius: 10px; margin-bottom: 1rem;">
                <div style="display: flex; align-items: center;">
                    <div style="background-color: #28a745; color: white; width: 40px; height: 40px; 
                                border-radius: 50%; display: flex; align-items: center; justify-content: center; 
                                margin-right: 10px; font-weight: bold;">
                        {user_info['username'][0].upper()}
                    </div>
                    <div>
                        <div style="font-weight: bold; color: #155724;">
                            {user_info['username']}
                        </div>
                        <div style="font-size: 0.8rem; color: #6c757d;">
                            {get_role_display(user_info['role'])}
                        </div>
                    </div>
                </div>
            </div>
            """, unsafe_allow_html=True)
            
            # 導航選單
            st.markdown("### 📋 系統功能")
            
            # 功能頁面按鈕
            navigation_buttons = [
                ("📊 系統儀表板", "dashboard", "📊"),
                ("🏠 房間管理", "rooms", "🏠"),
                ("👥 住戶管理", "residents", "👥"),
                ("💰 費用管理", "utilities", "💰"),
                ("📈 報表統計", "reports", "📈")
            ]
            
            # 設定預設頁面
            if 'page' not in st.session_state:
                st.session_state.page = "dashboard"
            
            # 創建導航按鈕
            for button_text, page_key, icon in navigation_buttons:
                # 檢查是否為當前頁面
                is_current = st.session_state.page == page_key
                
                # 設定按鈕樣式
                button_style = """
                <style>
                .current-page {{
                    background-color: #007bff !important;
                    color: white !important;
                    border: 2px solid #007bff !important;
                }}
                </style>
                """ if is_current else ""
                
                st.markdown(button_style, unsafe_allow_html=True)
                
                if st.button(
                    button_text, 
                    key=f"nav_{page_key}",
                    help=f"前往{button_text}頁面"
                ):
                    st.session_state.page = page_key
                    st.rerun()
            
            st.markdown("---")
            
            # 快速操作區域
            st.markdown("### ⚡ 快速操作")
            
            quick_actions = [
                ("➕ 新增房間", "rooms", "快速新增房間"),
                ("👤 新增住戶", "residents", "快速新增住戶"),
                ("📊 電表抄錄", "utilities", "進行電表抄錄"),
                ("💳 付款管理", "utilities", "管理費用付款")
            ]
            
            for action_text, target_page, help_text in quick_actions:
                if st.button(action_text, key=f"quick_{action_text}", help=help_text):
                    st.session_state.page = target_page
                    st.rerun()
            
            st.markdown("---")
            
            # 系統資訊
            with st.expander("ℹ️ 系統資訊"):
                st.markdown(f"""
                **版本**: v1.0.0  
                **狀態**: 🟢 運行中  
                **API**: 🟢 已連接  
                **用戶**: {user_info['username']}  
                **角色**: {get_role_display(user_info['role'])}  
                **登入時間**: {get_login_time()}
                """)
                
                # 系統統計
                try:
                    dashboard_stats = get_quick_stats()
                    if dashboard_stats:
                        st.markdown("**系統概況**:")
                        st.text(f"房間總數: {dashboard_stats.get('total_rooms', 'N/A')}")
                        st.text(f"住戶總數: {dashboard_stats.get('total_residents', 'N/A')}")
                        st.text(f"入住率: {dashboard_stats.get('occupancy_rate', 'N/A')}%")
                except:
                    pass
            
            # 登出按鈕
            if st.button("🚪 安全登出", help="登出系統"):
                logout_user()
                st.success("已安全登出")
                st.rerun()
            
            return st.session_state.page
        else:
            # 未登入狀態的資訊
            st.markdown("""
            <div style="text-align: center; padding: 2rem 0;">
                <div style="font-size: 3rem; margin-bottom: 1rem;">🔐</div>
                <h3 style="color: #6c757d;">請先登入系統</h3>
                <p style="color: #adb5bd; font-size: 0.9rem;">
                    使用您的帳號登入以存取系統功能
                </p>
            </div>
            """, unsafe_allow_html=True)
            
            # 系統功能預覽
            with st.expander("🎯 系統功能預覽"):
                st.markdown("""
                **核心功能**
                - 🏠 房間管理 - 房間資訊維護
                - 👥 住戶管理 - 入住退房管理
                - 💰 費用管理 - 電費水費計算
                - 📊 統計報表 - 數據分析展示
                
                **特色優勢**
                - 🎨 現代化介面設計
                - 📱 響應式設計支援
                - 🔒 安全認證機制
                - 📈 即時數據更新
                """)
            
            return "login"

def get_role_display(role):
    """獲取角色顯示名稱"""
    role_map = {
        "admin": "系統管理員",
        "manager": "物業管理員",
        "user": "一般使用者"
    }
    return role_map.get(role, role)

def get_login_time():
    """獲取登入時間"""
    if 'login_time' not in st.session_state:
        st.session_state.login_time = "剛剛"
    return st.session_state.login_time

def get_quick_stats():
    """獲取快速統計資料"""
    try:
        from api_client import api_client
        return api_client.get_dashboard_stats()
    except:
        return None

def show_page_header(page_name):
    """顯示頁面標題"""
    page_info = {
        "dashboard": {"title": "系統儀表板", "icon": "📊", "desc": "系統概覽與關鍵指標"},
        "rooms": {"title": "房間管理", "icon": "🏠", "desc": "房間資訊維護與狀態管理"},
        "residents": {"title": "住戶管理", "icon": "👥", "desc": "住戶資料管理與入住退房"},
        "utilities": {"title": "費用管理", "icon": "💰", "desc": "電費水費計算與帳單管理"},
        "reports": {"title": "報表統計", "icon": "📈", "desc": "數據分析與趨勢統計"}
    }
    
    info = page_info.get(page_name, {"title": "系統功能", "icon": "⚙️", "desc": "系統功能頁面"})
    
    st.markdown(f"""
    <div style="background: linear-gradient(90deg, #667eea 0%, #764ba2 100%); 
                padding: 1.5rem; border-radius: 15px; margin-bottom: 2rem; color: white;">
        <h1 style="margin: 0; font-size: 2rem; border: none; padding: 0;">
            {info['icon']} {info['title']}
        </h1>
        <p style="margin: 0.5rem 0 0 0; opacity: 0.9; font-size: 1rem;">
            {info['desc']}
        </p>
    </div>
    """, unsafe_allow_html=True)

def show_error_page():
    """顯示錯誤頁面"""
    st.error("❌ 頁面載入錯誤")
    
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.markdown("""
        <div style="text-align: center; padding: 3rem 0;">
            <div style="font-size: 5rem; margin-bottom: 2rem;">😵</div>
            <h2 style="color: #dc3545;">系統錯誤</h2>
            <p style="color: #6c757d; margin-bottom: 2rem;">
                很抱歉，系統遇到了一些問題。請稍後再試或聯絡系統管理員。
            </p>
        </div>
        """, unsafe_allow_html=True)
        
        if st.button("🔄 重新載入", key="reload_error"):
            st.rerun()
        
        if st.button("🏠 返回首頁", key="home_error"):
            st.session_state.page = "dashboard"
            st.rerun()

def main():
    """主程式"""
    try:
        # 顯示導航
        current_page = show_sidebar()
        
        # 路由處理
        if current_page == "login":
            show_login_page()
        else:
            # 顯示頁面標題
            show_page_header(current_page)
            
            # 路由到對應頁面
            if current_page == "dashboard":
                show_dashboard_page()
            elif current_page == "rooms":
                show_rooms_page()
            elif current_page == "residents":
                show_residents_page()
            elif current_page == "utilities":
                show_utilities_page()
            elif current_page == "reports":
                show_reports_page()
            else:
                st.error(f"頁面 '{current_page}' 不存在")
                show_error_page()
    
    except Exception as e:
        st.error(f"系統錯誤: {str(e)}")
        show_error_page()
    
    # 頁面底部資訊
    if st.session_state.authenticated:
        st.markdown("---")
        st.markdown("""
        <div style="text-align: center; color: #6c757d; font-size: 0.8rem; padding: 1rem 0;">
            <p>© 2024 租房管理系統 | 技術支援: FastAPI + Streamlit | 版本: v1.0.0</p>
        </div>
        """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()

# =================== frontend/components/__init__.py ===================
# 組件包初始化文件

# =================== frontend/components/forms.py ===================
import streamlit as st
from typing import Dict, Any, List, Optional
from datetime import date, datetime

def create_form_field(field_type: str, label: str, key: str, **kwargs) -> Any:
    """創建表單欄位"""
    if field_type == "text":
        return st.text_input(label, key=key, **kwargs)
    elif field_type == "number":
        return st.number_input(label, key=key, **kwargs)
    elif field_type == "date":
        return st.date_input(label, key=key, **kwargs)
    elif field_type == "select":
        return st.selectbox(label, key=key, **kwargs)
    elif field_type == "textarea":
        return st.text_area(label, key=key, **kwargs)
    elif field_type == "checkbox":
        return st.checkbox(label, key=key, **kwargs)
    else:
        return st.text_input(label, key=key, **kwargs)

def create_dynamic_form(form_config: Dict[str, Any], form_key: str) -> Dict[str, Any]:
    """創建動態表單"""
    form_data = {}
    
    with st.form(form_key):
        for field_name, field_config in form_config.items():
            field_data = create_form_field(
                field_config['type'],
                field_config['label'],
                f"{form_key}_{field_name}",
                **field_config.get('options', {})
            )
            form_data[field_name] = field_data
        
        submitted = st.form_submit_button("提交")
        
        if submitted:
            return form_data, True
    
    return form_data, False

# =================== frontend/components/tables.py ===================
import streamlit as st
import pandas as pd
from typing import List, Dict, Any

def display_interactive_table(data: List[Dict[str, Any]], 
                            columns: List[str] = None,
                            search_columns: List[str] = None,
                            sortable: bool = True) -> pd.DataFrame:
    """顯示互動式表格"""
    if not data:
        st.info("暫無數據")
        return pd.DataFrame()
    
    df = pd.DataFrame(data)
    
    if columns:
        df = df[columns]
    
    # 搜尋功能
    if search_columns:
        search_term = st.text_input("🔍 搜尋", key="table_search")
        if search_term:
            mask = df[search_columns].astype(str).apply(
                lambda x: x.str.contains(search_term, case=False, na=False)
            ).any(axis=1)
            df = df[mask]
    
    # 排序功能
    if sortable and len(df) > 0:
        sort_column = st.selectbox("排序欄位", options=df.columns.tolist())
        sort_order = st.selectbox("排序順序", options=["升序", "降序"])
        
        ascending = sort_order == "升序"
        df = df.sort_values(by=sort_column, ascending=ascending)
    
    # 顯示表格
    st.dataframe(df, use_container_width=True)
    
    return df

def create_summary_cards(data: Dict[str, Any], columns: int = 4):
    """創建摘要卡片"""
    cols = st.columns(columns)
    
    for i, (label, value) in enumerate(data.items()):
        with cols[i % columns]:
            st.metric(label, value)

# =================== frontend/components/charts.py ===================
import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from typing import Dict, Any, List

def create_pie_chart(data: Dict[str, float], title: str = "分布圖") -> None:
    """創建餅圖"""
    fig = px.pie(
        values=list(data.values()),
        names=list(data.keys()),
        title=title
    )
    st.plotly_chart(fig, use_container_width=True)

def create_bar_chart(data: Dict[str, float], title: str = "柱狀圖") -> None:
    """創建柱狀圖"""
    fig = px.bar(
        x=list(data.keys()),
        y=list(data.values()),
        title=title
    )
    st.plotly_chart(fig, use_container_width=True)

def create_line_chart(data: List[Dict[str, Any]], x_col: str, y_col: str, title: str = "趨勢圖") -> None:
    """創建折線圖"""
    fig = px.line(
        data,
        x=x_col,
        y=y_col,
        title=title,
        markers=True
    )
    st.plotly_chart(fig, use_container_width=True)

def create_gauge_chart(value: float, title: str = "指標", max_value: float = 100) -> None:
    """創建儀表板圖"""
    fig = go.Figure(go.Indicator(
        mode="gauge+number+delta",
        value=value,
        title={'text': title},
        domain={'x': [0, 1], 'y': [0, 1]},
        gauge={
            'axis': {'range': [None, max_value]},
            'bar': {'color': "darkblue"},
            'steps': [
                {'range': [0, max_value * 0.5], 'color': "lightgray"},
                {'range': [max_value * 0.5, max_value * 0.8], 'color': "yellow"}
            ],
            'threshold': {
                'line': {'color': "red", 'width': 4},
                'thickness': 0.75,
                'value': max_value * 0.9
            }
        }
    ))
    
    st.plotly_chart(fig, use_container_width=True)

# =================== frontend/components/navigation.py ===================
import streamlit as st
from typing import Dict, Any, List, Tuple

def create_breadcrumb(pages: List[Tuple[str, str]]) -> None:
    """創建麵包屑導航"""
    breadcrumb_html = " / ".join([f"<a href='#{page[1]}'>{page[0]}</a>" for page in pages])
    st.markdown(f"**導航**: {breadcrumb_html}", unsafe_allow_html=True)

def create_tab_navigation(tabs: Dict[str, str], current_tab: str = None) -> str:
    """創建頁籤導航"""
    tab_keys = list(tabs.keys())
    tab_labels = list(tabs.values())
    
    if current_tab and current_tab in tab_keys:
        index = tab_keys.index(current_tab)
    else:
        index = 0
    
    selected_tab = st.selectbox("選擇頁籤", tab_keys, index=index, format_func=lambda x: tabs[x])
    return selected_tab

def create_sidebar_navigation(nav_items: Dict[str, Dict[str, Any]]) -> str:
    """創建側邊欄導航"""
    selected_page = None
    
    with st.sidebar:
        st.title("導航選單")
        
        for page_key, page_info in nav_items.items():
            if st.button(
                f"{page_info['icon']} {page_info['title']}",
                key=f"nav_{page_key}",
                help=page_info.get('description', '')
            ):
                selected_page = page_key
    
    return selected_page

def create_quick_actions(actions: List[Dict[str, Any]]) -> str:
    """創建快速操作按鈕"""
    selected_action = None
    
    st.markdown("### ⚡ 快速操作")
    
    cols = st.columns(len(actions))
    
    for i, action in enumerate(actions):
        with cols[i]:
            if st.button(
                action['label'],
                key=f"quick_action_{i}",
                help=action.get('help', ''),
                use_container_width=True
            ):
                selected_action = action['key']
    
    return selected_action