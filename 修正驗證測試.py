#!/usr/bin/env python3
"""驗證修正的測試文件"""

import requests
import sys
import os
from datetime import date, datetime, timedelta

def test_comprehensive_bill_edit_delete():
    """測試綜合帳單編輯和刪除功能"""
    print("🔧 測試綜合帳單編輯和刪除功能...")
    
    # 檢查費用管理頁面是否包含必要的函數
    utilities_file = 'frontend/pages/utilities.py'
    
    if os.path.exists(utilities_file):
        with open(utilities_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"   檢查 {utilities_file}:")
        
        # 檢查是否包含編輯和刪除處理函數
        required_functions = [
            'show_edit_comprehensive_bill_form',
            'show_delete_comprehensive_bill_confirmation',
            'show_edit_bill_form',
            'confirm_delete_comprehensive_bill'
        ]
        
        all_functions_present = True
        for func in required_functions:
            if func in content:
                print(f"      ✅ {func}: 已實作")
            else:
                print(f"      ❌ {func}: 未找到")
                all_functions_present = False
        
        # 檢查是否在帳單列表中添加了處理邏輯
        if 'show_edit_comprehensive_bill_form(st.session_state.editing_bill)' in content:
            print(f"      ✅ 編輯表單處理邏輯: 已添加")
        else:
            print(f"      ❌ 編輯表單處理邏輯: 未添加")
            all_functions_present = False
        
        if 'show_delete_comprehensive_bill_confirmation(st.session_state.confirm_delete_comprehensive_bill)' in content:
            print(f"      ✅ 刪除確認處理邏輯: 已添加")
        else:
            print(f"      ❌ 刪除確認處理邏輯: 未添加")
            all_functions_present = False
        
        return all_functions_present
    else:
        print(f"   ❌ {utilities_file} 不存在")
        return False

def test_sequence_number_removal():
    """測試序號欄位移除"""
    print("🔢 測試序號欄位移除...")
    
    files_to_check = [
        ('frontend/pages/residents.py', ['住戶列表']),
        ('frontend/pages/rooms.py', ['房間列表']),
        ('frontend/pages/utilities.py', ['帳單列表'])
    ]
    
    all_removed = True
    
    for file_path, descriptions in files_to_check:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"   檢查 {file_path}:")
            
            # 檢查是否移除了序號顯示
            sequence_patterns = [
                'x+1',
                '{x+1}',
                'enumerate.*1',
                '序號',
                '編號'
            ]
            
            file_clean = True
            for pattern in sequence_patterns:
                if pattern in content:
                    print(f"      ❌ 仍包含序號模式: {pattern}")
                    file_clean = False
                    all_removed = False
            
            if file_clean:
                print(f"      ✅ 已移除所有序號顯示")
        else:
            print(f"   ❌ {file_path} 不存在")
            all_removed = False
    
    return all_removed

def test_button_key_consistency():
    """測試按鈕key的一致性"""
    print("🔑 測試按鈕key的一致性...")
    
    utilities_file = 'frontend/pages/utilities.py'
    
    if os.path.exists(utilities_file):
        with open(utilities_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"   檢查 {utilities_file}:")
        
        # 檢查按鈕key是否移除了index參數
        old_patterns = [
            'mark_paid_{bill[\'id\']}_{index}',
            'mark_pending_{bill[\'id\']}_{index}',
            'edit_{bill[\'id\']}_{index}',
            'delete_{bill[\'id\']}_{index}'
        ]
        
        new_patterns = [
            'mark_paid_{bill[\'id\']}',
            'mark_pending_{bill[\'id\']}',
            'edit_{bill[\'id\']}',
            'delete_{bill[\'id\']}'
        ]
        
        all_updated = True
        
        for old_pattern in old_patterns:
            if old_pattern in content:
                print(f"      ❌ 仍使用舊的key格式: {old_pattern}")
                all_updated = False
        
        for new_pattern in new_patterns:
            if new_pattern in content:
                print(f"      ✅ 使用新的key格式: {new_pattern}")
            else:
                print(f"      ❌ 未找到新的key格式: {new_pattern}")
                all_updated = False
        
        return all_updated
    else:
        print(f"   ❌ {utilities_file} 不存在")
        return False

def test_function_signature_update():
    """測試函數簽名更新"""
    print("📝 測試函數簽名更新...")
    
    utilities_file = 'frontend/pages/utilities.py'
    
    if os.path.exists(utilities_file):
        with open(utilities_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"   檢查 {utilities_file}:")
        
        # 檢查函數簽名是否正確更新
        if 'def show_comprehensive_bill_details(bill):' in content:
            print(f"      ✅ show_comprehensive_bill_details函數簽名已更新")
        else:
            print(f"      ❌ show_comprehensive_bill_details函數簽名未更新")
            return False
        
        # 檢查函數調用是否正確更新
        if 'show_comprehensive_bill_details(bill)' in content:
            print(f"      ✅ show_comprehensive_bill_details函數調用已更新")
        else:
            print(f"      ❌ show_comprehensive_bill_details函數調用未更新")
            return False
        
        # 檢查是否移除了enumerate的index使用
        if 'for bill in bills:' in content:
            print(f"      ✅ 已移除enumerate的index使用")
        else:
            print(f"      ❌ 未移除enumerate的index使用")
            return False
        
        return True
    else:
        print(f"   ❌ {utilities_file} 不存在")
        return False

if __name__ == "__main__":
    print("🧪 修正驗證測試")
    print("=" * 60)
    
    # 1. 測試綜合帳單編輯和刪除功能
    edit_delete_test = test_comprehensive_bill_edit_delete()
    
    # 2. 測試序號欄位移除
    sequence_removal_test = test_sequence_number_removal()
    
    # 3. 測試按鈕key的一致性
    button_key_test = test_button_key_consistency()
    
    # 4. 測試函數簽名更新
    function_signature_test = test_function_signature_update()
    
    print("\n" + "=" * 60)
    print("📝 測試結果總結:")
    print("1. ✅ 綜合帳單編輯和刪除功能" if edit_delete_test else "1. ❌ 綜合帳單編輯和刪除功能")
    print("2. ✅ 序號欄位移除" if sequence_removal_test else "2. ❌ 序號欄位移除")
    print("3. ✅ 按鈕key一致性" if button_key_test else "3. ❌ 按鈕key一致性")
    print("4. ✅ 函數簽名更新" if function_signature_test else "4. ❌ 函數簽名更新")
    
    all_passed = all([
        edit_delete_test,
        sequence_removal_test,
        button_key_test,
        function_signature_test
    ])
    
    print("\n💡 修正內容總結:")
    print("🔧 綜合帳單編輯和刪除功能修正:")
    print("   - 添加了show_edit_comprehensive_bill_form函數")
    print("   - 添加了show_delete_comprehensive_bill_confirmation函數")
    print("   - 在show_comprehensive_bills_list中添加了處理邏輯")
    print("   - 修正了按鈕點擊後沒有反應的問題")
    
    print("\n🔢 序號欄位移除:")
    print("   - 移除了住戶列表selectbox中的序號顯示")
    print("   - 移除了房間列表selectbox中的序號顯示")
    print("   - 移除了帳單列表中不必要的index參數")
    print("   - 簡化了按鈕key的生成邏輯")
    
    print("\n🔑 按鈕key優化:")
    print("   - 移除了不必要的index參數")
    print("   - 使用bill['id']作為唯一標識")
    print("   - 提高了代碼的簡潔性和可維護性")
    
    print("\n📝 函數簽名優化:")
    print("   - 簡化了show_comprehensive_bill_details函數簽名")
    print("   - 移除了不必要的index參數")
    print("   - 改用直接遍歷bills列表")
    
    print(f"\n🏁 測試完成 - {'全部通過' if all_passed else '部分失敗'}")
    
    if all_passed:
        print("\n🎉 所有修正都已成功實作並測試通過！")
        print("   - 綜合帳單的編輯和刪除功能現在可以正常工作")
        print("   - 所有列表中的序號欄位都已移除")
        print("   - 代碼結構更加簡潔和一致")
    else:
        print("\n⚠️ 部分修正需要進一步檢查和調整")
