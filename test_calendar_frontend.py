#!/usr/bin/env python3
"""測試民國年日曆選擇器前端功能"""

import streamlit as st
import sys
import os
from datetime import date

# 添加前端路徑
sys.path.append('frontend')

def test_calendar_widget():
    """測試民國年日曆選擇器組件"""
    st.title("🧪 民國年日曆選擇器測試")
    
    try:
        from date_utils import roc_calendar_input, format_date_roc
        
        st.success("✅ 成功導入 roc_calendar_input 函數")
        
        # 測試基本功能
        st.subheader("📅 基本日曆選擇器測試")
        
        # 測試 1: 帶 key 的日曆選擇器
        st.write("**測試 1: 帶 key 的日曆選擇器**")
        selected_date1 = roc_calendar_input(
            "選擇日期（測試1）",
            value=date.today(),
            help="這是一個帶 key 的日曆選擇器",
            key="test_calendar_1"
        )
        
        if selected_date1:
            st.write(f"選擇的日期: {selected_date1}")
            st.write(f"民國年格式: {format_date_roc(selected_date1, 'full')}")
        
        st.markdown("---")
        
        # 測試 2: 不帶 key 的日曆選擇器（應該自動生成穩定 key）
        st.write("**測試 2: 不帶 key 的日曆選擇器**")
        selected_date2 = roc_calendar_input(
            "選擇日期（測試2）",
            value=date(2025, 1, 1),
            help="這是一個不帶 key 的日曆選擇器，應該自動生成穩定 key"
        )
        
        if selected_date2:
            st.write(f"選擇的日期: {selected_date2}")
            st.write(f"民國年格式: {format_date_roc(selected_date2, 'full')}")
        
        st.markdown("---")
        
        # 測試 3: 多個日曆選擇器
        st.write("**測試 3: 多個日曆選擇器**")
        
        col1, col2 = st.columns(2)
        
        with col1:
            start_date = roc_calendar_input(
                "開始日期",
                value=date.today(),
                key="test_start_date"
            )
        
        with col2:
            end_date = roc_calendar_input(
                "結束日期",
                value=date.today(),
                key="test_end_date"
            )
        
        if start_date and end_date:
            if start_date <= end_date:
                st.success(f"✅ 日期範圍有效: {format_date_roc(start_date, 'short')} 到 {format_date_roc(end_date, 'short')}")
            else:
                st.error("❌ 開始日期不能晚於結束日期")
        
        st.markdown("---")
        
        # 測試 4: 狀態保持測試
        st.write("**測試 4: 狀態保持測試**")
        st.write("點擊下面的按鈕來測試日曆選擇器狀態是否保持:")
        
        if st.button("🔄 重新渲染頁面", key="rerender_test"):
            st.rerun()
        
        persistent_date = roc_calendar_input(
            "持久性日期選擇器",
            value=date(2025, 7, 20),
            help="這個日曆選擇器的狀態應該在頁面重新渲染後保持",
            key="persistent_calendar"
        )
        
        if persistent_date:
            st.info(f"📌 當前選擇: {format_date_roc(persistent_date, 'full')}")
        
        # 顯示測試結果
        st.subheader("🎯 測試結果")
        
        test_results = []
        
        # 檢查是否有錯誤
        if 'error' not in st.session_state:
            test_results.append("✅ 日曆選擇器組件載入成功")
        else:
            test_results.append("❌ 日曆選擇器組件載入失敗")
        
        # 檢查日期選擇是否正常
        if selected_date1 and selected_date2:
            test_results.append("✅ 日期選擇功能正常")
        else:
            test_results.append("⚠️  部分日期選擇器未選擇日期")
        
        # 檢查多個選擇器是否衝突
        if start_date and end_date:
            test_results.append("✅ 多個日曆選擇器無衝突")
        else:
            test_results.append("⚠️  多個日曆選擇器測試未完成")
        
        for result in test_results:
            st.write(result)
        
        # 顯示技術信息
        st.subheader("🔧 技術信息")
        
        st.write("**修正內容:**")
        st.write("- 修正 key 生成邏輯，使用基於標籤和調用位置的穩定 key")
        st.write("- 移除隨機數生成，避免每次重新渲染時 key 改變")
        st.write("- 確保組件狀態在頁面重新渲染時保持穩定")
        
        st.write("**使用方式:**")
        st.code("""
# 帶 key 的使用方式（推薦）
selected_date = roc_calendar_input(
    "選擇日期",
    value=date.today(),
    key="my_unique_key"
)

# 不帶 key 的使用方式（自動生成穩定 key）
selected_date = roc_calendar_input(
    "選擇日期",
    value=date.today()
)
        """)
        
    except Exception as e:
        st.error(f"❌ 測試過程中發生錯誤: {str(e)}")
        st.exception(e)

if __name__ == "__main__":
    test_calendar_widget()
