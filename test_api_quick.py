#!/usr/bin/env python3
"""快速測試API功能"""

import requests
import json

def test_apis():
    """測試登入和住戶列表API"""
    base_url = 'http://localhost:8080'
    
    # 1. 登入獲取token
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    try:
        print("🔐 測試登入...")
        login_response = requests.post(f'{base_url}/auth/login', data=login_data)
        print(f'登入狀態碼: {login_response.status_code}')
        
        if login_response.status_code == 200:
            token = login_response.json()['access_token']
            print('✅ 登入成功')
            
            # 測試住戶列表API
            print("\n👥 測試住戶列表API...")
            headers = {'Authorization': f'Bearer {token}'}
            residents_response = requests.get(f'{base_url}/residents/', headers=headers)
            print(f'住戶列表狀態碼: {residents_response.status_code}')
            
            if residents_response.status_code == 200:
                residents = residents_response.json()
                print(f'✅ 住戶列表API正常，返回 {len(residents)} 個住戶')
            else:
                print(f'❌ 住戶列表API錯誤: {residents_response.status_code}')
                print(f'錯誤詳情: {residents_response.text}')
                
            # 測試房間列表API
            print("\n🏠 測試房間列表API...")
            rooms_response = requests.get(f'{base_url}/rooms/', headers=headers)
            print(f'房間列表狀態碼: {rooms_response.status_code}')
            
            if rooms_response.status_code == 200:
                rooms = rooms_response.json()
                print(f'✅ 房間列表API正常，返回 {len(rooms)} 個房間')
                
                # 如果有房間，測試房間詳情
                if rooms:
                    room_id = rooms[0]['id']
                    print(f"\n🔍 測試房間詳情API (房間ID: {room_id})...")
                    room_detail_response = requests.get(f'{base_url}/rooms/{room_id}', headers=headers)
                    print(f'房間詳情狀態碼: {room_detail_response.status_code}')
                    
                    if room_detail_response.status_code == 200:
                        print('✅ 房間詳情API正常')
                    else:
                        print(f'❌ 房間詳情API錯誤: {room_detail_response.status_code}')
                        print(f'錯誤詳情: {room_detail_response.text}')
            else:
                print(f'❌ 房間列表API錯誤: {rooms_response.status_code}')
                print(f'錯誤詳情: {rooms_response.text}')
                
        else:
            print(f'❌ 登入失敗: {login_response.status_code}')
            print(f'錯誤詳情: {login_response.text}')
            
    except Exception as e:
        print(f'❌ 測試過程中發生錯誤: {e}')

if __name__ == "__main__":
    test_apis()
