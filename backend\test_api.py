#!/usr/bin/env python3
"""
API測試腳本
測試租房管理系統的所有API端點
"""

import requests
import json
from datetime import datetime

BASE_URL = "http://localhost:8080"

def test_api_endpoint(method, endpoint, data=None, headers=None, auth_token=None):
    """測試API端點"""
    url = f"{BASE_URL}{endpoint}"
    
    if headers is None:
        headers = {"Content-Type": "application/json"}
    
    if auth_token:
        headers["Authorization"] = f"Bearer {auth_token}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, headers=headers)
        elif method.upper() == "PUT":
            response = requests.put(url, json=data, headers=headers)
        elif method.upper() == "DELETE":
            response = requests.delete(url, headers=headers)
        else:
            print(f"❌ 不支援的HTTP方法: {method}")
            return None
        
        print(f"🔍 {method.upper()} {endpoint}")
        print(f"   狀態碼: {response.status_code}")
        
        if response.status_code < 400:
            print(f"   ✅ 成功")
            try:
                result = response.json()
                print(f"   回應: {json.dumps(result, ensure_ascii=False, indent=2)}")
                return result
            except:
                print(f"   回應: {response.text}")
                return response.text
        else:
            print(f"   ❌ 失敗")
            try:
                error = response.json()
                print(f"   錯誤: {json.dumps(error, ensure_ascii=False, indent=2)}")
            except:
                print(f"   錯誤: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 請求失敗: {e}")
        return None

def main():
    """主測試函數"""
    print("🚀 開始API測試")
    print("=" * 50)
    
    # 1. 測試根路徑
    print("\n📍 1. 測試根路徑")
    test_api_endpoint("GET", "/")
    
    # 2. 測試健康檢查
    print("\n📍 2. 測試健康檢查")
    test_api_endpoint("GET", "/health")
    
    # 3. 測試認證相關API
    print("\n📍 3. 測試認證API")
    
    # 測試登入（需要先有用戶）
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    # 使用form data格式進行登入測試
    try:
        response = requests.post(
            f"{BASE_URL}/auth/login",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        print(f"🔍 POST /auth/login")
        print(f"   狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            print(f"   ✅ 登入成功")
            token_data = response.json()
            print(f"   回應: {json.dumps(token_data, ensure_ascii=False, indent=2)}")
            auth_token = token_data.get("access_token")
        else:
            print(f"   ❌ 登入失敗")
            try:
                error = response.json()
                print(f"   錯誤: {json.dumps(error, ensure_ascii=False, indent=2)}")
            except:
                print(f"   錯誤: {response.text}")
            auth_token = None
    except Exception as e:
        print(f"❌ 登入請求失敗: {e}")
        auth_token = None
    
    # 4. 測試房間管理API
    print("\n📍 4. 測試房間管理API")
    test_api_endpoint("GET", "/rooms", auth_token=auth_token)
    
    # 5. 測試住戶管理API
    print("\n📍 5. 測試住戶管理API")
    test_api_endpoint("GET", "/residents", auth_token=auth_token)
    
    # 6. 測試費用管理API
    print("\n📍 6. 測試費用管理API")
    test_api_endpoint("GET", "/utilities/rates/current", auth_token=auth_token)
    
    # 7. 測試報表API
    print("\n📍 7. 測試報表API")
    test_api_endpoint("GET", "/reports/dashboard", auth_token=auth_token)

    # 8. 測試創建房間API
    print("\n📍 8. 測試創建房間API")
    import random
    room_number = f"TEST{random.randint(100, 999)}"
    new_room_data = {
        "room_number": room_number,
        "floor": 1,
        "area": 15.0,
        "rent_single": 8000.0,
        "rent_double": 12000.0,
        "max_occupants": 2,
        "description": "測試房間"
    }
    created_room = test_api_endpoint("POST", "/rooms", data=new_room_data, auth_token=auth_token)

    # 9. 測試創建住戶API（如果房間創建成功）
    if created_room:
        print("\n📍 9. 測試創建住戶API")
        new_resident_data = {
            "name": "測試住戶",
            "phone": "0912345678",
            "id_number": "A123456789",
            "emergency_contact": "緊急聯絡人",
            "emergency_phone": "0987654321",
            "room_id": created_room["id"],
            "move_in_date": "2025-07-19T00:00:00",
            "deposit": 5000.0
        }
        created_resident = test_api_endpoint("POST", "/residents", data=new_resident_data, auth_token=auth_token)

        # 10. 測試創建費率API
        print("\n📍 10. 測試創建費率API")
        new_rate_data = {
            "electricity_rate": 6.0,
            "monthly_water_fee": 250.0,
            "effective_date": "2025-07-19T00:00:00",
            "notes": "測試費率"
        }
        test_api_endpoint("POST", "/utilities/rates", data=new_rate_data, auth_token=auth_token)

        # 11. 測試電表讀數API
        if created_resident:
            print("\n📍 11. 測試電表讀數API")
            reading_data = {
                "room_id": created_room["id"],
                "billing_year": 2025,
                "billing_month": 7,
                "current_electricity_reading": 100.0
            }
            test_api_endpoint("POST", "/utilities/readings", data=reading_data, auth_token=auth_token)

        # 12. 測試完整CRUD操作
        print("\n📍 12. 測試完整CRUD操作")

        # 測試房間詳情和更新
        if created_room:
            print("\n🏠 房間CRUD測試:")
            room_id = created_room["id"]

            # 獲取房間詳情
            room_detail = test_api_endpoint("GET", f"/rooms/{room_id}", auth_token=auth_token)

            # 更新房間
            update_room_data = {
                "description": "更新後的測試房間",
                "rent_single": 8500.0
            }
            test_api_endpoint("PUT", f"/rooms/{room_id}", data=update_room_data, auth_token=auth_token)

        # 測試住戶詳情和更新
        if created_resident:
            print("\n👥 住戶CRUD測試:")
            resident_id = created_resident["id"]

            # 獲取住戶詳情
            resident_detail = test_api_endpoint("GET", f"/residents/{resident_id}", auth_token=auth_token)

            # 更新住戶
            update_resident_data = {
                "phone": "0912345679",
                "emergency_contact": "更新的緊急聯絡人"
            }
            test_api_endpoint("PUT", f"/residents/{resident_id}", data=update_resident_data, auth_token=auth_token)

        # 測試費用管理CRUD
        print("\n💰 費用管理CRUD測試:")

        # 獲取所有費率
        test_api_endpoint("GET", "/utilities/rates", auth_token=auth_token)

        # 獲取帳單
        test_api_endpoint("GET", "/utilities/bills?year=2025&month=7", auth_token=auth_token)

    print("\n" + "=" * 50)
    print("🏁 完整API測試完成")

if __name__ == "__main__":
    main()
