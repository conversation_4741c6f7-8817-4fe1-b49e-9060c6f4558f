# =================== backend/app/routers/auth.py ===================
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from pydantic import BaseModel
from datetime import datetime, timedelta
from ..database import get_db
from ..models import User
from ..services import UserService
from ..auth import create_access_token, get_current_user
from ..config import settings

router = APIRouter(prefix="/auth", tags=["authentication"])

class UserCreate(BaseModel):
    username: str
    password: str
    email: str = None
    role: str = "user"

class Token(BaseModel):
    access_token: str
    token_type: str

class UserInfo(BaseModel):
    id: int
    username: str
    email: str = None
    role: str
    is_active: bool
    last_login: datetime = None

@router.post("/register", response_model=dict)
async def register(user_data: UserCreate, db: Session = Depends(get_db)):
    """用戶註冊"""
    try:
        user = UserService.create_user(
            db=db,
            username=user_data.username,
            password=user_data.password,
            email=user_data.email,
            role=user_data.role
        )
        return {"message": "用戶註冊成功", "user_id": user.id}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.post("/login", response_model=Token)
async def login(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    """用戶登入"""
    user = UserService.authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用戶名或密碼錯誤",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": user.username, "role": user.role},
        expires_delta=access_token_expires
    )
    
    return {"access_token": access_token, "token_type": "bearer"}

@router.get("/me", response_model=UserInfo)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """獲取當前用戶資訊"""
    return UserInfo(
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        role=current_user.role,
        is_active=current_user.is_active,
        last_login=current_user.last_login
    )

# =================== backend/app/routers/rooms.py ===================
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional
from ..database import get_db
from ..models import Room, User
from ..services import RoomService
from ..auth import get_current_user, require_role

router = APIRouter(prefix="/rooms", tags=["rooms"])

class RoomCreate(BaseModel):
    room_number: str
    floor: Optional[int] = None
    area: Optional[float] = None
    rent_single: float
    rent_double: float
    description: Optional[str] = None

class RoomResponse(BaseModel):
    id: int
    room_number: str
    floor: Optional[int]
    area: Optional[float]
    rent_single: float
    rent_double: float
    current_occupants: int
    max_occupants: int
    status: str
    description: Optional[str]
    is_active: bool
    current_rent: float

@router.post("/", response_model=dict)
async def create_room(
    room_data: RoomCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_role(["admin", "manager"]))
):
    """創建新房間"""
    try:
        room = RoomService.create_room(db, room_data.dict())
        return {"message": "房間創建成功", "room_id": room.id}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.get("/", response_model=List[dict])
async def get_rooms(
    include_inactive: bool = False,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """獲取所有房間"""
    rooms = RoomService.get_all_rooms(db, include_inactive)
    return [room.to_dict() for room in rooms]

@router.get("/available", response_model=List[dict])
async def get_available_rooms(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """獲取可用房間"""
    rooms = RoomService.get_available_rooms(db)
    return [room.to_dict() for room in rooms]

@router.get("/{room_id}", response_model=dict)
async def get_room(
    room_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """獲取特定房間"""
    room = RoomService.get_room_by_id(db, room_id)
    if not room:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="房間不存在"
        )
    return room.to_dict()

@router.put("/{room_id}", response_model=dict)
async def update_room(
    room_id: int,
    room_data: RoomCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_role(["admin", "manager"]))
):
    """更新房間資訊"""
    room = RoomService.get_room_by_id(db, room_id)
    if not room:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="房間不存在"
        )
    
    for key, value in room_data.dict().items():
        setattr(room, key, value)
    
    db.commit()
    db.refresh(room)
    return {"message": "房間更新成功", "room": room.to_dict()}

# =================== backend/app/routers/residents.py ===================
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime, date
from ..database import get_db
from ..models import Resident, User
from ..services import ResidentService
from ..auth import get_current_user, require_role

router = APIRouter(prefix="/residents", tags=["residents"])

class ResidentCreate(BaseModel):
    name: str
    id_number: str
    phone: Optional[str] = None
    emergency_contact: Optional[str] = None
    emergency_phone: Optional[str] = None
    room_id: int
    move_in_date: date
    deposit: float = 0.0

class ResidentMoveOut(BaseModel):
    move_out_date: date

@router.post("/", response_model=dict)
async def create_resident(
    resident_data: ResidentCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_role(["admin", "manager"]))
):
    """創建新住戶"""
    try:
        # 轉換日期格式
        data = resident_data.dict()
        data['move_in_date'] = datetime.combine(data['move_in_date'], datetime.min.time())
        
        resident = ResidentService.create_resident(db, data)
        return {"message": "住戶創建成功", "resident_id": resident.id}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.get("/", response_model=List[dict])
async def get_residents(
    active_only: bool = True,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """獲取所有住戶"""
    residents = ResidentService.get_all_residents(db, active_only)
    return [resident.to_dict() for resident in residents]

@router.get("/{resident_id}", response_model=dict)
async def get_resident(
    resident_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """獲取特定住戶"""
    resident = ResidentService.get_resident_by_id(db, resident_id)
    if not resident:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="住戶不存在"
        )
    return resident.to_dict()

@router.post("/{resident_id}/move-out", response_model=dict)
async def move_out_resident(
    resident_id: int,
    move_out_data: ResidentMoveOut,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_role(["admin", "manager"]))
):
    """住戶退房"""
    move_out_date = datetime.combine(move_out_data.move_out_date, datetime.min.time())
    
    try:
        resident = ResidentService.move_out_resident(db, resident_id, move_out_date)
        if not resident:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="住戶不存在"
            )
        return {"message": "退房成功", "resident_id": resident_id}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"退房失敗: {str(e)}"
        )

# =================== backend/app/routers/utilities.py ===================
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime, date
from ..database import get_db
from ..models import UtilityRate, UtilityRecord, User
from ..services import UtilityService
from ..auth import get_current_user, require_role

router = APIRouter(prefix="/utilities", tags=["utilities"])

class UtilityRateCreate(BaseModel):
    electricity_rate: float
    monthly_water_fee: float
    effective_date: date
    notes: Optional[str] = None

class MeterReadingCreate(BaseModel):
    room_id: int
    billing_year: int
    billing_month: int
    current_electricity_reading: float

class PaymentStatusUpdate(BaseModel):
    payment_status: str
    payment_date: Optional[date] = None

@router.post("/rates", response_model=dict)
async def create_utility_rate(
    rate_data: UtilityRateCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_role(["admin", "manager"]))
):
    """創建新費率"""
    try:
        # 轉換日期格式
        data = rate_data.dict()
        data['effective_date'] = datetime.combine(data['effective_date'], datetime.min.time())
        
        rate = UtilityService.create_utility_rate(db, data)
        return {"message": "費率創建成功", "rate_id": rate.id}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"創建費率失敗: {str(e)}"
        )

@router.get("/rates/current", response_model=dict)
async def get_current_utility_rate(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """獲取當前費率"""
    rate = UtilityService.get_current_utility_rate(db)
    if not rate:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="未設定費率"
        )
    return rate.to_dict()

@router.post("/readings", response_model=dict)
async def create_meter_reading(
    reading_data: MeterReadingCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_role(["admin", "manager"]))
):
    """創建電表抄錄記錄"""
    try:
        # 檢查是否已有該月份的記錄
        existing_record = db.query(UtilityRecord).filter(
            UtilityRecord.room_id == reading_data.room_id,
            UtilityRecord.billing_year == reading_data.billing_year,
            UtilityRecord.billing_month == reading_data.billing_month
        ).first()
        
        if existing_record:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="該月份已有抄錄記錄"
            )
        
        record = UtilityService.calculate_monthly_bill(
            db,
            reading_data.room_id,
            reading_data.billing_year,
            reading_data.billing_month,
            reading_data.current_electricity_reading
        )
        
        return {"message": "抄錄記錄創建成功", "record_id": record.id}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.get("/bills", response_model=List[dict])
async def get_utility_bills(
    year: int,
    month: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """獲取費用帳單"""
    bills = UtilityService.get_monthly_bills(db, year, month)
    return [bill.to_dict() for bill in bills]

@router.put("/bills/{bill_id}/payment", response_model=dict)
async def update_payment_status(
    bill_id: int,
    payment_data: PaymentStatusUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_role(["admin", "manager"]))
):
    """更新付款狀態"""
    payment_date = None
    if payment_data.payment_date:
        payment_date = datetime.combine(payment_data.payment_date, datetime.min.time())
    
    bill = UtilityService.update_payment_status(
        db, bill_id, payment_data.payment_status, payment_date
    )
    
    if not bill:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="帳單不存在"
        )
    
    return {"message": "付款狀態更新成功", "bill_id": bill_id}

# =================== backend/app/routers/reports.py ===================
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import func, extract
from typing import List, Dict, Any
from datetime import datetime, date
from ..database import get_db
from ..models import Room, Resident, UtilityRecord, User
from ..auth import get_current_user

router = APIRouter(prefix="/reports", tags=["reports"])

@router.get("/dashboard", response_model=dict)
async def get_dashboard_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """獲取儀表板統計資料"""
    # 房間統計
    total_rooms = db.query(Room).filter(Room.is_active == True).count()
    occupied_rooms = db.query(Room).filter(
        Room.is_active == True,
        Room.current_occupants > 0
    ).count()
    available_rooms = total_rooms - occupied_rooms
    
    # 住戶統計
    total_residents = db.query(Resident).filter(Resident.is_active == True).count()
    
    # 入住率
    occupancy_rate = (occupied_rooms / total_rooms * 100) if total_rooms > 0 else 0
    
    # 當月收入統計
    current_month = datetime.now().month
    current_year = datetime.now().year
    
    monthly_income = db.query(func.sum(UtilityRecord.total_amount)).filter(
        UtilityRecord.billing_year == current_year,
        UtilityRecord.billing_month == current_month
    ).scalar() or 0
    
    return {
        "total_rooms": total_rooms,
        "occupied_rooms": occupied_rooms,
        "available_rooms": available_rooms,
        "total_residents": total_residents,
        "occupancy_rate": round(occupancy_rate, 1),
        "monthly_income": monthly_income
    }

@router.get("/occupancy-trend", response_model=List[dict])
async def get_occupancy_trend(
    months: int = 12,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """獲取入住率趨勢"""
    # 這裡實作入住率趨勢的計算邏輯
    # 簡化版本：返回最近12個月的入住率
    result = []
    current_date = datetime.now()
    
    for i in range(months):
        # 計算目標月份
        target_month = current_date.month - i
        target_year = current_date.year
        
        if target_month <= 0:
            target_month += 12
            target_year -= 1
        
        # 計算該月的入住率（這裡需要根據實際需求調整）
        total_rooms = db.query(Room).filter(Room.is_active == True).count()
        # 簡化計算：假設當前入住率適用於所有月份
        occupied_rooms = db.query(Room).filter(
            Room.is_active == True,
            Room.current_occupants > 0
        ).count()
        
        occupancy_rate = (occupied_rooms / total_rooms * 100) if total_rooms > 0 else 0
        
        result.append({
            "year": target_year,
            "month": target_month,
            "occupancy_rate": round(occupancy_rate, 1)
        })
    
    return list(reversed(result))

@router.get("/income-summary", response_model=dict)
async def get_income_summary(
    year: int,
    month: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """獲取收入統計"""
    # 電費收入
    electricity_income = db.query(func.sum(UtilityRecord.electricity_cost)).filter(
        UtilityRecord.billing_year == year,
        UtilityRecord.billing_month == month
    ).scalar() or 0
    
    # 水費收入
    water_income = db.query(func.sum(UtilityRecord.water_fee)).filter(
        UtilityRecord.billing_year == year,
        UtilityRecord.billing_month == month
    ).scalar() or 0
    
    # 總收入
    total_income = electricity_income + water_income
    
    # 已付款金額
    paid_amount = db.query(func.sum(UtilityRecord.total_amount)).filter(
        UtilityRecord.billing_year == year,
        UtilityRecord.billing_month == month,
        UtilityRecord.payment_status == "paid"
    ).scalar() or 0
    
    # 未付款金額
    unpaid_amount = db.query(func.sum(UtilityRecord.total_amount)).filter(
        UtilityRecord.billing_year == year,
        UtilityRecord.billing_month == month,
        UtilityRecord.payment_status == "pending"
    ).scalar() or 0
    
    return {
        "year": year,
        "month": month,
        "electricity_income": electricity_income,
        "water_income": water_income,
        "total_income": total_income,
        "paid_amount": paid_amount,
        "unpaid_amount": unpaid_amount,
        "collection_rate": round((paid_amount / total_income * 100), 1) if total_income > 0 else 0
    }

# =================== backend/app/main.py ===================
from fastapi import FastAPI, Request, status
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from .database import create_tables
from .config import settings
from .routers import auth, rooms, residents, utilities, reports
import logging

# 配置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 創建FastAPI應用程式
app = FastAPI(
    title=settings.app_name,
    description="現代化租房管理系統",
    version=settings.app_version
)

# 添加CORS中間件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:8501", "http://127.0.0.1:8501"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 註冊路由
app.include_router(auth.router)
app.include_router(rooms.router)
app.include_router(residents.router)
app.include_router(utilities.router)
app.include_router(reports.router)

@app.on_event("startup")
async def startup_event():
    """應用程式啟動事件"""
    logger.info(f"啟動 {settings.app_name} v{settings.app_version}")
    
    # 創建資料庫表格
    create_tables()
    
    # 創建預設管理員帳號
    try:
        from .database import get_db_session
        from .services import UserService
        
        db = next(get_db_session())
        try:
            admin_user = UserService.get_user_by_username(db, "admin")
            if not admin_user:
                UserService.create_user(db, "admin", "admin123", "<EMAIL>", "admin")
                logger.info("創建預設管理員帳號: admin/admin123")
        except Exception as e:
            logger.error(f"創建管理員帳號失敗: {e}")
        finally:
            db.close()
    except Exception as e:
        logger.error(f"啟動初始化失敗: {e}")

@app.get("/")
async def root():
    """根路徑"""
    return {
        "message": f"歡迎使用 {settings.app_name}",
        "version": settings.app_version,
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """健康檢查"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局異常處理器"""
    logger.error(f"全局異常: {exc}")
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"error": "系統內部錯誤", "message": str(exc)}
    )

# =================== backend/init_db.py ===================
from app.database import create_tables
from app.services import UserService
from app.config import settings
from app.database import get_db_session

def init_database():
    """初始化資料庫"""
    print("正在初始化資料庫...")
    
    # 創建資料庫表格
    create_tables()
    print("資料庫表格創建完成")
    
    # 創建預設管理員帳號
    try:
        db = next(get_db_session())
        try:
            admin_user = UserService.get_user_by_username(db, "admin")
            if not admin_user:
                UserService.create_user(db, "admin", "admin123", "<EMAIL>", "admin")
                print("創建預設管理員帳號: admin/admin123")
            else:
                print("管理員帳號已存在")
        finally:
            db.close()
    except Exception as e:
        print(f"創建管理員帳號失敗: {e}")
    
    print("資料庫初始化完成")

if __name__ == "__main__":
    init_database()

# =================== backend/run_server.py ===================
import uvicorn
from app.config import settings

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level="info"
    )