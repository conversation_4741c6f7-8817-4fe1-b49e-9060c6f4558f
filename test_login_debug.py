#!/usr/bin/env python3
"""測試登入功能並調試"""

import requests
import json
import sys
import os

import sqlite3
from passlib.context import CryptContext

# 設置密碼加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """驗證密碼"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """生成密碼哈希"""
    return pwd_context.hash(password)

def check_password_hash():
    """檢查密碼哈希"""
    print("🔍 檢查密碼哈希...")
    
    # 連接資料庫
    conn = sqlite3.connect('backend/rental_management.db')
    cursor = conn.cursor()
    
    cursor.execute('SELECT username, password_hash FROM users WHERE username = "admin"')
    result = cursor.fetchone()
    
    if result:
        username, stored_hash = result
        print(f"✅ 找到用戶: {username}")
        print(f"📝 存儲的哈希: {stored_hash[:50]}...")
        
        # 測試密碼驗證
        test_password = "admin123"
        is_valid = verify_password(test_password, stored_hash)
        print(f"🔐 密碼 '{test_password}' 驗證結果: {'✅ 正確' if is_valid else '❌ 錯誤'}")
        
        # 生成新的哈希進行比較
        new_hash = get_password_hash(test_password)
        print(f"🆕 新生成的哈希: {new_hash[:50]}...")
        
    else:
        print("❌ 未找到 admin 用戶")
    
    conn.close()

def test_login_api():
    """測試登入 API"""
    print("\n🔐 測試登入 API...")
    
    base_url = 'http://localhost:8080'
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    try:
        response = requests.post(f'{base_url}/auth/login', data=login_data)
        print(f"📡 API 響應狀態碼: {response.status_code}")
        print(f"📄 API 響應內容: {response.text}")
        
        if response.status_code == 200:
            token_data = response.json()
            print(f"✅ 登入成功，獲得 token: {token_data['access_token'][:50]}...")
            return token_data['access_token']
        else:
            print(f"❌ 登入失敗: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ API 調用錯誤: {e}")
        return None

def test_residents_api(token):
    """測試住戶列表 API"""
    if not token:
        print("⏭️  跳過住戶 API 測試（無有效 token）")
        return
        
    print("\n👥 測試住戶列表 API...")
    
    base_url = 'http://localhost:8080'
    headers = {'Authorization': f'Bearer {token}'}
    
    try:
        response = requests.get(f'{base_url}/residents/', headers=headers)
        print(f"📡 住戶 API 響應狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            residents = response.json()
            print(f"✅ 住戶列表 API 正常，返回 {len(residents)} 個住戶")
        else:
            print(f"❌ 住戶列表 API 錯誤: {response.status_code}")
            print(f"📄 錯誤詳情: {response.text}")
            
    except Exception as e:
        print(f"❌ 住戶 API 調用錯誤: {e}")

def test_rooms_api(token):
    """測試房間列表 API"""
    if not token:
        print("⏭️  跳過房間 API 測試（無有效 token）")
        return
        
    print("\n🏠 測試房間列表 API...")
    
    base_url = 'http://localhost:8080'
    headers = {'Authorization': f'Bearer {token}'}
    
    try:
        response = requests.get(f'{base_url}/rooms/', headers=headers)
        print(f"📡 房間 API 響應狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            rooms = response.json()
            print(f"✅ 房間列表 API 正常，返回 {len(rooms)} 個房間")
        else:
            print(f"❌ 房間列表 API 錯誤: {response.status_code}")
            print(f"📄 錯誤詳情: {response.text}")
            
    except Exception as e:
        print(f"❌ 房間 API 調用錯誤: {e}")

if __name__ == "__main__":
    print("🔧 登入功能調試測試")
    print("=" * 50)
    
    # 1. 檢查密碼哈希
    check_password_hash()
    
    # 2. 測試登入 API
    token = test_login_api()
    
    # 3. 測試其他 API
    test_residents_api(token)
    test_rooms_api(token)
    
    print("\n" + "=" * 50)
    print("🏁 測試完成")
