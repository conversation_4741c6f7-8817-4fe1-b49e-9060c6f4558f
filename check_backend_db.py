#!/usr/bin/env python3
"""檢查 backend 目錄下的資料庫結構"""

import sqlite3
import os

def check_backend_database():
    """檢查 backend 目錄下的資料庫文件"""
    # 檢查 backend 目錄下的資料庫文件
    db_file = 'backend/rental_management.db'
    if not os.path.exists(db_file):
        print(f'❌ 資料庫文件 {db_file} 不存在')
        return False

    # 連接資料庫並檢查 residents 表結構
    conn = sqlite3.connect(db_file)
    cursor = conn.cursor()

    print('📋 檢查 backend/rental_management.db 中的 residents 表結構:')
    print('=' * 60)

    # 獲取表結構
    cursor.execute('PRAGMA table_info(residents)')
    columns = cursor.fetchall()

    if not columns:
        print('❌ residents 表不存在')
        conn.close()
        return False
    else:
        print('✅ residents 表存在，欄位如下:')
        for col in columns:
            col_id, name, type_name, not_null, default, pk = col
            marker = '🆕' if name == 'lease_end_date' else '  '
            print(f'{marker} {name}: {type_name}')

    # 檢查是否有 lease_end_date 欄位
    cursor.execute('SELECT COUNT(*) FROM pragma_table_info("residents") WHERE name = "lease_end_date"')
    has_lease_end_date = cursor.fetchone()[0] > 0

    print(f'\n🔍 lease_end_date 欄位存在: {"✅ 是" if has_lease_end_date else "❌ 否"}')

    conn.close()
    return has_lease_end_date

def add_lease_end_date_to_backend_db():
    """在 backend 資料庫中添加 lease_end_date 欄位"""
    db_file = 'backend/rental_management.db'
    
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        print('\n🔧 在 backend 資料庫中添加 lease_end_date 欄位...')
        
        # 添加 lease_end_date 欄位
        cursor.execute('ALTER TABLE residents ADD COLUMN lease_end_date DATETIME NULL')
        conn.commit()
        
        print('✅ lease_end_date 欄位添加成功')
        
        # 驗證添加結果
        cursor.execute('PRAGMA table_info(residents)')
        columns = cursor.fetchall()
        
        print('\n📋 更新後的表結構:')
        for col in columns:
            col_id, name, type_name, not_null, default, pk = col
            marker = '🆕' if name == 'lease_end_date' else '  '
            print(f'{marker} {name}: {type_name}')
        
        conn.close()
        return True
        
    except Exception as e:
        print(f'❌ 添加欄位失敗: {e}')
        return False

if __name__ == "__main__":
    print("🔍 檢查 backend 資料庫結構")
    print("=" * 50)
    
    has_field = check_backend_database()
    
    if not has_field:
        print("\n需要添加 lease_end_date 欄位")
        success = add_lease_end_date_to_backend_db()
        if success:
            print("\n✅ backend 資料庫修正完成")
        else:
            print("\n❌ backend 資料庫修正失敗")
    else:
        print("\n✅ backend 資料庫已包含 lease_end_date 欄位")
