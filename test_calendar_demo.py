#!/usr/bin/env python3
"""民國年日曆選擇器展示頁面"""

import streamlit as st
import sys
import os
from datetime import date, datetime

# 添加前端路徑
sys.path.append('frontend')

from date_utils import roc_calendar_input, roc_date_input, format_date_roc

def main():
    """主函數"""
    st.set_page_config(
        page_title="民國年日曆選擇器展示",
        page_icon="📅",
        layout="wide"
    )
    
    st.title("📅 民國年日曆選擇器展示")
    st.markdown("---")
    
    # 說明
    st.markdown("""
    ## 🎯 修正說明
    
    根據您的要求，我們已經完成以下修正：
    
    1. **✅ 移除民國年提示文字**：所有日期輸入欄位不再顯示 "(民國年)" 等提示文字
    2. **✅ 實作民國年日曆選擇器**：新增下拉選單式的民國年日期選擇器
    3. **✅ 更新前端頁面**：住戶管理和費用管理頁面已使用新的日曆選擇器
    
    ## 📋 功能對比
    """)
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("🔹 原始日期選擇器 (無提示文字)")
        st.caption("roc_date_input - 使用 Streamlit 原生日期選擇器，已移除所有民國年提示文字")
        
        selected_date_1 = roc_date_input(
            "選擇日期",
            value=date.today(),
            help="這是原始的日期選擇器，不再顯示民國年提示"
        )
        
        if selected_date_1:
            st.info(f"📅 選擇的日期：{format_date_roc(selected_date_1, 'full')}")
    
    with col2:
        st.subheader("🔹 民國年日曆選擇器 (新功能)")
        st.caption("roc_calendar_input - 使用下拉選單的民國年日期選擇器")
        
        selected_date_2 = roc_calendar_input(
            "選擇日期",
            value=date.today(),
            help="這是新的民國年日曆選擇器，使用下拉選單方式",
            key="demo_calendar"
        )
    
    st.markdown("---")
    
    # 功能展示
    st.subheader("🎮 功能展示")
    
    demo_tab1, demo_tab2, demo_tab3 = st.tabs(["住戶入住日期", "租約到期日", "費率生效日期"])
    
    with demo_tab1:
        st.markdown("**模擬住戶管理 - 入住日期選擇**")
        move_in_date = roc_calendar_input(
            "入住日期*",
            value=date.today(),
            help="請選擇住戶入住日期",
            key="move_in_demo"
        )
        
        if move_in_date:
            st.success(f"✅ 入住日期設定為：{format_date_roc(move_in_date, 'full')}")
    
    with demo_tab2:
        st.markdown("**模擬住戶管理 - 租約到期日選擇**")
        lease_end_date = roc_calendar_input(
            "租約到期日",
            value=None,
            help="請選擇租約到期日期（可選）",
            key="lease_end_demo"
        )
        
        if lease_end_date:
            st.success(f"✅ 租約到期日設定為：{format_date_roc(lease_end_date, 'full')}")
        else:
            st.info("📝 租約到期日未設定")
    
    with demo_tab3:
        st.markdown("**模擬費用管理 - 費率生效日期**")
        effective_date = roc_calendar_input(
            "生效日期*",
            value=date.today(),
            help="新費率的生效日期",
            key="effective_date_demo"
        )
        
        if effective_date:
            st.success(f"✅ 費率生效日期設定為：{format_date_roc(effective_date, 'full')}")
    
    st.markdown("---")
    
    # 技術說明
    st.subheader("🔧 技術實作說明")
    
    with st.expander("查看技術細節"):
        st.markdown("""
        ### 修正內容
        
        1. **date_utils.py**
           - 修正 `roc_date_input` 函數，移除所有民國年提示文字
           - 新增 `roc_calendar_input` 函數，實作下拉選單式民國年日期選擇器
        
        2. **residents.py**
           - 更新導入語句，加入 `roc_calendar_input`
           - 將入住日期和租約到期日改用 `roc_calendar_input`
           - 將退房日期改用 `roc_calendar_input`
        
        3. **utilities.py**
           - 更新導入語句，加入 `roc_calendar_input`
           - 將費率生效日期改用 `roc_calendar_input`
           - 將租金到期日期改用 `roc_calendar_input`
           - 將付款日期改用 `roc_calendar_input`
        
        ### 功能特點
        
        - **無提示文字**：所有日期選擇器不再顯示民國年相關提示
        - **民國年顯示**：選擇後自動顯示對應的民國年格式
        - **範圍控制**：支援最小值和最大值限制
        - **錯誤處理**：自動處理無效日期和範圍檢查
        - **向後相容**：保留原有的 `roc_date_input` 函數
        """)
    
    st.markdown("---")
    st.markdown("**🎉 修正完成！您現在可以在租房管理系統中使用新的民國年日曆選擇器了。**")

if __name__ == "__main__":
    main()
