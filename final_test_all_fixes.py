#!/usr/bin/env python3
"""最終測試所有修正"""

import requests
import sys

def test_all_fixes():
    """測試所有修正"""
    base_url = 'http://localhost:8080'
    login_data = {'username': 'admin', 'password': 'admin5813'}

    try:
        # 登入
        print('🔐 登入系統...')
        response = requests.post(f'{base_url}/auth/login', data=login_data)
        if response.status_code != 200:
            print(f'❌ 登入失敗: {response.status_code}')
            return False
            
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        print('✅ 登入成功')

        # 測試 1: 面積為 0 的房間（應該成功）
        print('\n📏 測試 1: 創建面積為 0 的房間...')
        room_data_zero = {
            'room_number': 'TEST_ZERO_AREA',
            'floor': 1,
            'area': 0.0,
            'rent_single': 8000,
            'rent_double': 12000,
            'description': '面積為0的房間'
        }
        
        response = requests.post(f'{base_url}/rooms/', json=room_data_zero, headers=headers)
        if response.status_code == 200:
            print('✅ 成功創建面積為 0 的房間')
            room_id = response.json()['id']
            
            # 清理
            requests.delete(f'{base_url}/rooms/{room_id}', headers=headers)
        else:
            print(f'❌ 創建失敗: {response.status_code} - {response.text}')
            return False

        # 測試 2: 負數面積（應該失敗）
        print('\n🚫 測試 2: 創建負數面積的房間（應該失敗）...')
        room_data_negative = {
            'room_number': 'TEST_NEGATIVE_AREA',
            'floor': 1,
            'area': -1.0,
            'rent_single': 8000,
            'rent_double': 12000,
            'description': '負數面積的房間'
        }
        
        response = requests.post(f'{base_url}/rooms/', json=room_data_negative, headers=headers)
        if response.status_code == 422:
            error_detail = response.json()
            if 'area' in str(error_detail) and '負數' in str(error_detail):
                print('✅ 正確阻止負數面積，錯誤訊息正確')
            else:
                print(f'⚠️  阻止了負數面積，但錯誤訊息不正確: {error_detail}')
        else:
            print(f'❌ 應該阻止負數面積，但狀態碼是: {response.status_code}')
            return False

        # 測試 3: 更新房間面積為 0（應該成功）
        print('\n🔄 測試 3: 更新房間面積為 0...')
        # 先創建一個房間
        room_data = {
            'room_number': 'TEST_UPDATE_AREA',
            'floor': 1,
            'area': 10.0,
            'rent_single': 8000,
            'rent_double': 12000,
            'description': '用於測試更新的房間'
        }
        
        create_response = requests.post(f'{base_url}/rooms/', json=room_data, headers=headers)
        if create_response.status_code == 200:
            room_id = create_response.json()['id']
            
            # 更新面積為 0
            update_data = {'area': 0.0}
            update_response = requests.put(f'{base_url}/rooms/{room_id}', json=update_data, headers=headers)
            
            if update_response.status_code == 200:
                print('✅ 成功更新房間面積為 0')
            else:
                print(f'❌ 更新失敗: {update_response.status_code} - {update_response.text}')
                return False
            
            # 清理
            requests.delete(f'{base_url}/rooms/{room_id}', headers=headers)
        else:
            print(f'❌ 創建測試房間失敗: {create_response.status_code}')
            return False

        # 測試 4: 日期工具修正
        print('\n📅 測試 4: 日期工具修正...')
        try:
            sys.path.append('frontend')
            from date_utils import format_date_roc
            from datetime import date
            
            test_date = date(2025, 7, 20)
            formatted = format_date_roc(test_date, 'full')
            expected = '民國114年7月20日'
            
            if formatted == expected:
                print(f'✅ 日期格式化正確: {formatted}')
            else:
                print(f'⚠️  日期格式化結果不符預期: {formatted} (預期: {expected})')
                
        except Exception as e:
            print(f'❌ 日期工具測試錯誤: {e}')
            return False

        print('\n🎉 所有測試通過！')
        return True

    except Exception as e:
        print(f'❌ 測試過程中發生錯誤: {e}')
        return False

if __name__ == "__main__":
    print("🧪 最終測試 - 驗證所有修正")
    print("=" * 60)
    
    success = test_all_fixes()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有修正驗證成功！")
        print("\n📝 修正完成項目:")
        print("1. ✅ 移除民國年格式提示文字")
        print("2. ✅ 修正房間面積驗證（允許面積為 0，禁止負數）")
        print("3. ✅ 修正房間刪除後的編輯狀態清理")
        print("\n💡 前端測試建議:")
        print("- 檢查日期輸入欄位是否已移除民國年格式提示")
        print("- 測試房間面積設為 0 是否能正常保存")
        print("- 測試房間刪除後編輯狀態是否正確結束")
    else:
        print("❌ 部分修正驗證失敗，請檢查上述錯誤訊息")
    
    print("\n🏁 測試完成")
