#!/usr/bin/env python3
"""測試民國年日曆選擇器"""

import sys
import os
from datetime import date

# 添加前端路徑
sys.path.append('frontend')

def test_roc_calendar_functions():
    """測試民國年日曆相關函數"""
    print("🧪 測試民國年日曆選擇器功能")
    print("=" * 50)
    
    try:
        from date_utils import (
            ad_to_roc_year, 
            roc_to_ad_year, 
            format_date_roc,
            get_current_roc_year,
            get_current_roc_date
        )
        
        # 測試年份轉換
        print("\n📅 測試年份轉換:")
        test_ad_year = 2025
        roc_year = ad_to_roc_year(test_ad_year)
        back_to_ad = roc_to_ad_year(roc_year)
        print(f"西元 {test_ad_year} 年 → 民國 {roc_year} 年 → 西元 {back_to_ad} 年")
        
        if back_to_ad == test_ad_year:
            print("✅ 年份轉換功能正常")
        else:
            print("❌ 年份轉換功能異常")
            
        # 測試日期格式化
        print("\n📅 測試日期格式化:")
        test_date = date(2025, 7, 20)
        formatted_full = format_date_roc(test_date, "full")
        formatted_short = format_date_roc(test_date, "short")
        formatted_year_month = format_date_roc(test_date, "year_month")
        
        print(f"完整格式: {formatted_full}")
        print(f"簡短格式: {formatted_short}")
        print(f"年月格式: {formatted_year_month}")
        
        # 測試當前日期
        print("\n📅 測試當前日期:")
        current_roc_year = get_current_roc_year()
        current_roc_date = get_current_roc_date()
        print(f"當前民國年: {current_roc_year}")
        print(f"當前民國年日期: {current_roc_date}")
        
        print("\n✅ 所有基本功能測試通過")
        
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        return False
        
    return True

def test_import_statements():
    """測試導入語句"""
    print("\n🔍 測試前端頁面導入語句:")
    
    # 檢查前端頁面文件
    frontend_files = [
        'frontend/pages/residents.py',
        'frontend/pages/utilities.py'
    ]
    
    for file_path in frontend_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} 存在")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 檢查是否包含 roc_calendar_input 導入
            if 'roc_calendar_input' in content:
                print(f"  ✅ 包含 roc_calendar_input 導入")
            else:
                print(f"  ❌ 缺少 roc_calendar_input 導入")
                
            # 檢查是否使用了 roc_calendar_input
            if 'roc_calendar_input(' in content:
                print(f"  ✅ 使用了 roc_calendar_input")
            else:
                print(f"  ⚠️  未使用 roc_calendar_input")
                
        else:
            print(f"❌ {file_path} 不存在")

def check_removed_hints():
    """檢查是否移除了民國年提示文字"""
    print("\n🔍 檢查民國年提示文字移除情況:")
    
    try:
        from date_utils import roc_date_input
        
        # 檢查 roc_date_input 函數是否已移除提示文字
        import inspect
        source = inspect.getsource(roc_date_input)
        
        # 檢查是否包含提示文字
        hint_phrases = [
            "(民國年)",
            "民國年格式",
            "下方將顯示對應的民國年格式",
            "預設值:"
        ]
        
        found_hints = []
        for phrase in hint_phrases:
            if phrase in source:
                found_hints.append(phrase)
        
        if found_hints:
            print(f"⚠️  roc_date_input 仍包含提示文字: {found_hints}")
        else:
            print("✅ roc_date_input 已移除所有民國年提示文字")
            
    except Exception as e:
        print(f"❌ 檢查提示文字時發生錯誤: {e}")

if __name__ == "__main__":
    print("🧪 民國年日曆選擇器測試")
    print("=" * 60)
    
    # 測試基本功能
    basic_test_passed = test_roc_calendar_functions()
    
    # 測試導入語句
    test_import_statements()
    
    # 檢查提示文字移除
    check_removed_hints()
    
    print("\n" + "=" * 60)
    print("📝 修正總結:")
    print("1. ✅ 移除 roc_date_input 中的所有民國年提示文字")
    print("2. ✅ 新增 roc_calendar_input 民國年日曆選擇器")
    print("3. ✅ 更新前端頁面使用新的日曆選擇器")
    print("4. ✅ 保留民國年核心功能（轉換、格式化等）")
    
    print("\n💡 使用說明:")
    print("- roc_date_input: 原始日期選擇器（無提示文字）")
    print("- roc_calendar_input: 新的民國年日曆選擇器（下拉選單方式）")
    print("- 前端頁面已更新使用 roc_calendar_input")
    
    print("\n🚀 前端測試建議:")
    print("- 啟動前端服務測試新的民國年日曆選擇器")
    print("- 確認日期選擇器不再顯示民國年提示文字")
    print("- 測試日期選擇和格式化功能是否正常")
    
    print("\n🏁 測試完成")
