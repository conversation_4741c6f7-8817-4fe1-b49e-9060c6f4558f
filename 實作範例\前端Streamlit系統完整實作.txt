# =================== frontend/requirements.txt ===================
streamlit==1.28.1
requests==2.31.0
pandas==2.1.1
plotly==5.17.0
python-dotenv==1.0.0

# =================== frontend/config.py ===================
import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    """前端配置管理"""
    
    # API設定
    API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")
    
    # 應用程式設定
    APP_TITLE = "租房管理系統"
    APP_ICON = "🏠"
    
    # 頁面設定
    PAGE_CONFIG = {
        "page_title": APP_TITLE,
        "page_icon": APP_ICON,
        "layout": "wide",
        "initial_sidebar_state": "expanded"
    }
    
    # 狀態預設值
    DEFAULT_ELECTRICITY_RATE = 5.5
    DEFAULT_WATER_FEE = 200.0

config = Config()

# =================== frontend/api_client.py ===================
import requests
import streamlit as st
from typing import Dict, Any, Optional, List
import json

class APIClient:
    """API客戶端封裝"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def _get_headers(self) -> Dict[str, str]:
        """獲取請求頭"""
        headers = {"Content-Type": "application/json"}
        
        if st.session_state.get('access_token'):
            headers["Authorization"] = f"Bearer {st.session_state.access_token}"
        
        return headers
    
    def _handle_response(self, response: requests.Response) -> Optional[Dict[str, Any]]:
        """處理API響應"""
        if response.status_code == 401:
            st.error("認證失效，請重新登入")
            st.session_state.authenticated = False
            st.session_state.access_token = None
            st.rerun()
        
        if not response.ok:
            try:
                error_detail = response.json().get('detail', '未知錯誤')
            except:
                error_detail = response.text or '未知錯誤'
            st.error(f"API錯誤: {error_detail}")
            return None
        
        return response.json()
    
    def login(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """用戶登入"""
        try:
            response = self.session.post(
                f"{self.base_url}/auth/login",
                data={"username": username, "password": password}
            )
            
            if response.ok:
                token_data = response.json()
                # 獲取用戶資訊
                user_info = self.get_current_user(token_data['access_token'])
                if user_info:
                    return {
                        "token": token_data['access_token'],
                        "user_info": user_info
                    }
            else:
                st.error("登入失敗，請檢查用戶名和密碼")
        except Exception as e:
            st.error(f"登入錯誤: {str(e)}")
        
        return None
    
    def get_current_user(self, token: str) -> Optional[Dict[str, Any]]:
        """獲取當前用戶資訊"""
        try:
            headers = {"Authorization": f"Bearer {token}"}
            response = self.session.get(
                f"{self.base_url}/auth/me",
                headers=headers
            )
            
            if response.ok:
                return response.json()
        except Exception as e:
            st.error(f"獲取用戶資訊失敗: {str(e)}")
        
        return None
    
    def get_rooms(self, include_inactive: bool = False) -> List[Dict[str, Any]]:
        """獲取房間列表"""
        try:
            params = {"include_inactive": include_inactive}
            response = self.session.get(
                f"{self.base_url}/rooms/",
                headers=self._get_headers(),
                params=params
            )
            
            return self._handle_response(response) or []
        except Exception as e:
            st.error(f"獲取房間列表失敗: {str(e)}")
            return []
    
    def create_room(self, room_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """創建新房間"""
        try:
            response = self.session.post(
                f"{self.base_url}/rooms/",
                headers=self._get_headers(),
                json=room_data
            )
            
            return self._handle_response(response)
        except Exception as e:
            st.error(f"創建房間失敗: {str(e)}")
            return None
    
    def get_available_rooms(self) -> List[Dict[str, Any]]:
        """獲取可用房間"""
        try:
            response = self.session.get(
                f"{self.base_url}/rooms/available",
                headers=self._get_headers()
            )
            
            return self._handle_response(response) or []
        except Exception as e:
            st.error(f"獲取可用房間失敗: {str(e)}")
            return []
    
    def get_residents(self, active_only: bool = True) -> List[Dict[str, Any]]:
        """獲取住戶列表"""
        try:
            params = {"active_only": active_only}
            response = self.session.get(
                f"{self.base_url}/residents/",
                headers=self._get_headers(),
                params=params
            )
            
            return self._handle_response(response) or []
        except Exception as e:
            st.error(f"獲取住戶列表失敗: {str(e)}")
            return []
    
    def create_resident(self, resident_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """創建新住戶"""
        try:
            response = self.session.post(
                f"{self.base_url}/residents/",
                headers=self._get_headers(),
                json=resident_data
            )
            
            return self._handle_response(response)
        except Exception as e:
            st.error(f"創建住戶失敗: {str(e)}")
            return None
    
    def move_out_resident(self, resident_id: int, move_out_date: str) -> Optional[Dict[str, Any]]:
        """住戶退房"""
        try:
            response = self.session.post(
                f"{self.base_url}/residents/{resident_id}/move-out",
                headers=self._get_headers(),
                json={"move_out_date": move_out_date}
            )
            
            return self._handle_response(response)
        except Exception as e:
            st.error(f"住戶退房失敗: {str(e)}")
            return None
    
    def get_current_utility_rate(self) -> Optional[Dict[str, Any]]:
        """獲取當前費率"""
        try:
            response = self.session.get(
                f"{self.base_url}/utilities/rates/current",
                headers=self._get_headers()
            )
            
            return self._handle_response(response)
        except Exception as e:
            st.error(f"獲取費率失敗: {str(e)}")
            return None
    
    def create_utility_rate(self, rate_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """創建新費率"""
        try:
            response = self.session.post(
                f"{self.base_url}/utilities/rates",
                headers=self._get_headers(),
                json=rate_data
            )
            
            return self._handle_response(response)
        except Exception as e:
            st.error(f"創建費率失敗: {str(e)}")
            return None
    
    def create_meter_reading(self, reading_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """創建電表抄錄"""
        try:
            response = self.session.post(
                f"{self.base_url}/utilities/readings",
                headers=self._get_headers(),
                json=reading_data
            )
            
            return self._handle_response(response)
        except Exception as e:
            st.error(f"創建抄錄記錄失敗: {str(e)}")
            return None
    
    def get_utility_bills(self, year: int, month: int) -> List[Dict[str, Any]]:
        """獲取費用帳單"""
        try:
            params = {"year": year, "month": month}
            response = self.session.get(
                f"{self.base_url}/utilities/bills",
                headers=self._get_headers(),
                params=params
            )
            
            return self._handle_response(response) or []
        except Exception as e:
            st.error(f"獲取帳單失敗: {str(e)}")
            return []
    
    def update_payment_status(self, bill_id: int, payment_status: str, payment_date: str = None) -> Optional[Dict[str, Any]]:
        """更新付款狀態"""
        try:
            data = {"payment_status": payment_status}
            if payment_date:
                data["payment_date"] = payment_date
            
            response = self.session.put(
                f"{self.base_url}/utilities/bills/{bill_id}/payment",
                headers=self._get_headers(),
                json=data
            )
            
            return self._handle_response(response)
        except Exception as e:
            st.error(f"更新付款狀態失敗: {str(e)}")
            return None
    
    def get_dashboard_stats(self) -> Optional[Dict[str, Any]]:
        """獲取儀表板統計"""
        try:
            response = self.session.get(
                f"{self.base_url}/reports/dashboard",
                headers=self._get_headers()
            )
            
            return self._handle_response(response)
        except Exception as e:
            st.error(f"獲取統計資料失敗: {str(e)}")
            return None
    
    def get_income_summary(self, year: int, month: int) -> Optional[Dict[str, Any]]:
        """獲取收入統計"""
        try:
            params = {"year": year, "month": month}
            response = self.session.get(
                f"{self.base_url}/reports/income-summary",
                headers=self._get_headers(),
                params=params
            )
            
            return self._handle_response(response)
        except Exception as e:
            st.error(f"獲取收入統計失敗: {str(e)}")
            return None

# 全域API客戶端實例
api_client = APIClient()

# =================== frontend/utils.py ===================
import streamlit as st
from datetime import datetime, date
import pandas as pd
from typing import Dict, Any, List

def init_session_state():
    """初始化會話狀態"""
    if 'authenticated' not in st.session_state:
        st.session_state.authenticated = False
    if 'user_info' not in st.session_state:
        st.session_state.user_info = None
    if 'access_token' not in st.session_state:
        st.session_state.access_token = None

def login_user(token: str, user_info: Dict[str, Any]):
    """登入用戶"""
    st.session_state.authenticated = True
    st.session_state.access_token = token
    st.session_state.user_info = user_info

def logout_user():
    """登出用戶"""
    st.session_state.authenticated = False
    st.session_state.access_token = None
    st.session_state.user_info = None

def format_currency(amount: float) -> str:
    """格式化貨幣"""
    return f"${amount:,.0f}"

def format_date(date_str: str) -> str:
    """格式化日期"""
    if not date_str:
        return "N/A"
    try:
        dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
        return dt.strftime("%Y-%m-%d")
    except:
        return date_str

def format_datetime(datetime_str: str) -> str:
    """格式化日期時間"""
    if not datetime_str:
        return "N/A"
    try:
        dt = datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
        return dt.strftime("%Y-%m-%d %H:%M")
    except:
        return datetime_str

def get_room_status_color(status: str) -> str:
    """獲取房間狀態顏色"""
    colors = {
        "available": "🟢",
        "occupied": "🔴",
        "partial": "🟡",
        "maintenance": "🟠"
    }
    return colors.get(status, "⚪")

def get_payment_status_color(status: str) -> str:
    """獲取付款狀態顏色"""
    colors = {
        "paid": "🟢",
        "pending": "🟡",
        "overdue": "🔴"
    }
    return colors.get(status, "⚪")

def validate_id_number(id_number: str) -> bool:
    """驗證身份證號"""
    if not id_number or len(id_number) != 10:
        return False
    
    # 簡單驗證：第一位為英文字母，其餘為數字
    return id_number[0].isalpha() and id_number[1:].isdigit()

def validate_phone_number(phone: str) -> bool:
    """驗證電話號碼"""
    if not phone:
        return True  # 電話號碼可選
    
    # 移除常見分隔符
    clean_phone = phone.replace('-', '').replace(' ', '').replace('(', '').replace(')', '')
    
    # 檢查是否為純數字且長度合理
    return clean_phone.isdigit() and 8 <= len(clean_phone) <= 12

def create_dataframe_from_dict_list(data: List[Dict[str, Any]]) -> pd.DataFrame:
    """從字典列表創建DataFrame"""
    if not data:
        return pd.DataFrame()
    
    return pd.DataFrame(data)

def show_success_message(message: str):
    """顯示成功訊息"""
    st.success(f"✅ {message}")

def show_error_message(message: str):
    """顯示錯誤訊息"""
    st.error(f"❌ {message}")

def show_warning_message(message: str):
    """顯示警告訊息"""
    st.warning(f"⚠️ {message}")

def show_info_message(message: str):
    """顯示資訊訊息"""
    st.info(f"ℹ️ {message}")

# =================== frontend/pages/login.py ===================
import streamlit as st
from api_client import api_client
from utils import init_session_state, login_user

def show_login_page():
    """顯示登入頁面"""
    st.title("🏠 租房管理系統")
    st.markdown("---")
    
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.subheader("用戶登入")
        
        with st.form("login_form"):
            username = st.text_input("用戶名", placeholder="請輸入用戶名")
            password = st.text_input("密碼", type="password", placeholder="請輸入密碼")
            
            submitted = st.form_submit_button("登入", use_container_width=True)
            
            if submitted:
                if not username or not password:
                    st.error("請輸入用戶名和密碼")
                else:
                    with st.spinner("登入中..."):
                        login_result = api_client.login(username, password)
                        
                        if login_result:
                            login_user(
                                login_result['token'],
                                login_result['user_info']
                            )
                            st.success("登入成功！")
                            st.rerun()
        
        st.markdown("---")
        
        # 預設帳號資訊
        st.info("🔑 預設管理員帳號：admin / admin123")
        
        # 系統資訊
        with st.expander("系統資訊"):
            st.markdown("""
            ### 功能特色
            - 🏠 房間管理
            - 👥 住戶管理
            - 💰 費用管理
            - 📊 統計報表
            
            ### 技術架構
            - 後端：FastAPI + SQLAlchemy
            - 前端：Streamlit
            - 資料庫：SQLite
            - 認證：JWT
            """)

# =================== frontend/pages/dashboard.py ===================
import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from api_client import api_client
from utils import format_currency

def show_dashboard_page():
    """顯示儀表板頁面"""
    st.title("📊 系統儀表板")
    
    # 獲取統計資料
    stats = api_client.get_dashboard_stats()
    
    if not stats:
        st.error("無法獲取統計資料")
        return
    
    # 主要統計指標
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            label="總房間數",
            value=stats['total_rooms'],
            delta=None
        )
    
    with col2:
        st.metric(
            label="已住房間",
            value=stats['occupied_rooms'],
            delta=f"{stats['occupied_rooms'] - stats['available_rooms']}"
        )
    
    with col3:
        st.metric(
            label="可用房間",
            value=stats['available_rooms'],
            delta=None
        )
    
    with col4:
        st.metric(
            label="入住率",
            value=f"{stats['occupancy_rate']}%",
            delta=f"{stats['occupancy_rate'] - 50:.1f}%"
        )
    
    st.markdown("---")
    
    # 圖表展示
    col1, col2 = st.columns(2)
    
    with col1:
        # 房間狀態分布餅圖
        fig_rooms = go.Figure(data=[go.Pie(
            labels=['已住房間', '可用房間'],
            values=[stats['occupied_rooms'], stats['available_rooms']],
            hole=0.3,
            marker_colors=['#ff6b6b', '#4ecdc4']
        )])
        
        fig_rooms.update_layout(
            title="房間狀態分布",
            annotations=[dict(text='房間狀態', x=0.5, y=0.5, font_size=20, showarrow=False)]
        )
        
        st.plotly_chart(fig_rooms, use_container_width=True)
    
    with col2:
        # 入住率指標
        fig_occupancy = go.Figure(go.Indicator(
            mode="gauge+number+delta",
            value=stats['occupancy_rate'],
            domain={'x': [0, 1], 'y': [0, 1]},
            title={'text': "入住率 (%)"},
            delta={'reference': 80},
            gauge={
                'axis': {'range': [None, 100]},
                'bar': {'color': "darkblue"},
                'steps': [
                    {'range': [0, 50], 'color': "lightgray"},
                    {'range': [50, 80], 'color': "gray"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 90
                }
            }
        ))
        
        fig_occupancy.update_layout(height=400)
        st.plotly_chart(fig_occupancy, use_container_width=True)
    
    # 當月收入統計
    st.subheader("當月收入統計")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric(
            label="當月收入",
            value=format_currency(stats['monthly_income']),
            delta=None
        )
    
    with col2:
        st.metric(
            label="住戶總數",
            value=stats['total_residents'],
            delta=None
        )
    
    with col3:
        avg_income = stats['monthly_income'] / stats['total_residents'] if stats['total_residents'] > 0 else 0
        st.metric(
            label="平均收入",
            value=format_currency(avg_income),
            delta=None
        )
    
    # 快速操作
    st.subheader("快速操作")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("🏠 新增房間", use_container_width=True):
            st.session_state.page = "rooms"
            st.rerun()
    
    with col2:
        if st.button("👥 新增住戶", use_container_width=True):
            st.session_state.page = "residents"
            st.rerun()
    
    with col3:
        if st.button("💰 費用管理", use_container_width=True):
            st.session_state.page = "utilities"
            st.rerun()
    
    with col4:
        if st.button("📊 查看報表", use_container_width=True):
            st.session_state.page = "reports"
            st.rerun()

# =================== frontend/pages/rooms.py ===================
import streamlit as st
import pandas as pd
from api_client import api_client
from utils import format_currency, get_room_status_color, show_success_message, show_error_message

def show_rooms_page():
    """顯示房間管理頁面"""
    st.title("🏠 房間管理")
    
    # 頁籤設計
    tab1, tab2, tab3 = st.tabs(["房間列表", "新增房間", "房間統計"])
    
    with tab1:
        show_rooms_list()
    
    with tab2:
        show_create_room_form()
    
    with tab3:
        show_rooms_statistics()

def show_rooms_list():
    """顯示房間列表"""
    st.subheader("房間列表")
    
    # 搜尋和篩選
    col1, col2, col3 = st.columns([2, 1, 1])
    
    with col1:
        search_term = st.text_input("🔍 搜尋房間號", placeholder="輸入房間號")
    
    with col2:
        status_filter = st.selectbox("狀態篩選", ["全部", "可用", "已住", "部分住", "維護中"])
    
    with col3:
        if st.button("🔄 刷新數據"):
            st.rerun()
    
    # 獲取房間數據
    rooms = api_client.get_rooms()
    
    if not rooms:
        st.info("暫無房間資料")
        return
    
    # 數據篩選
    filtered_rooms = rooms
    
    if search_term:
        filtered_rooms = [r for r in filtered_rooms if search_term.lower() in r['room_number'].lower()]
    
    if status_filter != "全部":
        status_map = {
            "可用": "available",
            "已住": "occupied", 
            "部分住": "partial",
            "維護中": "maintenance"
        }
        filtered_rooms = [r for r in filtered_rooms if r['status'] == status_map[status_filter]]
    
    # 數據表格
    if filtered_rooms:
        # 準備顯示數據
        display_data = []
        for room in filtered_rooms:
            display_data.append({
                "房間號": room['room_number'],
                "樓層": room['floor'] or 'N/A',
                "坪數": room['area'] or 'N/A',
                "單人租金": format_currency(room['rent_single']),
                "雙人租金": format_currency(room['rent_double']),
                "目前住戶": f"{room['current_occupants']}/{room['max_occupants']}",
                "狀態": f"{get_room_status_color(room['status'])} {room['status']}",
                "現在租金": format_currency(room['current_rent'])
            })
        
        df = pd.DataFrame(display_data)
        st.dataframe(df, use_container_width=True)
        
        # 房間操作
        st.subheader("房間操作")
        
        selected_room_idx = st.selectbox(
            "選擇房間",
            options=range(len(filtered_rooms)),
            format_func=lambda x: filtered_rooms[x]['room_number']
        )
        
        if selected_room_idx is not None:
            selected_room = filtered_rooms[selected_room_idx]
            
            col1, col2 = st.columns(2)
            
            with col1:
                if st.button("📋 查看詳情", use_container_width=True):
                    show_room_details(selected_room)
            
            with col2:
                if st.button("✏️ 編輯房間", use_container_width=True):
                    st.info("編輯功能開發中...")
    else:
        st.info("沒有符合條件的房間")

def show_create_room_form():
    """顯示新增房間表單"""
    st.subheader("新增房間")
    
    with st.form("create_room_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            room_number = st.text_input("房間號*", placeholder="例如: A101")
            floor = st.number_input("樓層", min_value=1, max_value=50, value=1)
            area = st.number_input("坪數", min_value=0.0, value=0.0, step=0.1)
        
        with col2:
            rent_single = st.number_input("單人租金*", min_value=0, value=10000, step=100)
            rent_double = st.number_input("雙人租金*", min_value=0, value=15000, step=100)
        
        description = st.text_area("房間描述", placeholder="房間設施、特色等")
        
        submitted = st.form_submit_button("🏠 創建房間", use_container_width=True)
        
        if submitted:
            if not room_number:
                show_error_message("請輸入房間號")
            elif rent_single <= 0 or rent_double <= 0:
                show_error_message("租金必須大於0")
            elif rent_double < rent_single:
                show_error_message("雙人租金應該大於等於單人租金")
            else:
                room_data = {
                    "room_number": room_number,
                    "floor": floor,
                    "area": area if area > 0 else None,
                    "rent_single": rent_single,
                    "rent_double": rent_double,
                    "description": description if description else None
                }
                
                with st.spinner("創建中..."):
                    result = api_client.create_room(room_data)
                    
                    if result:
                        show_success_message(f"房間 {room_number} 創建成功！")
                        st.rerun()

def show_room_details(room_data):
    """顯示房間詳情"""
    st.subheader(f"房間 {room_data['room_number']} 詳情")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.metric("目前住戶", f"{room_data['current_occupants']}/{room_data['max_occupants']}")
        st.metric("樓層", room_data['floor'] or 'N/A')
        st.metric("坪數", room_data['area'] or 'N/A')
    
    with col2:
        st.metric("單人租金", format_currency(room_data['rent_single']))
        st.metric("雙人租金", format_currency(room_data['rent_double']))
        st.metric("現在租金", format_currency(room_data['current_rent']))
    
    if room_data.get('description'):
        st.text_area("房間描述", value=room_data['description'], disabled=True)

def show_rooms_statistics():
    """顯示房間統計"""
    st.subheader("房間統計")
    
    rooms = api_client.get_rooms()
    
    if not rooms:
        st.info("暫無統計資料")
        return
    
    # 統計指標
    total_rooms = len(rooms)
    available_rooms = len([r for r in rooms if r['status'] == 'available'])
    occupied_rooms = len([r for r in rooms if r['status'] == 'occupied'])
    partial_rooms = len([r for r in rooms if r['status'] == 'partial'])
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("總房間數", total_rooms)
    
    with col2:
        st.metric("可用房間", available_rooms)
    
    with col3:
        st.metric("已滿房間", occupied_rooms)
    
    with col4:
        st.metric("部分住宿", partial_rooms)
    
    # 圖表展示
    import plotly.express as px
    
    # 房間狀態分布
    status_counts = {}
    for room in rooms:
        status = room['status']
        status_counts[status] = status_counts.get(status, 0) + 1
    
    if status_counts:
        fig = px.pie(
            values=list(status_counts.values()),
            names=list(status_counts.keys()),
            title="房間狀態分布"
        )
        st.plotly_chart(fig, use_container_width=True)
    
    # 租金統計
    rent_data = []
    for room in rooms:
        rent_data.append({
            "房間號": room['room_number'],
            "單人租金": room['rent_single'],
            "雙人租金": room['rent_double']
        })
    
    if rent_data:
        df = pd.DataFrame(rent_data)
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.metric("平均單人租金", format_currency(df['單人租金'].mean()))
            st.metric("最高單人租金", format_currency(df['單人租金'].max()))
        
        with col2:
            st.metric("平均雙人租金", format_currency(df['雙人租金'].mean()))
            st.metric("最高雙人租金", format_currency(df['雙人租金'].max()))

# =================== frontend/app.py ===================
import streamlit as st
from config import config
from utils import init_session_state, logout_user
from pages.login import show_login_page
from pages.dashboard import show_dashboard_page
from pages.rooms import show_rooms_page

# 頁面配置
st.set_page_config(**config.PAGE_CONFIG)

# 初始化會話狀態
init_session_state()

def show_sidebar():
    """顯示側邊欄導航"""
    with st.sidebar:
        st.title(f"{config.APP_ICON} {config.APP_TITLE}")
        
        if st.session_state.authenticated:
            st.success(f"歡迎, {st.session_state.user_info['username']}")
            st.caption(f"角色: {st.session_state.user_info['role']}")
            
            # 導航選單
            st.markdown("### 📋 功能選單")
            
            pages = {
                "📊 儀表板": "dashboard",
                "🏠 房間管理": "rooms",
                "👥 住戶管理": "residents",
                "💰 費用管理": "utilities",
                "📈 報表統計": "reports"
            }
            
            # 設定預設頁面
            if 'page' not in st.session_state:
                st.session_state.page = "dashboard"
            
            # 頁面按鈕
            for page_name, page_key in pages.items():
                if st.button(page_name, use_container_width=True):
                    st.session_state.page = page_key
                    st.rerun()
            
            st.markdown("---")
            
            # 登出按鈕
            if st.button("🚪 登出", use_container_width=True):
                logout_user()
                st.rerun()
            
            # 系統資訊
            with st.expander("ℹ️ 系統資訊"):
                st.markdown(f"""
                **版本**: 1.0.0  
                **狀態**: 運行中 🟢  
                **API**: 已連接  
                **用戶**: {st.session_state.user_info['username']}  
                **角色**: {st.session_state.user_info['role']}
                """)
            
            return st.session_state.page
        else:
            st.info("請先登入系統")
            return "login"

def main():
    """主程式"""
    # 顯示導航
    current_page = show_sidebar()
    
    # 路由處理
    if current_page == "login":
        show_login_page()
    elif current_page == "dashboard":
        show_dashboard_page()
    elif current_page == "rooms":
        show_rooms_page()
    elif current_page == "residents":
        # 住戶管理頁面（需要實作）
        st.title("👥 住戶管理")
        st.info("住戶管理功能開發中...")
    elif current_page == "utilities":
        # 費用管理頁面（需要實作）
        st.title("💰 費用管理")
        st.info("費用管理功能開發中...")
    elif current_page == "reports":
        # 報表統計頁面（需要實作）
        st.title("📈 報表統計")
        st.info("報表統計功能開發中...")
    else:
        st.error("頁面不存在")

if __name__ == "__main__":
    main()

# =================== frontend/run_frontend.py ===================
import subprocess
import sys

def run_streamlit():
    """啟動Streamlit前端"""
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "app.py",
            "--server.port", "8501",
            "--server.headless", "true"
        ])
    except KeyboardInterrupt:
        print("\n前端服務已停止")
    except Exception as e:
        print(f"啟動前端失敗: {e}")

if __name__ == "__main__":
    run_streamlit()