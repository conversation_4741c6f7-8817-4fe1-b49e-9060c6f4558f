#!/usr/bin/env python3
"""測試面積 N/A 處理修正"""

import requests
import sqlite3
import sys
import os
from datetime import date

def create_na_area_room_in_db():
    """在資料庫中創建一個 N/A 面積的房間"""
    print("🔧 在資料庫中創建 N/A 面積房間...")
    
    try:
        # 連接到 backend 資料庫
        conn = sqlite3.connect('backend/rental_management.db')
        cursor = conn.cursor()
        
        # 插入一個 NULL 面積的房間（模擬 N/A 情況）
        cursor.execute("""
            INSERT INTO rooms (room_number, floor, area, rent_single, rent_double, description, status, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            'TEST_NA_AREA_FIX',  # room_number
            1,                   # floor
            None,                # area (NULL，模擬 N/A)
            8000,                # rent_single
            12000,               # rent_double
            '測試 N/A 面積修正',   # description
            'available',         # status
            True                 # is_active
        ))
        
        room_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        print(f"✅ 成功創建 N/A 面積房間，ID: {room_id}")
        return room_id
        
    except Exception as e:
        print(f"❌ 創建 N/A 面積房間失敗: {e}")
        return None

def test_backend_area_handling(room_id):
    """測試後端面積處理"""
    print(f"\n🏠 測試後端面積處理 (房間 ID: {room_id})...")
    
    base_url = 'http://localhost:8080'
    login_data = {
        'username': 'admin',
        'password': 'admin5813'
    }
    
    try:
        # 登入
        response = requests.post(f'{base_url}/auth/login', data=login_data)
        if response.status_code != 200:
            print(f'❌ 登入失敗: {response.status_code}')
            return False
            
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        # 測試獲取房間詳情
        print("   測試獲取房間詳情...")
        response = requests.get(f'{base_url}/rooms/{room_id}', headers=headers)
        
        if response.status_code == 200:
            room = response.json()
            area = room.get('area')
            
            print(f"   房間號: {room.get('room_number')}")
            print(f"   面積值: {area}")
            print(f"   面積類型: {type(area)}")
            
            # 檢查面積是否為 0.0 而不是 None
            if area == 0.0:
                print("   ✅ 面積正確處理為 0.0")
                return True
            elif area is None:
                print("   ❌ 面積仍為 None，未正確處理")
                return False
            else:
                print(f"   ⚠️  面積為其他值: {area}")
                return True
        else:
            print(f"   ❌ 獲取房間詳情失敗: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 後端面積處理測試錯誤: {e}")
        return False

def test_room_list_api():
    """測試房間列表 API"""
    print("\n📋 測試房間列表 API...")
    
    base_url = 'http://localhost:8080'
    login_data = {
        'username': 'admin',
        'password': 'admin5813'
    }
    
    try:
        # 登入
        response = requests.post(f'{base_url}/auth/login', data=login_data)
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        # 測試房間列表
        response = requests.get(f'{base_url}/rooms/', headers=headers)
        
        if response.status_code == 200:
            rooms = response.json()
            print(f"   ✅ 房間列表 API 正常，返回 {len(rooms)} 個房間")
            
            # 檢查是否有面積為 None 的房間
            none_area_rooms = [r for r in rooms if r.get('area') is None]
            zero_area_rooms = [r for r in rooms if r.get('area') == 0.0]
            
            print(f"   面積為 None 的房間數: {len(none_area_rooms)}")
            print(f"   面積為 0.0 的房間數: {len(zero_area_rooms)}")
            
            if len(none_area_rooms) == 0:
                print("   ✅ 沒有面積為 None 的房間，處理正確")
                return True
            else:
                print("   ❌ 仍有面積為 None 的房間")
                for room in none_area_rooms:
                    print(f"      房間 {room.get('room_number')}: area = {room.get('area')}")
                return False
        else:
            print(f"   ❌ 房間列表 API 錯誤: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 房間列表 API 測試錯誤: {e}")
        return False

def test_room_update_api(room_id):
    """測試房間更新 API"""
    print(f"\n🔄 測試房間更新 API (房間 ID: {room_id})...")
    
    base_url = 'http://localhost:8080'
    login_data = {
        'username': 'admin',
        'password': 'admin5813'
    }
    
    try:
        # 登入
        response = requests.post(f'{base_url}/auth/login', data=login_data)
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        # 測試更新房間（不改變面積）
        print("   測試更新房間描述...")
        update_data = {
            "description": "已測試 N/A 面積修正"
        }
        
        response = requests.put(f'{base_url}/rooms/{room_id}', json=update_data, headers=headers)
        
        if response.status_code == 200:
            updated_room = response.json()
            area = updated_room.get('area')
            
            print(f"   ✅ 房間更新成功")
            print(f"   更新後面積: {area}")
            
            if area == 0.0:
                print("   ✅ 更新後面積仍正確為 0.0")
                return True
            else:
                print(f"   ⚠️  更新後面積為: {area}")
                return True
        else:
            print(f"   ❌ 房間更新失敗: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 房間更新 API 測試錯誤: {e}")
        return False

def test_frontend_comparison_safety():
    """測試前端比較操作安全性"""
    print("\n🖥️ 測試前端比較操作安全性...")
    
    try:
        # 模擬前端可能遇到的情況
        test_cases = [
            {"area": None, "description": "None 值"},
            {"area": 0.0, "description": "0.0 值"},
            {"area": 10.5, "description": "正常值"},
            {"area": -5.0, "description": "負數值"}
        ]
        
        for case in test_cases:
            area = case["area"]
            desc = case["description"]
            
            print(f"   測試 {desc}: area = {area}")
            
            try:
                # 模擬前端比較操作
                if area is not None and area < 0:
                    result = "負數警告"
                elif area == 0:
                    result = "零值提示"
                else:
                    result = "正常"
                
                print(f"      ✅ 比較操作安全: {result}")
                
            except TypeError as e:
                print(f"      ❌ 比較操作錯誤: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 前端比較操作測試錯誤: {e}")
        return False

def cleanup_test_room(room_id):
    """清理測試房間"""
    print(f"\n🗑️ 清理測試房間 (ID: {room_id})...")
    
    base_url = 'http://localhost:8080'
    login_data = {
        'username': 'admin',
        'password': 'admin5813'
    }
    
    try:
        # 登入
        response = requests.post(f'{base_url}/auth/login', data=login_data)
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        # 刪除房間
        response = requests.delete(f'{base_url}/rooms/{room_id}', headers=headers)
        if response.status_code == 200:
            print("✅ 測試房間已清理")
        else:
            print(f"⚠️  清理測試房間失敗: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 清理錯誤: {e}")

if __name__ == "__main__":
    print("🧪 面積 N/A 處理修正測試")
    print("=" * 60)
    
    # 1. 創建 N/A 面積房間
    room_id = create_na_area_room_in_db()
    
    if room_id:
        # 2. 測試後端面積處理
        backend_test = test_backend_area_handling(room_id)
        
        # 3. 測試房間列表 API
        list_test = test_room_list_api()
        
        # 4. 測試房間更新 API
        update_test = test_room_update_api(room_id)
        
        # 5. 清理測試房間
        cleanup_test_room(room_id)
    else:
        backend_test = False
        list_test = False
        update_test = False
    
    # 6. 測試前端比較操作安全性
    frontend_test = test_frontend_comparison_safety()
    
    print("\n" + "=" * 60)
    print("📝 測試結果總結:")
    print("1. ✅ 後端面積處理" if backend_test else "1. ❌ 後端面積處理")
    print("2. ✅ 房間列表 API" if list_test else "2. ❌ 房間列表 API")
    print("3. ✅ 房間更新 API" if update_test else "3. ❌ 房間更新 API")
    print("4. ✅ 前端比較操作安全性" if frontend_test else "4. ❌ 前端比較操作安全性")
    
    all_passed = all([backend_test, list_test, update_test, frontend_test])
    
    print("\n💡 修正說明:")
    print("🔧 後端修正:")
    print("   - Room.to_dict() 方法：area 為 None 時返回 0.0")
    print("   - 確保 API 響應中面積始終為數字類型")
    
    print("\n🖥️ 前端修正:")
    print("   - 房間編輯表單：安全處理 None 面積值")
    print("   - 比較操作：添加 None 檢查避免 TypeError")
    print("   - 驗證邏輯：確保比較前檢查數據類型")
    
    print("\n🎯 修正效果:")
    print("   - 解決 TypeError: '<' not supported between instances of 'NoneType' and 'int'")
    print("   - 編輯 N/A 面積房間時不再出錯")
    print("   - 後端讀取資料時直接用 0 取代 None")
    
    print(f"\n🏁 測試完成 - {'全部通過' if all_passed else '部分失敗'}")
