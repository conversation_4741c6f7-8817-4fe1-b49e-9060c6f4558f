import streamlit as st
import pandas as pd
from datetime import datetime, date, timedelta
from api_client import api_client
from utils import show_success_message, show_error_message, show_info_message, format_currency
from date_utils import format_date_roc, roc_calendar_input

def show_comprehensive_bills_page():
    """顯示綜合帳單管理頁面"""
    st.title("💰 綜合帳單管理")
    
    # 頁籤設計
    tab1, tab2, tab3, tab4 = st.tabs(["帳單列表", "創建帳單", "月度摘要", "帳單統計"])
    
    with tab1:
        show_bills_list()
    
    with tab2:
        show_create_bill_form()
    
    with tab3:
        show_monthly_summary()
    
    with tab4:
        show_bills_statistics()

def show_bills_list():
    """顯示綜合帳單列表"""
    st.subheader("📋 綜合帳單列表")
    
    # 篩選條件
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        filter_year = st.selectbox(
            "篩選年份",
            options=[None] + list(range(2020, 2031)),
            format_func=lambda x: "全部年份" if x is None else str(x),
            key="bills_filter_year"
        )
    
    with col2:
        filter_month = st.selectbox(
            "篩選月份",
            options=[None] + list(range(1, 13)),
            format_func=lambda x: "全部月份" if x is None else f"{x}月",
            key="bills_filter_month"
        )
    
    with col3:
        # 獲取房間列表
        rooms = api_client.get_rooms()
        room_options = [None] + (rooms if rooms else [])
        filter_room = st.selectbox(
            "篩選房間",
            options=room_options,
            format_func=lambda x: "全部房間" if x is None else f"{x['room_number']}",
            key="bills_filter_room"
        )
    
    with col4:
        filter_status = st.selectbox(
            "付款狀態",
            options=[None, "pending", "paid", "overdue"],
            format_func=lambda x: {
                None: "全部狀態",
                "pending": "待付款",
                "paid": "已付款", 
                "overdue": "逾期"
            }.get(x, x),
            key="bills_filter_status"
        )
    
    # 獲取帳單列表
    bills = api_client.get_comprehensive_bills(
        year=filter_year,
        month=filter_month,
        room_id=filter_room['id'] if filter_room else None,
        payment_status=filter_status
    )
    
    if bills:
        # 顯示帳單列表
        for i, bill in enumerate(bills, 1):
            with st.expander(f"📄 {bill['room']['room_number']} - {bill['billing_year']}年{bill['billing_month']}月 - {format_currency(bill['total_amount'])}"):
                show_bill_details(bill, i)
    else:
        st.info("📝 目前沒有符合條件的綜合帳單")

def show_bill_details(bill, index):
    """顯示帳單詳情"""
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**基本資訊**")
        st.write(f"🏠 房間：{bill['room']['room_number']}")
        st.write(f"📅 計費期間：{bill['billing_year']}年{bill['billing_month']}月")
        st.write(f"👥 住戶數量：{bill['occupant_count']}人")
        
        # 付款狀態
        status_color = {
            "pending": "🟡",
            "paid": "🟢",
            "overdue": "🔴"
        }
        status_text = {
            "pending": "待付款",
            "paid": "已付款",
            "overdue": "逾期"
        }
        st.write(f"💳 付款狀態：{status_color.get(bill['payment_status'], '⚪')} {status_text.get(bill['payment_status'], bill['payment_status'])}")
        
        if bill['payment_date']:
            st.write(f"💰 付款日期：{format_date_roc(bill['payment_date'], 'full')}")
        
        st.write(f"⏰ 到期日：{format_date_roc(bill['due_date'], 'full')}")
    
    with col2:
        st.write("**費用明細**")
        st.write(f"🏠 租金：{format_currency(bill['rent_amount'])}")
        st.write(f"💧 水費：{format_currency(bill['water_fee'])}")
        st.write(f"⚡ 電費：{format_currency(bill['electricity_cost'])}")
        st.write(f"📊 電量：{bill['electricity_usage']:.2f} 度")
        st.write(f"💲 電費費率：${bill['electricity_rate']:.2f}/度")
        st.markdown(f"**💰 總金額：{format_currency(bill['total_amount'])}**")
    
    # 操作按鈕
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if bill['payment_status'] != 'paid':
            if st.button(f"✅ 標記已付款", key=f"mark_paid_{bill['id']}_{index}"):
                result = api_client.update_payment_status(
                    bill['id'], 
                    'paid', 
                    date.today().isoformat()
                )
                if result:
                    show_success_message("付款狀態已更新為已付款")
                    st.rerun()
    
    with col2:
        if bill['payment_status'] == 'paid':
            if st.button(f"🔄 標記待付款", key=f"mark_pending_{bill['id']}_{index}"):
                result = api_client.update_payment_status(bill['id'], 'pending')
                if result:
                    show_success_message("付款狀態已更新為待付款")
                    st.rerun()
    
    with col3:
        if st.button(f"📄 匯出帳單", key=f"export_{bill['id']}_{index}"):
            export_bill_pdf(bill)

def show_create_bill_form():
    """顯示創建帳單表單"""
    st.subheader("📝 創建綜合帳單")
    
    with st.form("create_comprehensive_bill"):
        col1, col2 = st.columns(2)
        
        with col1:
            # 獲取房間列表
            rooms = api_client.get_rooms()
            if not rooms:
                st.error("❌ 無法獲取房間列表")
                return
            
            room = st.selectbox(
                "選擇房間*",
                options=rooms,
                format_func=lambda x: f"{x['room_number']} ({x['current_occupants']}人)",
                key="create_bill_room"
            )
            
            billing_year = st.number_input(
                "計費年份*",
                min_value=2020,
                max_value=2030,
                value=datetime.now().year,
                key="create_bill_year"
            )
            
            billing_month = st.number_input(
                "計費月份*",
                min_value=1,
                max_value=12,
                value=datetime.now().month,
                key="create_bill_month"
            )
        
        with col2:
            current_electricity_reading = st.number_input(
                "當前電表讀數*",
                min_value=0.0,
                step=0.1,
                help="請輸入當前的電表讀數",
                key="create_bill_reading"
            )
            
            due_date = roc_calendar_input(
                "到期日",
                value=date.today() + timedelta(days=30),
                help="帳單到期日，預設為30天後",
                key="create_bill_due_date"
            )
            
            notes = st.text_area(
                "備註",
                placeholder="請輸入備註（選填）",
                key="create_bill_notes"
            )
        
        # 預覽計算
        if room and current_electricity_reading > 0:
            st.markdown("---")
            st.write("**費用預覽**")
            
            # 獲取當前費率
            rate = api_client.get_current_utility_rate()
            if rate:
                # 計算租金
                if room['current_occupants'] == 2:
                    rent_amount = room['rent_double']
                else:
                    rent_amount = room['rent_single']
                
                # 獲取上次讀數（簡化處理，這裡假設為0）
                previous_reading = 0  # 實際應該從API獲取
                electricity_usage = current_electricity_reading - previous_reading
                electricity_cost = electricity_usage * rate['electricity_rate']
                water_fee = 100.0
                total_amount = rent_amount + water_fee + electricity_cost
                
                col1, col2, col3, col4 = st.columns(4)
                with col1:
                    st.metric("租金", format_currency(rent_amount))
                with col2:
                    st.metric("水費", format_currency(water_fee))
                with col3:
                    st.metric("電費", format_currency(electricity_cost))
                with col4:
                    st.metric("總金額", format_currency(total_amount))
        
        # 提交按鈕
        submitted = st.form_submit_button("💰 創建綜合帳單", use_container_width=True)
        
        if submitted:
            if not room:
                show_error_message("請選擇房間")
            elif current_electricity_reading <= 0:
                show_error_message("請輸入有效的電表讀數")
            else:
                with st.spinner("創建中..."):
                    result = api_client.create_comprehensive_bill(
                        room_id=room['id'],
                        billing_year=int(billing_year),
                        billing_month=int(billing_month),
                        current_electricity_reading=current_electricity_reading,
                        due_date=due_date.isoformat() if due_date else None,
                        notes=notes if notes else None
                    )
                    
                    if result:
                        show_success_message(f"綜合帳單創建成功！總金額：{format_currency(result['bill']['total_amount'])}")
                        st.rerun()

def show_monthly_summary():
    """顯示月度摘要"""
    st.subheader("📊 月度摘要")
    
    col1, col2 = st.columns(2)
    
    with col1:
        summary_year = st.number_input(
            "年份",
            min_value=2020,
            max_value=2030,
            value=datetime.now().year,
            key="summary_year"
        )
    
    with col2:
        summary_month = st.number_input(
            "月份",
            min_value=1,
            max_value=12,
            value=datetime.now().month,
            key="summary_month"
        )
    
    if st.button("📊 獲取摘要", use_container_width=True):
        summary = api_client.get_monthly_summary(summary_year, summary_month)
        
        if summary:
            summary_data = summary['summary']
            
            st.markdown(f"### {summary_year}年{summary_month}月 綜合帳單摘要")
            
            # 總覽指標
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("總帳單數", summary_data['total_bills'])
            
            with col2:
                st.metric("總金額", format_currency(summary_data['total_amount']))
            
            with col3:
                st.metric("已付款", summary_data['paid_bills'])
            
            with col4:
                st.metric("已收金額", format_currency(summary_data['paid_amount']))
            
            # 詳細分析
            col1, col2 = st.columns(2)
            
            with col1:
                st.write("**收入分析**")
                st.write(f"🏠 租金收入：{format_currency(summary_data['total_rent'])}")
                st.write(f"💧 水費收入：{format_currency(summary_data['total_water_fee'])}")
                st.write(f"⚡電費收入：{format_currency(summary_data['total_electricity_cost'])}")
            
            with col2:
                st.write("**付款狀況**")
                payment_rate = (summary_data['paid_bills'] / summary_data['total_bills'] * 100) if summary_data['total_bills'] > 0 else 0
                st.write(f"💳 付款率：{payment_rate:.1f}%")
                st.write(f"💰 已收金額：{format_currency(summary_data['paid_amount'])}")
                st.write(f"⏳ 未收金額：{format_currency(summary_data['unpaid_amount'])}")

def show_bills_statistics():
    """顯示帳單統計"""
    st.subheader("📈 帳單統計")
    st.info("📊 統計功能開發中...")

def export_bill_pdf(bill):
    """匯出帳單PDF"""
    try:
        # 這裡可以實作PDF匯出功能
        # 暫時顯示帳單資訊
        st.success("📄 帳單匯出功能開發中...")
        
        # 顯示可複製的帳單資訊
        bill_text = f"""
綜合帳單
==================
房間：{bill['room']['room_number']}
計費期間：{bill['billing_year']}年{bill['billing_month']}月
住戶數量：{bill['occupant_count']}人

費用明細：
- 租金：{format_currency(bill['rent_amount'])}
- 水費：{format_currency(bill['water_fee'])}
- 電費：{format_currency(bill['electricity_cost'])} ({bill['electricity_usage']:.2f}度 × ${bill['electricity_rate']:.2f}/度)

總金額：{format_currency(bill['total_amount'])}
到期日：{format_date_roc(bill['due_date'], 'full')}
付款狀態：{bill['payment_status']}
        """
        
        st.text_area("帳單內容（可複製）", bill_text, height=300)
        
    except Exception as e:
        show_error_message(f"匯出失敗：{str(e)}")

if __name__ == "__main__":
    show_comprehensive_bills_page()
