#!/usr/bin/env python3
"""測試負數面積驗證"""

import requests

def test_negative_area():
    """測試負數面積驗證"""
    base_url = 'http://localhost:8080'
    login_data = {'username': 'admin', 'password': 'admin5813'}

    # 登入
    response = requests.post(f'{base_url}/auth/login', data=login_data)
    token = response.json()['access_token']
    headers = {'Authorization': f'Bearer {token}'}

    # 測試負數面積
    negative_room_data = {
        'room_number': 'TEST_NEG_AREA',
        'floor': 1,
        'area': -1.0,
        'rent_single': 8000,
        'rent_double': 12000,
        'description': '測試負數面積'
    }

    response = requests.post(f'{base_url}/rooms/', json=negative_room_data, headers=headers)
    print(f'負數面積測試狀態碼: {response.status_code}')
    if response.status_code == 422:
        print('✅ 正確阻止負數面積')
        print(f'錯誤詳情: {response.json()}')
    else:
        print(f'⚠️  預期 422，實際 {response.status_code}')
        print(f'響應: {response.text}')

if __name__ == "__main__":
    test_negative_area()
