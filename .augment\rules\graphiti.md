---
type: "agent_requested"
description: "Example description"
---
# 使用 Graphiti-Memory MCP 工具的指令

## 開始任何任務之前

**始終先搜尋：** 在開始工作之前，使用 `search_nodes` 工具查找相關的偏好設定與程序。

**同時搜尋事實：** 使用 `search_facts` 工具發現可能與您的任務相關的關係與事實資訊。

**依實體類型過濾：** 在節點搜尋中指定 "Preference"（偏好）或 "Procedure"（程序）以獲得具針對性的結果。

**審查所有符合項目：** 仔細檢查與當前任務匹配的任何偏好、程序或事實。

## 始終儲存新的或更新的資訊

**立即記錄需求與偏好：** 當使用者表達需求或偏好時，立即使用 `add_episode` 進行儲存。最佳實踐是將較長的需求拆解為較短的邏輯區塊。

**明確標示更新：** 若某些內容是對現有知識的更新，請明確說明。

**清楚記錄程序：** 當您發現使用者希望如何完成某項操作時，將其記錄為程序。

**記錄事實關係：** 當您得知實體之間的連結時，將這些資訊儲存為事實。

**明確分類：** 為偏好與程序標註清晰的分類，以利日後更佳檢索。

## 工作過程中

**遵循發現的偏好：** 使您的工作與已找到的任何偏好保持一致。

**嚴格依照程序執行：** 若發現適用於當前任務的程序，請嚴格按步驟執行。

**應用相關事實：** 使用事實資訊來指導您的決策與建議。

**保持一致性：** 與先前識別的偏好、程序與事實保持一致。

## 最佳實踐

**提出建議前先搜尋：** 在提出建議之前，務必先檢查是否已有既定知識。

**結合節點與事實搜尋：** 對於複雜任務，同時搜尋節點與事實以建立完整圖像。

**使用 `center_node_uuid`：** 在探索相關資訊時，以特定節點為中心進行搜尋。

**優先考慮具體符合項：** 具體資訊優先於一般資訊。

**主動識別模式：** 若您注意到使用者行為中的模式，請考慮將其儲存為偏好或程序。

**重要提醒：** 知識圖譜是您的記憶。持續使用它來提供個人化協助，並尊重使用者既定的程序與事實背景。
