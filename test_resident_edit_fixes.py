#!/usr/bin/env python3
"""測試住戶編輯修正功能"""

import requests
import sys
import os
from datetime import date, datetime, timedelta

def test_resident_update_with_lease_date():
    """測試住戶更新包含租約到期日期"""
    print("📝 測試住戶更新租約到期日期...")
    
    base_url = 'http://localhost:8080'
    login_data = {
        'username': 'admin',
        'password': 'admin5813'
    }
    
    try:
        # 登入
        response = requests.post(f'{base_url}/auth/login', data=login_data)
        if response.status_code != 200:
            print(f'❌ 登入失敗: {response.status_code}')
            return False
            
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        # 獲取活躍住戶
        response = requests.get(f'{base_url}/residents?active_only=true', headers=headers)
        if response.status_code != 200:
            print("❌ 無法獲取住戶列表")
            return False
            
        residents = response.json()
        if not residents:
            print("❌ 沒有活躍住戶")
            return False
        
        # 使用第一個住戶
        test_resident = residents[0]
        resident_id = test_resident['id']
        original_lease_end = test_resident.get('lease_end_date')
        
        print(f"   使用住戶: {test_resident['name']} (ID: {resident_id})")
        print(f"   原始租約到期日: {original_lease_end}")
        
        # 測試1: 設定新的租約到期日期
        print("   測試設定新的租約到期日期...")
        new_lease_end_date = (date.today() + timedelta(days=365)).isoformat()
        
        update_data = {
            "name": test_resident['name'],
            "phone": test_resident.get('phone'),
            "emergency_contact": test_resident.get('emergency_contact'),
            "emergency_phone": test_resident.get('emergency_phone'),
            "deposit": test_resident.get('deposit', 0.0),
            "lease_end_date": new_lease_end_date
        }
        
        response = requests.put(f'{base_url}/residents/{resident_id}', 
                              json=update_data, headers=headers)
        
        if response.status_code == 200:
            updated_resident = response.json()
            print(f"   ✅ 住戶資訊更新成功")
            print(f"      新租約到期日: {updated_resident.get('lease_end_date')}")
        else:
            print(f"   ❌ 住戶資訊更新失敗: {response.status_code} - {response.text}")
            return False
        
        # 測試2: 清除租約到期日期
        print("   測試清除租約到期日期...")
        clear_data = {
            "name": test_resident['name'],
            "phone": test_resident.get('phone'),
            "emergency_contact": test_resident.get('emergency_contact'),
            "emergency_phone": test_resident.get('emergency_phone'),
            "deposit": test_resident.get('deposit', 0.0),
            "lease_end_date": None
        }
        
        response = requests.put(f'{base_url}/residents/{resident_id}', 
                              json=clear_data, headers=headers)
        
        if response.status_code == 200:
            cleared_resident = response.json()
            print(f"   ✅ 租約到期日清除成功")
            print(f"      租約到期日: {cleared_resident.get('lease_end_date')}")
        else:
            print(f"   ❌ 租約到期日清除失敗: {response.status_code} - {response.text}")
            return False
        
        # 恢復原始租約到期日
        print("   恢復原始租約到期日...")
        restore_data = {
            "name": test_resident['name'],
            "phone": test_resident.get('phone'),
            "emergency_contact": test_resident.get('emergency_contact'),
            "emergency_phone": test_resident.get('emergency_phone'),
            "deposit": test_resident.get('deposit', 0.0),
            "lease_end_date": original_lease_end
        }
        
        requests.put(f'{base_url}/residents/{resident_id}', 
                    json=restore_data, headers=headers)
        
        print(f"   ✅ 已恢復原始租約到期日")
        return True
            
    except Exception as e:
        print(f"❌ 住戶更新租約到期日期測試錯誤: {e}")
        return False

def test_frontend_edit_form_simplified():
    """測試前端編輯表單簡化"""
    print(f"\n🖥️ 測試前端編輯表單簡化...")
    
    try:
        # 檢查前端文件是否正確修改
        residents_file = 'frontend/pages/residents.py'
        
        if os.path.exists(residents_file):
            with open(residents_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"   檢查 {residents_file}:")
            
            # 檢查是否移除了展期功能
            removed_features = [
                '同時進行租期展期',
                'enable_lease_extension',
                'perform_update_with_extension',
                'extension_type',
                'quick_extension',
                'edit_extension_reason'
            ]
            
            for feature in removed_features:
                if feature not in content:
                    print(f"      ✅ {feature} 已移除")
                else:
                    print(f"      ❌ {feature} 仍存在")
                    return False
            
            # 檢查保留的功能
            kept_features = [
                '租約資訊',
                'edit_lease_end_date',
                'roc_calendar_input',
                'lease_end_date.isoformat()',
                '入住日期',
                '租約到期日',
                'perform_basic_update'
            ]
            
            for feature in kept_features:
                if feature in content:
                    print(f"      ✅ {feature} 已保留")
                else:
                    print(f"      ❌ {feature} 缺失")
                    return False
            
            return True
        else:
            print(f"   ❌ {residents_file} 不存在")
            return False
            
    except Exception as e:
        print(f"❌ 前端編輯表單簡化測試錯誤: {e}")
        return False

def test_backend_date_validation():
    """測試後端日期驗證"""
    print(f"\n🔧 測試後端日期驗證...")
    
    try:
        # 檢查後端文件是否正確修改
        residents_file = 'backend/app/routers/residents.py'
        
        if os.path.exists(residents_file):
            with open(residents_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"   檢查 {residents_file}:")
            
            # 檢查是否添加了日期驗證器
            validation_features = [
                'parse_lease_end_date',
                '@validator(\'lease_end_date\', pre=True)',
                'datetime.strptime(v, \'%Y-%m-%d\').date()',
                'datetime.strptime(v, \'%Y/%m/%d\').date()'
            ]
            
            for feature in validation_features:
                if feature in content:
                    print(f"      ✅ {feature} 已添加")
                else:
                    print(f"      ❌ {feature} 缺失")
                    return False
            
            return True
        else:
            print(f"   ❌ {residents_file} 不存在")
            return False
            
    except Exception as e:
        print(f"❌ 後端日期驗證測試錯誤: {e}")
        return False

if __name__ == "__main__":
    print("🧪 住戶編輯修正功能測試")
    print("=" * 50)
    
    # 1. 測試住戶更新包含租約到期日期
    update_test = test_resident_update_with_lease_date()
    
    # 2. 測試前端編輯表單簡化
    frontend_test = test_frontend_edit_form_simplified()
    
    # 3. 測試後端日期驗證
    backend_test = test_backend_date_validation()
    
    print("\n" + "=" * 50)
    print("📝 測試結果總結:")
    print("1. ✅ 住戶更新租約到期日期" if update_test else "1. ❌ 住戶更新租約到期日期")
    print("2. ✅ 前端編輯表單簡化" if frontend_test else "2. ❌ 前端編輯表單簡化")
    print("3. ✅ 後端日期驗證" if backend_test else "3. ❌ 後端日期驗證")
    
    all_passed = all([update_test, frontend_test, backend_test])
    
    print("\n💡 修正內容:")
    print("🔧 SQLite DateTime 錯誤修正:")
    print("   - 在 ResidentUpdate 模型中添加 lease_end_date 驗證器")
    print("   - 自動將字符串日期轉換為 Python date 對象")
    print("   - 支援 YYYY-MM-DD 和 YYYY/MM/DD 格式")
    print("   - 解決 SQLite DateTime 類型錯誤")
    
    print("\n📝 編輯表單簡化:")
    print("   - 移除「同時進行租期展期」功能")
    print("   - 保留基本的租約到期日期編輯功能")
    print("   - 簡化表單提交邏輯")
    print("   - 移除 perform_update_with_extension 函數")
    print("   - 只保留 perform_basic_update 函數")
    
    print("\n🎯 功能特點:")
    print("   - ✅ 可在編輯住戶時設定租約到期日期")
    print("   - ✅ 可清除租約到期日期（設為 None）")
    print("   - ✅ 使用民國年日曆選擇器")
    print("   - ✅ 自動日期格式轉換")
    print("   - ✅ 簡化的用戶界面")
    
    print(f"\n🏁 測試完成 - {'全部通過' if all_passed else '部分失敗'}")
    
    if all_passed:
        print("\n🎉 住戶編輯修正功能已成功實作並測試通過！")
        print("   現在可以正常編輯住戶租約到期日期，不會出現 SQLite 錯誤。")
