#!/usr/bin/env python3
"""使用正確密碼測試系統功能"""

import requests

def test_with_correct_password():
    """使用正確的密碼測試登入和API功能"""
    # 使用正確的密碼測試登入
    base_url = 'http://localhost:8080'
    login_data = {
        'username': 'admin',
        'password': 'admin5813'
    }

    try:
        print('🔐 使用正確密碼測試登入...')
        response = requests.post(f'{base_url}/auth/login', data=login_data)
        print(f'登入狀態碼: {response.status_code}')
        
        if response.status_code == 200:
            token_data = response.json()
            print('✅ 登入成功！')
            token = token_data['access_token']
            
            # 測試住戶列表 API
            print('\n👥 測試住戶列表 API...')
            headers = {'Authorization': f'Bearer {token}'}
            residents_response = requests.get(f'{base_url}/residents/', headers=headers)
            print(f'住戶列表狀態碼: {residents_response.status_code}')
            
            if residents_response.status_code == 200:
                residents = residents_response.json()
                print(f'✅ 住戶列表 API 正常，返回 {len(residents)} 個住戶')
            else:
                print(f'❌ 住戶列表 API 錯誤: {residents_response.status_code}')
                print(f'錯誤詳情: {residents_response.text}')
                
            # 測試房間列表 API
            print('\n🏠 測試房間列表 API...')
            rooms_response = requests.get(f'{base_url}/rooms/', headers=headers)
            print(f'房間列表狀態碼: {rooms_response.status_code}')
            
            if rooms_response.status_code == 200:
                rooms = rooms_response.json()
                print(f'✅ 房間列表 API 正常，返回 {len(rooms)} 個房間')
                
                # 如果有房間，測試房間刪除功能（但不實際刪除）
                if rooms:
                    print(f'\n🔍 測試房間刪除檢查功能...')
                    # 找一個沒有住戶的房間來測試
                    test_room = None
                    for room in rooms:
                        if room.get('current_occupants', 0) == 0:
                            test_room = room
                            break
                    
                    if test_room:
                        room_id = test_room['id']
                        print(f'測試房間 ID: {room_id} ({test_room["room_number"]})')
                        
                        # 這裡我們不實際刪除，只是測試 API 是否會報錯
                        print('（注意：這裡只是測試 API 響應，不會實際刪除房間）')
                    else:
                        print('所有房間都有住戶，無法測試刪除功能')
                        
            else:
                print(f'❌ 房間列表 API 錯誤: {rooms_response.status_code}')
                
        else:
            print(f'❌ 登入失敗: {response.status_code}')
            print(f'錯誤詳情: {response.text}')
            
    except Exception as e:
        print(f'❌ 測試過程中發生錯誤: {e}')

if __name__ == "__main__":
    print("🧪 使用正確密碼測試系統功能")
    print("=" * 50)
    test_with_correct_password()
    print("\n" + "=" * 50)
    print("🏁 測試完成")
