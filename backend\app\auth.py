from datetime import datetime, timed<PERSON>ta
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2<PERSON><PERSON><PERSON>Bearer
from passlib.context import CryptContext
from jose import JWTError, jwt
from sqlalchemy.orm import Session
from .database import get_db
from .models import User
from .config import settings

# 密碼加密上下文 - 使用更兼容的配置
try:
    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
except Exception:
    # 如果bcrypt有問題，使用備用方案
    import hashlib
    import secrets

    class SimplePwdContext:
        def verify(self, plain_password: str, hashed_password: str) -> bool:
            if hashed_password.startswith('$2b$'):
                # 嘗試使用bcrypt
                try:
                    import bcrypt
                    return bcrypt.checkpw(plain_password.encode('utf-8'), hashed_password.encode('utf-8'))
                except:
                    return False
            else:
                # 使用簡單的SHA256+鹽值
                parts = hashed_password.split('$')
                if len(parts) == 3:
                    salt = parts[1]
                    expected_hash = parts[2]
                    actual_hash = hashlib.sha256((plain_password + salt).encode()).hexdigest()
                    return actual_hash == expected_hash
                return False

        def hash(self, password: str) -> str:
            try:
                import bcrypt
                return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            except:
                # 備用方案：SHA256+隨機鹽值
                salt = secrets.token_hex(16)
                hash_value = hashlib.sha256((password + salt).encode()).hexdigest()
                return f"sha256${salt}${hash_value}"

    pwd_context = SimplePwdContext()

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/login")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """驗證密碼"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """生成密碼雜湊"""
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """創建JWT訪問令牌"""
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
    return encoded_jwt

async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    """獲取當前用戶"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="無效的認證憑據",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    
    user = db.query(User).filter(User.username == username).first()
    if user is None:
        raise credentials_exception
    
    return user

def require_role(required_roles: list):
    """權限檢查裝飾器"""
    def role_checker(current_user: User = Depends(get_current_user)):
        if current_user.role not in required_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="權限不足"
            )
        return current_user
    return role_checker
