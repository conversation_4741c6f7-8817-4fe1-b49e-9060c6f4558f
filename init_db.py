from backend.database import create_tables, SessionLocal
from backend.models import User, UtilityRate
from backend.auth import get_password_hash
from datetime import datetime

def init_database():
    # 創建資料庫表
    create_tables()
    
    db = SessionLocal()
    
    # 創建預設管理員帳號
    admin_user = db.query(User).filter(User.username == "admin").first()
    if not admin_user:
        admin_user = User(
            username="admin",
            password_hash=get_password_hash("admin123"),
            role="admin",
            email="<EMAIL>"
        )
        db.add(admin_user)
    
    # 創建預設費率設定
    default_rate = db.query(UtilityRate).filter(UtilityRate.is_active == True).first()
    if not default_rate:
        default_rate = UtilityRate(
            electricity_rate=5.5,
            monthly_water_fee=200.0,
            effective_date=datetime.utcnow()
        )
        db.add(default_rate)
    
    db.commit()
    db.close()
    print("資料庫初始化完成！")

if __name__ == "__main__":
    init_database()