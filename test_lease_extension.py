#!/usr/bin/env python3
"""測試住戶租期展期功能"""

import requests
import sqlite3
import sys
import os
from datetime import date, datetime, timed<PERSON><PERSON>

def create_test_resident():
    """創建測試住戶"""
    print("🏠 創建測試住戶...")
    
    base_url = 'http://localhost:8080'
    login_data = {
        'username': 'admin',
        'password': 'admin5813'
    }
    
    try:
        # 登入
        response = requests.post(f'{base_url}/auth/login', data=login_data)
        if response.status_code != 200:
            print(f'❌ 登入失敗: {response.status_code}')
            return None
            
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        # 獲取可用房間
        rooms_response = requests.get(f'{base_url}/rooms/', headers=headers)
        if rooms_response.status_code != 200:
            print("❌ 無法獲取房間列表")
            return None
            
        rooms = rooms_response.json()
        if not rooms:
            print("❌ 沒有可用房間")
            return None
        
        # 使用第一個房間
        test_room = rooms[0]
        
        # 創建測試住戶
        resident_data = {
            "name": "測試展期住戶",
            "phone": "0912345678",
            "id_number": "A123456789",
            "emergency_contact": "緊急聯絡人",
            "emergency_phone": "0987654321",
            "room_id": test_room['id'],
            "move_in_date": "2024-01-01",
            "lease_end_date": "2025-01-01",  # 設定一個初始租約到期日
            "deposit": 10000.0
        }
        
        response = requests.post(f'{base_url}/residents', json=resident_data, headers=headers)
        
        if response.status_code == 200:
            resident = response.json()
            print(f"✅ 成功創建測試住戶，ID: {resident['id']}")
            print(f"   姓名: {resident['name']}")
            print(f"   房間: {test_room['room_number']}")
            print(f"   租約到期日: {resident['lease_end_date']}")
            return resident['id'], token
        else:
            print(f"❌ 創建測試住戶失敗: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 創建測試住戶錯誤: {e}")
        return None

def test_lease_extension_api(resident_id, token):
    """測試租期展期 API"""
    print(f"\n📅 測試租期展期 API (住戶 ID: {resident_id})...")
    
    base_url = 'http://localhost:8080'
    headers = {'Authorization': f'Bearer {token}'}
    
    try:
        # 1. 測試獲取租約資訊
        print("   測試獲取租約資訊...")
        response = requests.get(f'{base_url}/residents/{resident_id}/lease-info', headers=headers)
        
        if response.status_code == 200:
            lease_info = response.json()
            print(f"   ✅ 租約資訊獲取成功")
            print(f"      當前到期日: {lease_info.get('lease_end_date')}")
            print(f"      租約狀態: {lease_info.get('lease_status')}")
            print(f"      剩餘天數: {lease_info.get('days_until_expiry')}")
        else:
            print(f"   ❌ 獲取租約資訊失敗: {response.status_code}")
            return False
        
        # 2. 測試正常展期
        print("   測試正常展期（延長6個月）...")
        new_lease_end_date = (date.today() + timedelta(days=180)).isoformat()
        extension_data = {
            "new_lease_end_date": new_lease_end_date,
            "extension_reason": "測試展期功能"
        }
        
        response = requests.post(f'{base_url}/residents/{resident_id}/extend-lease', 
                               json=extension_data, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 正常展期成功")
            print(f"      新到期日: {result['extension_details']['new_lease_end_date']}")
            print(f"      展期天數: {result['extension_details']['extended_by_days']}")
        else:
            print(f"   ❌ 正常展期失敗: {response.status_code} - {response.text}")
            return False
        
        # 3. 測試無效展期（日期早於當前到期日）
        print("   測試無效展期（日期早於當前到期日）...")
        invalid_date = (date.today() - timedelta(days=30)).isoformat()
        invalid_extension_data = {
            "new_lease_end_date": invalid_date,
            "extension_reason": "測試無效展期"
        }
        
        response = requests.post(f'{base_url}/residents/{resident_id}/extend-lease', 
                               json=invalid_extension_data, headers=headers)
        
        if response.status_code == 400:
            print(f"   ✅ 無效展期正確被拒絕: {response.json().get('detail')}")
        else:
            print(f"   ❌ 無效展期未被正確拒絕: {response.status_code}")
            return False
        
        # 4. 測試再次展期
        print("   測試再次展期（延長1年）...")
        new_lease_end_date_2 = (date.today() + timedelta(days=365)).isoformat()
        extension_data_2 = {
            "new_lease_end_date": new_lease_end_date_2,
            "extension_reason": "第二次測試展期"
        }
        
        response = requests.post(f'{base_url}/residents/{resident_id}/extend-lease', 
                               json=extension_data_2, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 再次展期成功")
            print(f"      最新到期日: {result['extension_details']['new_lease_end_date']}")
        else:
            print(f"   ❌ 再次展期失敗: {response.status_code} - {response.text}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 租期展期 API 測試錯誤: {e}")
        return False

def test_inactive_resident_extension(token):
    """測試已退房住戶的展期功能"""
    print(f"\n🚫 測試已退房住戶展期...")
    
    base_url = 'http://localhost:8080'
    headers = {'Authorization': f'Bearer {token}'}
    
    try:
        # 獲取所有住戶（包括已退房）
        response = requests.get(f'{base_url}/residents?active_only=false', headers=headers)
        if response.status_code != 200:
            print("   ❌ 無法獲取住戶列表")
            return False
        
        residents = response.json()
        inactive_residents = [r for r in residents if not r.get('is_active')]
        
        if not inactive_residents:
            print("   ℹ️  沒有已退房住戶可測試")
            return True
        
        # 使用第一個已退房住戶測試
        inactive_resident = inactive_residents[0]
        print(f"   使用已退房住戶: {inactive_resident['name']} (ID: {inactive_resident['id']})")
        
        # 嘗試展期
        extension_data = {
            "new_lease_end_date": (date.today() + timedelta(days=180)).isoformat(),
            "extension_reason": "測試已退房住戶展期"
        }
        
        response = requests.post(f'{base_url}/residents/{inactive_resident["id"]}/extend-lease', 
                               json=extension_data, headers=headers)
        
        if response.status_code == 400:
            print(f"   ✅ 已退房住戶展期正確被拒絕: {response.json().get('detail')}")
            return True
        else:
            print(f"   ❌ 已退房住戶展期未被正確拒絕: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 已退房住戶展期測試錯誤: {e}")
        return False

def test_frontend_api_client():
    """測試前端 API 客戶端"""
    print(f"\n🖥️ 測試前端 API 客戶端...")
    
    try:
        # 檢查前端 API 客戶端是否添加了展期方法
        sys.path.append('frontend')
        from api_client import api_client
        
        # 檢查新方法是否存在
        methods_to_check = [
            'extend_resident_lease',
            'get_resident_lease_info'
        ]
        
        for method_name in methods_to_check:
            if hasattr(api_client, method_name):
                print(f"   ✅ {method_name} 方法已添加")
            else:
                print(f"   ❌ {method_name} 方法缺失")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 前端 API 客戶端測試錯誤: {e}")
        return False

def cleanup_test_resident(resident_id, token):
    """清理測試住戶"""
    print(f"\n🗑️ 清理測試住戶 (ID: {resident_id})...")
    
    base_url = 'http://localhost:8080'
    headers = {'Authorization': f'Bearer {token}'}
    
    try:
        # 先讓住戶退房
        move_out_data = {
            "move_out_date": datetime.now().isoformat()
        }
        
        response = requests.post(f'{base_url}/residents/{resident_id}/move-out', 
                               json=move_out_data, headers=headers)
        
        if response.status_code == 200:
            print("✅ 測試住戶已退房")
        else:
            print(f"⚠️  測試住戶退房失敗: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 清理錯誤: {e}")

if __name__ == "__main__":
    print("🧪 住戶租期展期功能測試")
    print("=" * 60)
    
    # 1. 創建測試住戶
    test_result = create_test_resident()
    
    if test_result:
        resident_id, token = test_result
        
        # 2. 測試展期 API
        extension_test = test_lease_extension_api(resident_id, token)
        
        # 3. 測試已退房住戶展期
        inactive_test = test_inactive_resident_extension(token)
        
        # 4. 清理測試住戶
        cleanup_test_resident(resident_id, token)
    else:
        extension_test = False
        inactive_test = False
    
    # 5. 測試前端 API 客戶端
    frontend_test = test_frontend_api_client()
    
    print("\n" + "=" * 60)
    print("📝 測試結果總結:")
    print("1. ✅ 租期展期 API" if extension_test else "1. ❌ 租期展期 API")
    print("2. ✅ 已退房住戶展期限制" if inactive_test else "2. ❌ 已退房住戶展期限制")
    print("3. ✅ 前端 API 客戶端" if frontend_test else "3. ❌ 前端 API 客戶端")
    
    all_passed = all([extension_test, inactive_test, frontend_test])
    
    print("\n💡 功能說明:")
    print("📅 租期展期功能:")
    print("   - 延長現有住戶的租約到期日")
    print("   - 提供快速展期選項（3個月、6個月、1年、2年）")
    print("   - 支援自定義展期日期")
    print("   - 顯示展期前後對比")
    print("   - 記錄展期原因和操作歷史")
    
    print("\n🔒 驗證邏輯:")
    print("   - 新租約到期日必須晚於原到期日")
    print("   - 新租約到期日必須晚於今天")
    print("   - 僅允許活躍住戶進行展期")
    print("   - 提供詳細的錯誤訊息")
    
    print("\n🎯 使用方式:")
    print("1. 前往「住戶管理」→「住戶列表」")
    print("2. 點擊住戶查看詳情")
    print("3. 在「租期展期」區域點擊「租期展期」按鈕")
    print("4. 選擇快速展期選項或自定義日期")
    print("5. 填寫展期原因（選填）")
    print("6. 確認展期資訊並提交")
    
    print(f"\n🏁 測試完成 - {'全部通過' if all_passed else '部分失敗'}")
