import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, date
from api_client import api_client
from utils import format_currency, format_date

def show_reports_page():
    """顯示報表統計頁面"""
    st.title("📈 報表統計")
    
    # 頁籤設計
    tab1, tab2, tab3 = st.tabs(["營運概況", "收入分析", "住戶分析"])
    
    with tab1:
        show_operation_overview()
    
    with tab2:
        show_income_analysis()
    
    with tab3:
        show_resident_analysis()

def show_operation_overview():
    """顯示營運概況"""
    st.subheader("營運概況")
    
    # 獲取儀表板統計
    stats = api_client.get_dashboard_stats()
    
    if not stats:
        st.error("無法獲取統計資料")
        return
    
    # 關鍵指標
    st.subheader("關鍵營運指標")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            label="總房間數",
            value=stats['total_rooms'],
            help="系統中所有房間的總數"
        )
    
    with col2:
        st.metric(
            label="入住率",
            value=f"{stats['occupancy_rate']}%",
            delta=f"{stats['occupancy_rate'] - 80:.1f}%",
            help="已住房間佔總房間的比例"
        )
    
    with col3:
        st.metric(
            label="住戶總數",
            value=stats['total_residents'],
            help="目前在住的住戶總數"
        )
    
    with col4:
        avg_occupancy = stats['total_residents'] / stats['total_rooms'] if stats['total_rooms'] > 0 else 0
        st.metric(
            label="平均住戶密度",
            value=f"{avg_occupancy:.1f}人/房",
            help="每間房間的平均住戶數"
        )
    
    # 房間狀態分析
    st.subheader("房間狀態分析")
    
    col1, col2 = st.columns(2)
    
    with col1:
        # 房間狀態餅圖
        labels = ['已住房間', '可用房間']
        values = [stats['occupied_rooms'], stats['available_rooms']]
        
        fig = px.pie(
            values=values,
            names=labels,
            title="房間狀態分布",
            color_discrete_sequence=['#ff6b6b', '#4ecdc4']
        )
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # 入住率儀表板
        fig = go.Figure(go.Indicator(
            mode="gauge+number+delta",
            value=stats['occupancy_rate'],
            domain={'x': [0, 1], 'y': [0, 1]},
            title={'text': "入住率 (%)"},
            delta={'reference': 80, 'position': "top"},
            gauge={
                'axis': {'range': [None, 100]},
                'bar': {'color': "darkblue"},
                'steps': [
                    {'range': [0, 50], 'color': "lightgray"},
                    {'range': [50, 80], 'color': "gray"},
                    {'range': [80, 100], 'color': "lightgreen"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 90
                }
            }
        ))
        fig.update_layout(height=400)
        st.plotly_chart(fig, use_container_width=True)
    
    # 收入概況
    st.subheader("收入概況")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric(
            label="當月總收入",
            value=format_currency(stats['monthly_income']),
            help="包含租金和公用事業費的總收入"
        )
    
    with col2:
        avg_income_per_room = stats['monthly_income'] / stats['total_rooms'] if stats['total_rooms'] > 0 else 0
        st.metric(
            label="平均房間收入",
            value=format_currency(avg_income_per_room),
            help="每間房間的平均月收入"
        )
    
    with col3:
        avg_income_per_resident = stats['monthly_income'] / stats['total_residents'] if stats['total_residents'] > 0 else 0
        st.metric(
            label="平均住戶貢獻",
            value=format_currency(avg_income_per_resident),
            help="每位住戶的平均月收入貢獻"
        )

def show_income_analysis():
    """顯示收入分析"""
    st.subheader("收入分析")
    
    # 選擇分析期間
    col1, col2 = st.columns(2)
    
    with col1:
        analysis_year = st.number_input("分析年份", min_value=2020, max_value=2030, value=datetime.now().year)
    
    with col2:
        analysis_month = st.number_input("分析月份", min_value=1, max_value=12, value=datetime.now().month)
    
    # 獲取收入統計
    income_stats = api_client.get_income_summary(analysis_year, analysis_month)
    
    if not income_stats:
        st.info("暫無收入資料")
        return
    
    # 收入結構分析
    st.subheader(f"{analysis_year}年{analysis_month}月收入結構")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("租金收入", format_currency(income_stats['total_rent']))
        rent_ratio = (income_stats['total_rent'] / income_stats['total_income'] * 100) if income_stats['total_income'] > 0 else 0
        st.caption(f"佔總收入 {rent_ratio:.1f}%")
    
    with col2:
        st.metric("公用事業費", format_currency(income_stats['total_utilities']))
        utility_ratio = (income_stats['total_utilities'] / income_stats['total_income'] * 100) if income_stats['total_income'] > 0 else 0
        st.caption(f"佔總收入 {utility_ratio:.1f}%")
    
    with col3:
        st.metric("總收入", format_currency(income_stats['total_income']))
        st.caption("租金 + 公用事業費")
    
    # 收入結構圓餅圖
    col1, col2 = st.columns(2)
    
    with col1:
        fig = px.pie(
            values=[income_stats['total_rent'], income_stats['total_utilities']],
            names=['租金收入', '公用事業費'],
            title="收入結構分布",
            color_discrete_sequence=['#36a2eb', '#ff6384']
        )
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # 付款狀態分析
        fig = px.pie(
            values=[income_stats['paid_amount'], income_stats['pending_amount']],
            names=['已收款', '待收款'],
            title="收款狀態分布",
            color_discrete_sequence=['#4bc0c0', '#ffcd56']
        )
        st.plotly_chart(fig, use_container_width=True)
    
    # 收款分析
    st.subheader("收款分析")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("已收款金額", format_currency(income_stats['paid_amount']))
    
    with col2:
        st.metric("待收款金額", format_currency(income_stats['pending_amount']))
    
    with col3:
        collection_rate = (income_stats['paid_amount'] / income_stats['total_utilities'] * 100) if income_stats['total_utilities'] > 0 else 0
        st.metric("收款率", f"{collection_rate:.1f}%")
    
    # 收款率指標
    fig = go.Figure(go.Indicator(
        mode="gauge+number",
        value=collection_rate,
        domain={'x': [0, 1], 'y': [0, 1]},
        title={'text': "收款率 (%)"},
        gauge={
            'axis': {'range': [None, 100]},
            'bar': {'color': "darkgreen"},
            'steps': [
                {'range': [0, 60], 'color': "lightgray"},
                {'range': [60, 80], 'color': "yellow"},
                {'range': [80, 100], 'color': "lightgreen"}
            ],
            'threshold': {
                'line': {'color': "red", 'width': 4},
                'thickness': 0.75,
                'value': 90
            }
        }
    ))
    fig.update_layout(height=300)
    st.plotly_chart(fig, use_container_width=True)

def show_resident_analysis():
    """顯示住戶分析"""
    st.subheader("住戶分析")
    
    # 獲取住戶資料
    residents = api_client.get_residents(active_only=True)
    all_residents = api_client.get_residents(active_only=False)
    rooms = api_client.get_rooms()
    
    if not residents:
        st.info("暫無住戶資料")
        return
    
    # 住戶概況
    st.subheader("住戶概況")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("在住住戶", len(residents))
    
    with col2:
        moved_out = len(all_residents) - len(residents)
        st.metric("已退房住戶", moved_out)
    
    with col3:
        total_capacity = sum(room['max_occupants'] for room in rooms)
        occupancy_rate = (len(residents) / total_capacity * 100) if total_capacity > 0 else 0
        st.metric("住戶入住率", f"{occupancy_rate:.1f}%")
    
    with col4:
        avg_stay_duration = calculate_average_stay_duration(residents)
        st.metric("平均入住天數", f"{avg_stay_duration:.0f}天")
    
    # 房間住戶分布
    st.subheader("房間住戶分布")
    
    room_occupancy = {}
    for resident in residents:
        room_number = resident.get('room', {}).get('room_number', '未知')
        room_occupancy[room_number] = room_occupancy.get(room_number, 0) + 1
    
    if room_occupancy:
        # 橫條圖
        fig = px.bar(
            x=list(room_occupancy.values()),
            y=list(room_occupancy.keys()),
            orientation='h',
            title="各房間住戶數量",
            labels={'x': '住戶數量', 'y': '房間號'}
        )
        fig.update_layout(height=400)
        st.plotly_chart(fig, use_container_width=True)
    
    # 入住時間分析
    st.subheader("入住時間分析")
    
    # 計算入住月份分布
    move_in_months = {}
    for resident in residents:
        try:
            move_in_date = datetime.fromisoformat(resident['move_in_date'].replace('Z', '+00:00'))
            month_key = f"{move_in_date.year}-{move_in_date.month:02d}"
            move_in_months[month_key] = move_in_months.get(month_key, 0) + 1
        except:
            continue
    
    if move_in_months:
        # 排序月份
        sorted_months = sorted(move_in_months.items())
        months = [item[0] for item in sorted_months]
        counts = [item[1] for item in sorted_months]
        
        fig = px.line(
            x=months,
            y=counts,
            title="入住趨勢分析",
            labels={'x': '月份', 'y': '入住人數'},
            markers=True
        )
        fig.update_layout(xaxis_tickangle=-45)
        st.plotly_chart(fig, use_container_width=True)
    
    # 住戶詳細列表
    st.subheader("住戶詳細資料")
    
    if residents:
        display_data = []
        for resident in residents:
            room_info = resident.get('room', {})
            move_in_date = datetime.fromisoformat(resident['move_in_date'].replace('Z', '+00:00'))
            stay_days = (datetime.now() - move_in_date).days
            
            display_data.append({
                "姓名": resident['name'],
                "房間": room_info.get('room_number', 'N/A'),
                "入住日期": format_date(resident['move_in_date']),
                "入住天數": f"{stay_days}天",
                "押金": format_currency(resident['deposit']),
                "聯絡電話": resident['phone'] or 'N/A'
            })
        
        df = pd.DataFrame(display_data)
        st.dataframe(df, use_container_width=True)
        
        # 匯出功能
        csv = df.to_csv(index=False, encoding='utf-8-sig')
        st.download_button(
            label="📥 匯出住戶資料 (CSV)",
            data=csv,
            file_name=f"住戶資料_{datetime.now().strftime('%Y%m%d')}.csv",
            mime="text/csv"
        )

def calculate_average_stay_duration(residents):
    """計算平均入住天數"""
    if not residents:
        return 0
    
    total_days = 0
    valid_residents = 0
    
    for resident in residents:
        try:
            move_in_date = datetime.fromisoformat(resident['move_in_date'].replace('Z', '+00:00'))
            stay_days = (datetime.now() - move_in_date).days
            total_days += stay_days
            valid_residents += 1
        except:
            continue
    
    return total_days / valid_residents if valid_residents > 0 else 0
