#!/usr/bin/env python3
"""
修正residents表，添加lease_end_date欄位
"""

import sys
import os
sys.path.append('backend')

from sqlalchemy import create_engine, text, inspect
from backend.app.config import settings

def check_residents_table_schema():
    """檢查residents表的當前結構"""
    print("🔍 檢查residents表結構")
    print("=" * 50)
    
    try:
        database_url = settings.database_url
        engine = create_engine(database_url, connect_args={"check_same_thread": False})
        
        with engine.connect() as conn:
            # 檢查表是否存在
            inspector = inspect(engine)
            tables = inspector.get_table_names()
            
            if 'residents' not in tables:
                print("❌ residents表不存在")
                return False
            
            print("✅ residents表存在")
            
            # 檢查表結構
            columns = inspector.get_columns('residents')
            print("\n📋 當前欄位:")
            for col in columns:
                print(f"   - {col['name']}: {col['type']}")
            
            # 檢查是否已有lease_end_date欄位
            column_names = [col['name'] for col in columns]
            if 'lease_end_date' in column_names:
                print("\n✅ lease_end_date欄位已存在")
                return True
            else:
                print("\n❌ lease_end_date欄位不存在，需要添加")
                return False
                
    except Exception as e:
        print(f"❌ 檢查表結構失敗: {e}")
        return False

def add_lease_end_date_column():
    """添加lease_end_date欄位"""
    print("\n🔧 添加lease_end_date欄位")
    print("=" * 50)
    
    try:
        database_url = settings.database_url
        engine = create_engine(database_url, connect_args={"check_same_thread": False})
        
        with engine.connect() as conn:
            # 添加lease_end_date欄位
            migration_sql = """
            ALTER TABLE residents 
            ADD COLUMN lease_end_date DATETIME NULL
            """
            
            print("📤 執行遷移SQL:")
            print(migration_sql)
            
            conn.execute(text(migration_sql))
            conn.commit()
            
            print("✅ lease_end_date欄位添加成功")
            return True
            
    except Exception as e:
        print(f"❌ 添加欄位失敗: {e}")
        return False

def verify_migration():
    """驗證遷移結果"""
    print("\n✅ 驗證遷移結果")
    print("=" * 50)
    
    try:
        database_url = settings.database_url
        engine = create_engine(database_url, connect_args={"check_same_thread": False})
        
        with engine.connect() as conn:
            # 檢查欄位是否存在
            check_sql = """
            SELECT COUNT(*) as count
            FROM pragma_table_info('residents')
            WHERE name = 'lease_end_date'
            """
            
            result = conn.execute(text(check_sql))
            count = result.fetchone()[0]
            
            if count > 0:
                print("✅ lease_end_date欄位驗證成功")
                
                # 顯示更新後的表結構
                inspector = inspect(engine)
                columns = inspector.get_columns('residents')
                print("\n📋 更新後的表結構:")
                for col in columns:
                    marker = "🆕" if col['name'] == 'lease_end_date' else "  "
                    print(f"{marker} {col['name']}: {col['type']}")
                
                return True
            else:
                print("❌ lease_end_date欄位驗證失敗")
                return False
                
    except Exception as e:
        print(f"❌ 驗證失敗: {e}")
        return False

def test_residents_api():
    """測試住戶列表API"""
    print("\n🧪 測試住戶列表API")
    print("=" * 50)
    
    import requests
    
    try:
        # 登入獲取token
        login_data = {
            "username": "admin",
            "password": "admin5813"
        }
        
        response = requests.post(
            "http://localhost:8080/auth/login",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        if response.status_code != 200:
            print(f"❌ 登入失敗: {response.status_code}")
            return False
        
        token = response.json()["access_token"]
        print("✅ 登入成功")
        
        # 測試住戶列表API
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(
            "http://localhost:8080/residents/",
            headers=headers
        )
        
        print(f"   API狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            residents = response.json()
            print(f"✅ 住戶列表API調用成功")
            print(f"   返回 {len(residents)} 筆住戶資料")
            
            # 檢查是否包含lease_end_date欄位
            if residents and 'lease_end_date' in residents[0]:
                print("✅ 住戶資料包含lease_end_date欄位")
            elif not residents:
                print("ℹ️ 資料庫中暫無住戶資料")
            else:
                print("⚠️ 住戶資料缺少lease_end_date欄位")
            
            return True
        else:
            print(f"❌ 住戶列表API調用失敗: {response.status_code}")
            print(f"   錯誤詳情: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🔧 修正residents表lease_end_date欄位")
    print("=" * 60)
    
    # 1. 檢查當前表結構
    has_column = check_residents_table_schema()
    
    # 2. 如果沒有欄位，則添加
    if not has_column:
        success = add_lease_end_date_column()
        if not success:
            print("\n❌ 遷移失敗，請檢查資料庫權限")
            return
        
        # 3. 驗證遷移結果
        if not verify_migration():
            print("\n❌ 遷移驗證失敗")
            return
    
    # 4. 測試API
    api_success = test_residents_api()
    
    print("\n" + "=" * 60)
    if api_success:
        print("🎉 住戶列表API修正完成！")
        print("✅ residents表結構已更新")
        print("✅ lease_end_date欄位已添加")
        print("✅ 住戶列表API正常工作")
        print("✅ 前端頁面應該能正常顯示")
    else:
        print("❌ 修正未完全成功")
        print("請檢查後端服務和資料庫狀態")
    
    print("\n💡 修正說明:")
    print("📋 在residents表中添加了lease_end_date DATETIME NULL欄位")
    print("🔧 現有住戶的租約到期日預設為NULL")
    print("📝 可以通過住戶管理頁面設定租約到期日")

if __name__ == "__main__":
    main()
