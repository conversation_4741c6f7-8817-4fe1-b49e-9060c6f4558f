import os
from pydantic_settings import BaseSettings
from functools import lru_cache

class Settings(BaseSettings):
    """系統配置管理"""
    
    # 應用程式設定
    app_name: str = "租房管理系統"
    app_version: str = "1.0.0"
    debug: bool = True
    port: int = 8080
    
    # 資料庫設定
    database_url: str = "sqlite:///./rental_management.db"
    
    # JWT設定
    secret_key: str = "your-secret-key-change-this-in-production-make-it-long-and-random"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # 業務設定
    default_electricity_rate: float = 5.5
    default_water_fee: float = 200.0
    
    class Config:
        env_file = ".env"

@lru_cache()
def get_settings():
    return Settings()

settings = get_settings()
