# =================== backend/requirements.txt ===================
fastapi==0.104.1
uvicorn==0.24.0
sqlalchemy==2.0.23
pydantic==2.4.2
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
python-dotenv==1.0.0

# =================== backend/.env.example ===================
DATABASE_URL=sqlite:///./rental_management.db
SECRET_KEY=your-secret-key-change-this-in-production-make-it-long-and-random
DEBUG=True
ACCESS_TOKEN_EXPIRE_MINUTES=30
DEFAULT_ELECTRICITY_RATE=5.5
DEFAULT_WATER_FEE=200.0

# =================== backend/app/config.py ===================
import os
from pydantic import BaseSettings
from functools import lru_cache

class Settings(BaseSettings):
    """系統配置管理"""
    
    # 應用程式設定
    app_name: str = "租房管理系統"
    app_version: str = "1.0.0"
    debug: bool = True
    
    # 資料庫設定
    database_url: str = "sqlite:///./rental_management.db"
    
    # JWT設定
    secret_key: str = "your-secret-key-change-this-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # 業務設定
    default_electricity_rate: float = 5.5
    default_water_fee: float = 200.0
    
    class Config:
        env_file = ".env"

@lru_cache()
def get_settings():
    return Settings()

settings = get_settings()

# =================== backend/app/database.py ===================
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from contextlib import contextmanager
from .config import settings

# 創建資料庫引擎
engine = create_engine(
    settings.database_url,
    connect_args={"check_same_thread": False}
)

# 創建會話工廠
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 創建基礎模型類
Base = declarative_base()

def get_db():
    """資料庫依賴注入"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def create_tables():
    """創建資料庫表格"""
    Base.metadata.create_all(bind=engine)

# =================== backend/app/models.py ===================
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Float, ForeignKey, Text
from sqlalchemy.orm import relationship
from datetime import datetime
from .database import Base

class User(Base):
    """用戶模型"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    email = Column(String(100), unique=True, nullable=True)
    role = Column(String(20), default="user")  # admin, manager, user
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    last_login = Column(DateTime, nullable=True)
    
    def to_dict(self):
        return {
            "id": self.id,
            "username": self.username,
            "email": self.email,
            "role": self.role,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "last_login": self.last_login.isoformat() if self.last_login else None
        }

class Room(Base):
    """房間模型"""
    __tablename__ = "rooms"
    
    id = Column(Integer, primary_key=True, index=True)
    room_number = Column(String(20), unique=True, index=True, nullable=False)
    floor = Column(Integer, nullable=True)
    area = Column(Float, nullable=True)
    rent_single = Column(Float, nullable=False)
    rent_double = Column(Float, nullable=False)
    current_occupants = Column(Integer, default=0)
    max_occupants = Column(Integer, default=2)
    status = Column(String(20), default="available")  # available, occupied, partial, maintenance
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 關聯關係
    residents = relationship("Resident", back_populates="room")
    utility_records = relationship("UtilityRecord", back_populates="room")
    
    @property
    def current_rent(self):
        return self.rent_double if self.current_occupants == 2 else self.rent_single
    
    def to_dict(self):
        return {
            "id": self.id,
            "room_number": self.room_number,
            "floor": self.floor,
            "area": self.area,
            "rent_single": self.rent_single,
            "rent_double": self.rent_double,
            "current_occupants": self.current_occupants,
            "max_occupants": self.max_occupants,
            "status": self.status,
            "description": self.description,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "current_rent": self.current_rent
        }

class Resident(Base):
    """住戶模型"""
    __tablename__ = "residents"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    phone = Column(String(20), nullable=True)
    id_number = Column(String(20), unique=True, nullable=False)
    emergency_contact = Column(String(100), nullable=True)
    emergency_phone = Column(String(20), nullable=True)
    room_id = Column(Integer, ForeignKey("rooms.id"), nullable=False)
    move_in_date = Column(DateTime, nullable=False)
    move_out_date = Column(DateTime, nullable=True)
    deposit = Column(Float, default=0.0)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 關聯關係
    room = relationship("Room", back_populates="residents")
    
    @property
    def is_current_resident(self):
        return self.is_active and self.move_out_date is None
    
    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "phone": self.phone,
            "id_number": self.id_number,
            "emergency_contact": self.emergency_contact,
            "emergency_phone": self.emergency_phone,
            "room_id": self.room_id,
            "move_in_date": self.move_in_date.isoformat() if self.move_in_date else None,
            "move_out_date": self.move_out_date.isoformat() if self.move_out_date else None,
            "deposit": self.deposit,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "room": self.room.to_dict() if self.room else None
        }

class UtilityRate(Base):
    """公用事業費率模型"""
    __tablename__ = "utility_rates"
    
    id = Column(Integer, primary_key=True, index=True)
    electricity_rate = Column(Float, nullable=False)
    monthly_water_fee = Column(Float, nullable=False)
    effective_date = Column(DateTime, nullable=False)
    notes = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            "id": self.id,
            "electricity_rate": self.electricity_rate,
            "monthly_water_fee": self.monthly_water_fee,
            "effective_date": self.effective_date.isoformat() if self.effective_date else None,
            "notes": self.notes,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

class UtilityRecord(Base):
    """公用事業使用記錄模型"""
    __tablename__ = "utility_records"
    
    id = Column(Integer, primary_key=True, index=True)
    room_id = Column(Integer, ForeignKey("rooms.id"), nullable=False)
    billing_year = Column(Integer, nullable=False)
    billing_month = Column(Integer, nullable=False)
    previous_electricity_reading = Column(Float, default=0.0)
    current_electricity_reading = Column(Float, nullable=False)
    electricity_usage = Column(Float, nullable=False)
    electricity_rate = Column(Float, nullable=False)
    electricity_cost = Column(Float, nullable=False)
    water_fee = Column(Float, nullable=False)
    total_amount = Column(Float, nullable=False)
    payment_status = Column(String(20), default="pending")  # pending, paid, overdue
    payment_date = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 關聯關係
    room = relationship("Room", back_populates="utility_records")
    
    def to_dict(self):
        return {
            "id": self.id,
            "room_id": self.room_id,
            "billing_year": self.billing_year,
            "billing_month": self.billing_month,
            "previous_electricity_reading": self.previous_electricity_reading,
            "current_electricity_reading": self.current_electricity_reading,
            "electricity_usage": self.electricity_usage,
            "electricity_rate": self.electricity_rate,
            "electricity_cost": self.electricity_cost,
            "water_fee": self.water_fee,
            "total_amount": self.total_amount,
            "payment_status": self.payment_status,
            "payment_date": self.payment_date.isoformat() if self.payment_date else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "room": self.room.to_dict() if self.room else None
        }

# =================== backend/app/auth.py ===================
from datetime import datetime, timedelta
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from passlib.context import CryptContext
from jose import JWTError, jwt
from sqlalchemy.orm import Session
from .database import get_db
from .models import User
from .config import settings

# 密碼加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/login")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """驗證密碼"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """生成密碼雜湊"""
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """創建JWT訪問令牌"""
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
    return encoded_jwt

async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    """獲取當前用戶"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="無效的認證憑據",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    
    user = db.query(User).filter(User.username == username).first()
    if user is None:
        raise credentials_exception
    
    return user

def require_role(required_roles: list):
    """權限檢查裝飾器"""
    def role_checker(current_user: User = Depends(get_current_user)):
        if current_user.role not in required_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="權限不足"
            )
        return current_user
    return role_checker

# =================== backend/app/services.py ===================
from sqlalchemy.orm import Session
from .models import User, Room, Resident, UtilityRate, UtilityRecord
from .auth import get_password_hash, verify_password
from datetime import datetime
from typing import List, Optional

class UserService:
    """用戶服務"""
    
    @staticmethod
    def create_user(db: Session, username: str, password: str, email: str = None, role: str = "user"):
        """創建新用戶"""
        # 檢查用戶名是否已存在
        if db.query(User).filter(User.username == username).first():
            raise ValueError("用戶名已存在")
        
        password_hash = get_password_hash(password)
        user = User(
            username=username,
            password_hash=password_hash,
            email=email,
            role=role
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        return user
    
    @staticmethod
    def authenticate_user(db: Session, username: str, password: str):
        """用戶認證"""
        user = db.query(User).filter(User.username == username).first()
        if not user or not verify_password(password, user.password_hash):
            return None
        
        # 更新最後登入時間
        user.last_login = datetime.utcnow()
        db.commit()
        return user
    
    @staticmethod
    def get_user_by_username(db: Session, username: str):
        """根據用戶名獲取用戶"""
        return db.query(User).filter(User.username == username).first()

class RoomService:
    """房間服務"""
    
    @staticmethod
    def create_room(db: Session, room_data: dict):
        """創建新房間"""
        # 檢查房間號是否已存在
        if db.query(Room).filter(Room.room_number == room_data["room_number"]).first():
            raise ValueError("房間號已存在")
        
        room = Room(**room_data)
        db.add(room)
        db.commit()
        db.refresh(room)
        return room
    
    @staticmethod
    def get_all_rooms(db: Session, include_inactive: bool = False):
        """獲取所有房間"""
        query = db.query(Room)
        if not include_inactive:
            query = query.filter(Room.is_active == True)
        return query.all()
    
    @staticmethod
    def get_room_by_id(db: Session, room_id: int):
        """根據ID獲取房間"""
        return db.query(Room).filter(Room.id == room_id).first()
    
    @staticmethod
    def update_room_occupancy(db: Session, room_id: int):
        """更新房間住戶數量"""
        room = db.query(Room).filter(Room.id == room_id).first()
        if room:
            active_residents = db.query(Resident).filter(
                Resident.room_id == room_id,
                Resident.is_active == True,
                Resident.move_out_date.is_(None)
            ).count()
            
            room.current_occupants = active_residents
            
            # 更新房間狀態
            if active_residents == 0:
                room.status = "available"
            elif active_residents == room.max_occupants:
                room.status = "occupied"
            else:
                room.status = "partial"
            
            db.commit()
            return room
        return None
    
    @staticmethod
    def get_available_rooms(db: Session):
        """獲取可用房間"""
        return db.query(Room).filter(
            Room.is_active == True,
            Room.current_occupants < Room.max_occupants
        ).all()

class ResidentService:
    """住戶服務"""
    
    @staticmethod
    def create_resident(db: Session, resident_data: dict):
        """創建新住戶"""
        # 檢查身份證是否已存在
        existing = db.query(Resident).filter(
            Resident.id_number == resident_data["id_number"],
            Resident.is_active == True
        ).first()
        if existing:
            raise ValueError("身份證號已存在")
        
        resident = Resident(**resident_data)
        db.add(resident)
        db.commit()
        db.refresh(resident)
        
        # 更新房間住戶數量
        RoomService.update_room_occupancy(db, resident.room_id)
        return resident
    
    @staticmethod
    def get_all_residents(db: Session, active_only: bool = True):
        """獲取所有住戶"""
        query = db.query(Resident)
        if active_only:
            query = query.filter(Resident.is_active == True)
        return query.all()
    
    @staticmethod
    def get_resident_by_id(db: Session, resident_id: int):
        """根據ID獲取住戶"""
        return db.query(Resident).filter(Resident.id == resident_id).first()
    
    @staticmethod
    def move_out_resident(db: Session, resident_id: int, move_out_date: datetime):
        """住戶退房"""
        resident = db.query(Resident).filter(Resident.id == resident_id).first()
        if resident:
            resident.move_out_date = move_out_date
            resident.is_active = False
            db.commit()
            
            # 更新房間住戶數量
            RoomService.update_room_occupancy(db, resident.room_id)
            return resident
        return None

class UtilityService:
    """公用事業服務"""
    
    @staticmethod
    def create_utility_rate(db: Session, rate_data: dict):
        """創建新費率"""
        rate = UtilityRate(**rate_data)
        db.add(rate)
        db.commit()
        db.refresh(rate)
        return rate
    
    @staticmethod
    def get_current_utility_rate(db: Session):
        """獲取當前費率"""
        return db.query(UtilityRate).filter(
            UtilityRate.is_active == True
        ).order_by(UtilityRate.effective_date.desc()).first()
    
    @staticmethod
    def get_previous_reading(db: Session, room_id: int, year: int, month: int):
        """獲取上月電表讀數"""
        # 計算上月
        if month == 1:
            prev_year, prev_month = year - 1, 12
        else:
            prev_year, prev_month = year, month - 1
        
        # 查詢上月記錄
        prev_record = db.query(UtilityRecord).filter(
            UtilityRecord.room_id == room_id,
            UtilityRecord.billing_year == prev_year,
            UtilityRecord.billing_month == prev_month
        ).first()
        
        return prev_record.current_electricity_reading if prev_record else 0.0
    
    @staticmethod
    def calculate_monthly_bill(db: Session, room_id: int, year: int, month: int, 
                             current_reading: float):
        """計算月度帳單"""
        room = db.query(Room).filter(Room.id == room_id).first()
        if not room:
            raise ValueError("房間不存在")
        
        # 獲取當前費率
        rate = UtilityService.get_current_utility_rate(db)
        if not rate:
            raise ValueError("費率未設定")
        
        # 獲取上月讀數
        previous_reading = UtilityService.get_previous_reading(db, room_id, year, month)
        
        # 計算用電量
        electricity_usage = current_reading - previous_reading
        electricity_cost = electricity_usage * rate.electricity_rate
        
        # 水費計算（根據住戶數量分攤）
        water_fee = rate.monthly_water_fee / room.current_occupants if room.current_occupants > 0 else rate.monthly_water_fee
        
        total_amount = electricity_cost + water_fee
        
        # 建立記錄
        record = UtilityRecord(
            room_id=room_id,
            billing_year=year,
            billing_month=month,
            previous_electricity_reading=previous_reading,
            current_electricity_reading=current_reading,
            electricity_usage=electricity_usage,
            electricity_rate=rate.electricity_rate,
            electricity_cost=electricity_cost,
            water_fee=water_fee,
            total_amount=total_amount
        )
        
        db.add(record)
        db.commit()
        db.refresh(record)
        return record
    
    @staticmethod
    def get_monthly_bills(db: Session, year: int, month: int):
        """獲取月度帳單"""
        return db.query(UtilityRecord).filter(
            UtilityRecord.billing_year == year,
            UtilityRecord.billing_month == month
        ).all()
    
    @staticmethod
    def update_payment_status(db: Session, bill_id: int, payment_status: str, payment_date: datetime = None):
        """更新付款狀態"""
        bill = db.query(UtilityRecord).filter(UtilityRecord.id == bill_id).first()
        if bill:
            bill.payment_status = payment_status
            if payment_date:
                bill.payment_date = payment_date
            db.commit()
            return bill
        return None