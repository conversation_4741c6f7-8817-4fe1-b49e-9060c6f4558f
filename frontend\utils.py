import streamlit as st
from datetime import datetime, date
import pandas as pd
from typing import Dict, Any, List

def init_session_state():
    """初始化會話狀態"""
    if 'authenticated' not in st.session_state:
        st.session_state.authenticated = False
    if 'user_info' not in st.session_state:
        st.session_state.user_info = None
    if 'access_token' not in st.session_state:
        st.session_state.access_token = None

def login_user(token: str, user_info: Dict[str, Any]):
    """登入用戶"""
    st.session_state.authenticated = True
    st.session_state.access_token = token
    st.session_state.user_info = user_info
    # 登入後預設顯示儀表板
    st.session_state.page = "dashboard"

def logout_user():
    """登出用戶"""
    st.session_state.authenticated = False
    st.session_state.access_token = None
    st.session_state.user_info = None

def format_currency(amount: float) -> str:
    """格式化貨幣"""
    return f"${amount:,.0f}"

def format_date(date_str: str) -> str:
    """格式化日期"""
    if not date_str:
        return "N/A"
    try:
        dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
        return dt.strftime("%Y-%m-%d")
    except:
        return date_str

def format_datetime(datetime_str: str) -> str:
    """格式化日期時間"""
    if not datetime_str:
        return "N/A"
    try:
        dt = datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
        return dt.strftime("%Y-%m-%d %H:%M")
    except:
        return datetime_str

def get_room_status_color(status: str) -> str:
    """獲取房間狀態顏色"""
    colors = {
        "available": "🟢",
        "occupied": "🔴",
        "partial": "🟡",
        "maintenance": "🟠"
    }
    return colors.get(status, "⚪")

def get_payment_status_color(status: str) -> str:
    """獲取付款狀態顏色"""
    colors = {
        "paid": "🟢",
        "pending": "🟡",
        "overdue": "🔴"
    }
    return colors.get(status, "⚪")

def validate_id_number(id_number: str) -> bool:
    """驗證身份證號"""
    if not id_number or len(id_number) != 10:
        return False
    
    # 簡單驗證：第一位為英文字母，其餘為數字
    return id_number[0].isalpha() and id_number[1:].isdigit()

def validate_phone_number(phone: str) -> bool:
    """驗證電話號碼"""
    if not phone:
        return True  # 電話號碼可選
    
    # 移除常見分隔符
    clean_phone = phone.replace('-', '').replace(' ', '').replace('(', '').replace(')', '')
    
    # 檢查是否為純數字且長度合理
    return clean_phone.isdigit() and 8 <= len(clean_phone) <= 12

def create_dataframe_from_dict_list(data: List[Dict[str, Any]]) -> pd.DataFrame:
    """從字典列表創建DataFrame"""
    if not data:
        return pd.DataFrame()
    
    return pd.DataFrame(data)

def show_success_message(message: str):
    """顯示成功訊息"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    st.success(f"✅ [{timestamp}] {message}")

def show_error_message(message: str):
    """顯示錯誤訊息"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    st.error(f"❌ [{timestamp}] {message}")

def show_warning_message(message: str):
    """顯示警告訊息"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    st.warning(f"⚠️ [{timestamp}] {message}")

def show_info_message(message: str):
    """顯示資訊訊息"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    st.info(f"ℹ️ [{timestamp}] {message}")
