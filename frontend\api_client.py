import requests
import streamlit as st
from typing import Dict, Any, Optional, List
import json

class APIClient:
    """API客戶端封裝"""
    
    def __init__(self, base_url: str = "http://localhost:8080"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def _get_headers(self) -> Dict[str, str]:
        """獲取請求頭"""
        headers = {"Content-Type": "application/json"}
        
        if st.session_state.get('access_token'):
            headers["Authorization"] = f"Bearer {st.session_state.access_token}"
        
        return headers
    
    def _handle_response(self, response: requests.Response) -> Optional[Dict[str, Any]]:
        """處理API響應"""
        if response.status_code == 401:
            st.error("認證失效，請重新登入")
            st.session_state.authenticated = False
            st.session_state.access_token = None
            st.rerun()
        
        if not response.ok:
            try:
                error_detail = response.json().get('detail', '未知錯誤')
            except:
                error_detail = response.text or '未知錯誤'

            # 對於特定的錯誤訊息，直接顯示不加"API錯誤:"前綴
            if error_detail in ['房間重複', '房間號已存在']:
                st.error(error_detail)
            else:
                st.error(f"API錯誤: {error_detail}")
            return None
        
        return response.json()
    
    def login(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """用戶登入"""
        try:
            # 使用form-data格式，符合OAuth2PasswordRequestForm的要求
            response = self.session.post(
                f"{self.base_url}/auth/login",
                data={"username": username, "password": password},
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )

            if response.ok:
                token_data = response.json()
                # 獲取用戶資訊
                user_info = self.get_current_user(token_data['access_token'])
                if user_info:
                    return {
                        "token": token_data['access_token'],
                        "user_info": user_info
                    }
            else:
                st.error("登入失敗，請檢查用戶名和密碼")
        except Exception as e:
            st.error(f"登入錯誤: {str(e)}")

        return None
    
    def get_current_user(self, token: str) -> Optional[Dict[str, Any]]:
        """獲取當前用戶資訊"""
        try:
            headers = {"Authorization": f"Bearer {token}"}
            response = self.session.get(
                f"{self.base_url}/auth/me",
                headers=headers
            )
            
            if response.ok:
                return response.json()
        except Exception as e:
            st.error(f"獲取用戶資訊失敗: {str(e)}")
        
        return None

    def change_password(self, current_password: str, new_password: str) -> bool:
        """變更密碼"""
        try:
            password_data = {
                "current_password": current_password,
                "new_password": new_password
            }

            response = self.session.post(
                f"{self.base_url}/auth/change-password",
                headers=self._get_headers(),
                json=password_data
            )

            if response.ok:
                st.success("密碼變更成功！")
                return True
            else:
                error_data = response.json()
                st.error(f"密碼變更失敗: {error_data.get('detail', '未知錯誤')}")
                return False
        except Exception as e:
            st.error(f"變更密碼時發生錯誤: {str(e)}")
            return False

    # 用戶管理API
    def get_all_users(self) -> List[Dict[str, Any]]:
        """獲取所有用戶列表（僅限管理員）"""
        try:
            response = self.session.get(
                f"{self.base_url}/auth/users",
                headers=self._get_headers()
            )

            if response.ok:
                return response.json()
            else:
                error_data = response.json()
                st.error(f"獲取用戶列表失敗: {error_data.get('detail', '未知錯誤')}")
                return []
        except Exception as e:
            st.error(f"獲取用戶列表時發生錯誤: {str(e)}")
            return []

    def create_user(self, username: str, password: str, email: str = None, role: str = "user") -> bool:
        """創建新用戶（僅限管理員）"""
        try:
            user_data = {
                "username": username,
                "password": password,
                "email": email,
                "role": role
            }

            response = self.session.post(
                f"{self.base_url}/auth/users",
                headers=self._get_headers(),
                json=user_data
            )

            if response.ok:
                st.success("用戶創建成功！")
                return True
            else:
                error_data = response.json()
                st.error(f"創建用戶失敗: {error_data.get('detail', '未知錯誤')}")
                return False
        except Exception as e:
            st.error(f"創建用戶時發生錯誤: {str(e)}")
            return False

    def update_user(self, user_id: int, email: str = None, role: str = None, is_active: bool = None) -> bool:
        """更新用戶資訊（僅限管理員）"""
        try:
            update_data = {}
            if email is not None:
                update_data["email"] = email
            if role is not None:
                update_data["role"] = role
            if is_active is not None:
                update_data["is_active"] = is_active

            response = self.session.put(
                f"{self.base_url}/auth/users/{user_id}",
                headers=self._get_headers(),
                json=update_data
            )

            if response.ok:
                st.success("用戶資訊更新成功！")
                return True
            else:
                error_data = response.json()
                st.error(f"更新用戶失敗: {error_data.get('detail', '未知錯誤')}")
                return False
        except Exception as e:
            st.error(f"更新用戶時發生錯誤: {str(e)}")
            return False

    def delete_user(self, user_id: int) -> bool:
        """刪除用戶（僅限管理員）"""
        try:
            response = self.session.delete(
                f"{self.base_url}/auth/users/{user_id}",
                headers=self._get_headers()
            )

            if response.ok:
                st.success("用戶刪除成功！")
                return True
            else:
                error_data = response.json()
                st.error(f"刪除用戶失敗: {error_data.get('detail', '未知錯誤')}")
                return False
        except Exception as e:
            st.error(f"刪除用戶時發生錯誤: {str(e)}")
            return False

    def get_rooms(self, include_inactive: bool = False) -> List[Dict[str, Any]]:
        """獲取房間列表"""
        try:
            params = {"include_inactive": include_inactive}
            response = self.session.get(
                f"{self.base_url}/rooms",
                headers=self._get_headers(),
                params=params
            )
            
            return self._handle_response(response) or []
        except Exception as e:
            st.error(f"獲取房間列表失敗: {str(e)}")
            return []
    
    def create_room(self, room_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """創建新房間"""
        try:
            response = self.session.post(
                f"{self.base_url}/rooms",
                headers=self._get_headers(),
                json=room_data
            )
            
            return self._handle_response(response)
        except Exception as e:
            st.error(f"創建房間失敗: {str(e)}")
            return None
    
    def get_available_rooms(self) -> List[Dict[str, Any]]:
        """獲取可用房間"""
        try:
            response = self.session.get(
                f"{self.base_url}/rooms/available",
                headers=self._get_headers()
            )

            return self._handle_response(response) or []
        except Exception as e:
            st.error(f"獲取可用房間失敗: {str(e)}")
            return []

    def get_room_by_id(self, room_id: int) -> Optional[Dict[str, Any]]:
        """獲取特定房間"""
        try:
            response = self.session.get(
                f"{self.base_url}/rooms/{room_id}",
                headers=self._get_headers()
            )

            return self._handle_response(response)
        except Exception as e:
            st.error(f"獲取房間資訊失敗: {str(e)}")
            return None

    def update_room(self, room_id: int, room_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """更新房間資訊"""
        try:
            response = self.session.put(
                f"{self.base_url}/rooms/{room_id}",
                headers=self._get_headers(),
                json=room_data
            )

            return self._handle_response(response)
        except Exception as e:
            st.error(f"更新房間失敗: {str(e)}")
            return None

    def delete_room(self, room_id: int) -> Optional[Dict[str, Any]]:
        """軟刪除房間"""
        try:
            response = self.session.delete(
                f"{self.base_url}/rooms/{room_id}",
                headers=self._get_headers()
            )

            return self._handle_response(response)
        except Exception as e:
            st.error(f"刪除房間失敗: {str(e)}")
            return None

    def get_residents(self, active_only: bool = True) -> List[Dict[str, Any]]:
        """獲取住戶列表"""
        try:
            params = {"active_only": active_only}
            response = self.session.get(
                f"{self.base_url}/residents",
                headers=self._get_headers(),
                params=params
            )

            return self._handle_response(response) or []
        except Exception as e:
            st.error(f"獲取住戶列表失敗: {str(e)}")
            return []

    def create_resident(self, resident_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """創建新住戶"""
        try:
            response = self.session.post(
                f"{self.base_url}/residents",
                headers=self._get_headers(),
                json=resident_data
            )

            return self._handle_response(response)
        except Exception as e:
            st.error(f"創建住戶失敗: {str(e)}")
            return None

    def get_resident_by_id(self, resident_id: int) -> Optional[Dict[str, Any]]:
        """獲取特定住戶"""
        try:
            response = self.session.get(
                f"{self.base_url}/residents/{resident_id}",
                headers=self._get_headers()
            )

            return self._handle_response(response)
        except Exception as e:
            st.error(f"獲取住戶資訊失敗: {str(e)}")
            return None

    def update_resident(self, resident_id: int, resident_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """更新住戶資訊"""
        try:
            response = self.session.put(
                f"{self.base_url}/residents/{resident_id}",
                headers=self._get_headers(),
                json=resident_data
            )

            return self._handle_response(response)
        except Exception as e:
            st.error(f"更新住戶失敗: {str(e)}")
            return None

    def move_out_resident(self, resident_id: int, move_out_date: str) -> Optional[Dict[str, Any]]:
        """住戶退房"""
        try:
            # 確保日期格式為完整的datetime格式
            if 'T' not in move_out_date:
                # 如果只是日期格式，添加時間部分
                move_out_date = f"{move_out_date}T00:00:00"

            response = self.session.post(
                f"{self.base_url}/residents/{resident_id}/move-out",
                headers=self._get_headers(),
                json={"move_out_date": move_out_date}
            )

            return self._handle_response(response)
        except Exception as e:
            st.error(f"住戶退房失敗: {str(e)}")
            return None

    def extend_resident_lease(self, resident_id: int, new_lease_end_date: str, extension_reason: str = None) -> Optional[Dict[str, Any]]:
        """住戶租期展期"""
        try:
            extension_data = {
                "new_lease_end_date": new_lease_end_date,
                "extension_reason": extension_reason
            }

            response = self.session.post(
                f"{self.base_url}/residents/{resident_id}/extend-lease",
                headers=self._get_headers(),
                json=extension_data
            )

            return self._handle_response(response)
        except Exception as e:
            st.error(f"租期展期失敗: {str(e)}")
            return None

    def get_resident_lease_info(self, resident_id: int) -> Optional[Dict[str, Any]]:
        """獲取住戶租約資訊"""
        try:
            response = self.session.get(
                f"{self.base_url}/residents/{resident_id}/lease-info",
                headers=self._get_headers()
            )

            return self._handle_response(response)
        except Exception as e:
            st.error(f"獲取租約資訊失敗: {str(e)}")
            return None

    def get_current_utility_rate(self) -> Optional[Dict[str, Any]]:
        """獲取當前費率"""
        try:
            response = self.session.get(
                f"{self.base_url}/utilities/rates/current",
                headers=self._get_headers()
            )

            return self._handle_response(response)
        except Exception as e:
            st.error(f"獲取費率失敗: {str(e)}")
            return None

    def get_utility_rates(self) -> List[Dict[str, Any]]:
        """獲取所有費率"""
        try:
            response = self.session.get(
                f"{self.base_url}/utilities/rates",
                headers=self._get_headers()
            )

            return self._handle_response(response) or []
        except Exception as e:
            st.error(f"獲取費率列表失敗: {str(e)}")
            return []

    def get_utility_rate_by_id(self, rate_id: int) -> Optional[Dict[str, Any]]:
        """獲取特定費率"""
        try:
            response = self.session.get(
                f"{self.base_url}/utilities/rates/{rate_id}",
                headers=self._get_headers()
            )

            return self._handle_response(response)
        except Exception as e:
            st.error(f"獲取費率資訊失敗: {str(e)}")
            return None

    def get_current_utility_rate(self) -> Optional[Dict[str, Any]]:
        """獲取當前費率"""
        try:
            response = self.session.get(
                f"{self.base_url}/utilities/rates/current",
                headers=self._get_headers()
            )

            return self._handle_response(response)
        except Exception as e:
            # 如果沒有當前費率，不顯示錯誤訊息
            return None

    def create_utility_rate(self, rate_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """創建新費率"""
        print(f"url: {self.base_url}/utilities/rates, rate_data: {rate_data}")
        try:
            response = self.session.post(
                f"{self.base_url}/utilities/rates",
                headers=self._get_headers(),
                json=rate_data
            )

            return self._handle_response(response)
        except Exception as e:
            st.error(f"創建費率失敗: {str(e)}")
            return None

    def update_utility_rate(self, rate_id: int, rate_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """更新費率"""
        try:
            response = self.session.put(
                f"{self.base_url}/utilities/rates/{rate_id}",
                headers=self._get_headers(),
                json=rate_data
            )

            return self._handle_response(response)
        except Exception as e:
            st.error(f"更新費率失敗: {str(e)}")
            return None

    def delete_utility_rate(self, rate_id: int) -> bool:
        """刪除費率"""
        try:
            response = self.session.delete(
                f"{self.base_url}/utilities/rates/{rate_id}",
                headers=self._get_headers()
            )

            result = self._handle_response(response)
            return result is not None
        except Exception as e:
            st.error(f"刪除費率失敗: {str(e)}")
            return False

    def delete_utility_rate(self, rate_id: int) -> bool:
        """刪除費率"""
        try:
            response = self.session.delete(
                f"{self.base_url}/utilities/rates/{rate_id}",
                headers=self._get_headers()
            )

            if response.status_code == 200:
                st.success("費率已刪除")
                return True
            else:
                error_data = response.json()
                st.error(f"刪除費率失敗: {error_data.get('detail', '未知錯誤')}")
                return False
        except Exception as e:
            st.error(f"刪除費率失敗: {str(e)}")
            return False

    def create_meter_reading(self, reading_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """創建電表抄錄"""
        try:
            response = self.session.post(
                f"{self.base_url}/utilities/readings",
                headers=self._get_headers(),
                json=reading_data
            )

            return self._handle_response(response)
        except Exception as e:
            st.error(f"創建抄錄記錄失敗: {str(e)}")
            return None

    def get_utility_bills(self, year: int, month: int) -> List[Dict[str, Any]]:
        """獲取費用帳單"""
        try:
            params = {"year": year, "month": month}
            response = self.session.get(
                f"{self.base_url}/utilities/bills",
                headers=self._get_headers(),
                params=params
            )

            return self._handle_response(response) or []
        except Exception as e:
            st.error(f"獲取帳單失敗: {str(e)}")
            return []

    def clear_all_utility_rates(self, keep_current: bool = True) -> Optional[Dict[str, Any]]:
        """清除所有費率記錄"""
        try:
            params = {"keep_current": keep_current}
            response = self.session.delete(
                f"{self.base_url}/utilities/rates/clear-all",
                headers=self._get_headers(),
                params=params
            )

            return self._handle_response(response)
        except Exception as e:
            st.error(f"清除費率記錄失敗: {str(e)}")
            return None

    def clear_all_utility_bills(self) -> Optional[Dict[str, Any]]:
        """清除所有帳單記錄"""
        try:
            response = self.session.delete(
                f"{self.base_url}/utilities/bills/clear-all",
                headers=self._get_headers()
            )

            return self._handle_response(response)
        except Exception as e:
            st.error(f"清除帳單記錄失敗: {str(e)}")
            return None

    def delete_utility_bill(self, bill_id: int) -> Optional[Dict[str, Any]]:
        """刪除個別帳單"""
        try:
            response = self.session.delete(
                f"{self.base_url}/utilities/bills/{bill_id}",
                headers=self._get_headers()
            )

            return self._handle_response(response)
        except Exception as e:
            st.error(f"刪除帳單失敗: {str(e)}")
            return None

    def update_payment_status(self, bill_id: int, payment_status: str, payment_date: str = None) -> Optional[Dict[str, Any]]:
        """更新付款狀態"""
        try:
            data = {"payment_status": payment_status}
            if payment_date:
                data["payment_date"] = payment_date

            response = self.session.put(
                f"{self.base_url}/utilities/bills/{bill_id}/payment",
                headers=self._get_headers(),
                json=data
            )

            return self._handle_response(response)
        except Exception as e:
            st.error(f"更新付款狀態失敗: {str(e)}")
            return None

    def get_dashboard_stats(self) -> Optional[Dict[str, Any]]:
        """獲取儀表板統計"""
        try:
            response = self.session.get(
                f"{self.base_url}/reports/dashboard",
                headers=self._get_headers()
            )

            # 不在這裡顯示錯誤，讓_handle_response處理
            return self._handle_response(response)
        except Exception as e:
            # 只在網路連接等異常時顯示錯誤
            st.error(f"網路連接失敗: {str(e)}")
            return None

    def get_income_summary(self, year: int, month: int) -> Optional[Dict[str, Any]]:
        """獲取收入統計"""
        try:
            params = {"year": year, "month": month}
            response = self.session.get(
                f"{self.base_url}/reports/income-summary",
                headers=self._get_headers(),
                params=params
            )

            return self._handle_response(response)
        except Exception as e:
            st.error(f"獲取收入統計失敗: {str(e)}")
            return None

    # 租金管理相關方法
    def get_rent_records(self, year: int = None, month: int = None, room_id: int = None, payment_status: str = None) -> Optional[List[Dict[str, Any]]]:
        """獲取租金記錄"""
        try:
            params = {}
            if year:
                params["year"] = year
            if month:
                params["month"] = month
            if room_id:
                params["room_id"] = room_id
            if payment_status:
                params["payment_status"] = payment_status

            response = self.session.get(
                f"{self.base_url}/rent/",
                headers=self._get_headers(),
                params=params
            )

            return self._handle_response(response)
        except Exception as e:
            st.error(f"獲取租金記錄失敗: {str(e)}")
            return None

    def create_rent_record(self, rent_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """創建租金記錄"""
        try:
            response = self.session.post(
                f"{self.base_url}/rent/",
                headers=self._get_headers(),
                json=rent_data
            )

            return self._handle_response(response)
        except Exception as e:
            st.error(f"創建租金記錄失敗: {str(e)}")
            return None

    def update_rent_record(self, record_id: int, update_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """更新租金記錄"""
        try:
            response = self.session.put(
                f"{self.base_url}/rent/{record_id}",
                headers=self._get_headers(),
                json=update_data
            )

            return self._handle_response(response)
        except Exception as e:
            st.error(f"更新租金記錄失敗: {str(e)}")
            return None

    def delete_rent_record(self, record_id: int) -> bool:
        """刪除租金記錄"""
        try:
            response = self.session.delete(
                f"{self.base_url}/rent/{record_id}",
                headers=self._get_headers()
            )

            result = self._handle_response(response)
            return result is not None
        except Exception as e:
            st.error(f"刪除租金記錄失敗: {str(e)}")
            return False

    def get_rent_statistics(self, year: int = None, month: int = None) -> Optional[Dict[str, Any]]:
        """獲取租金統計"""
        try:
            params = {}
            if year:
                params["year"] = year
            if month:
                params["month"] = month

            response = self.session.get(
                f"{self.base_url}/rent/statistics",
                headers=self._get_headers(),
                params=params
            )

            return self._handle_response(response)
        except Exception as e:
            st.error(f"獲取租金統計失敗: {str(e)}")
            return None

# 全域API客戶端實例
api_client = APIClient()

