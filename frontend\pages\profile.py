import streamlit as st
from utils import show_error_message, show_success_message, show_info_message, show_warning_message
from api_client import APIClient

def show_profile_page():
    """顯示個人資料頁面"""
    st.title("👤 個人資料")
    
    # 獲取當前用戶資訊
    user_info = st.session_state.get('user_info', {})
    
    if not user_info:
        show_error_message("無法獲取用戶資訊")
        return
    
    # 用戶資訊顯示
    st.subheader("📋 基本資訊")
    
    col1, col2 = st.columns(2)
    
    with col1:
        show_info_message(f"**用戶名：** {user_info.get('username', 'N/A')}")
        show_info_message(f"**角色：** {user_info.get('role', 'N/A')}")

    with col2:
        show_info_message(f"**電子郵件：** {user_info.get('email', '未設定')}")
        show_info_message(f"**帳號狀態：** {'啟用' if user_info.get('is_active', False) else '停用'}")
    
    st.markdown("---")
    
    # 變更密碼區塊
    show_change_password_section()

def show_change_password_section():
    """顯示變更密碼區塊"""
    st.subheader("🔐 變更密碼")
    
    with st.form("change_password_form"):
        st.markdown("**密碼安全要求：**")
        st.markdown("- 密碼長度至少6個字符")
        st.markdown("- 新密碼不能與當前密碼相同")
        
        st.markdown("---")
        
        # 當前密碼
        current_password = st.text_input(
            "當前密碼*",
            type="password",
            help="請輸入您的當前密碼"
        )
        
        # 新密碼
        new_password = st.text_input(
            "新密碼*",
            type="password",
            help="請輸入新密碼（至少6個字符）"
        )
        
        # 確認新密碼
        confirm_password = st.text_input(
            "確認新密碼*",
            type="password",
            help="請再次輸入新密碼"
        )
        
        # 提交按鈕
        submitted = st.form_submit_button("🔄 變更密碼", use_container_width=True)
        
        if submitted:
            # 驗證輸入
            if not current_password:
                show_error_message("請輸入當前密碼")
                return
            
            if not new_password:
                show_error_message("請輸入新密碼")
                return
            
            if not confirm_password:
                show_error_message("請確認新密碼")
                return
            
            # 檢查新密碼長度
            if len(new_password) < 6:
                show_error_message("新密碼長度至少需要6個字符")
                return
            
            # 檢查密碼確認
            if new_password != confirm_password:
                show_error_message("新密碼與確認密碼不一致")
                return
            
            # 檢查是否與當前密碼相同
            if current_password == new_password:
                show_error_message("新密碼不能與當前密碼相同")
                return
            
            # 執行密碼變更
            api_client = APIClient()
            
            with st.spinner("正在變更密碼..."):
                success = api_client.change_password(current_password, new_password)
                
                if success:
                    show_success_message("密碼變更成功！系統將自動登出，請重新登入")

                    # 自動清除認證狀態並跳轉到登入頁面
                    for key in ['authenticated', 'token', 'user_info']:
                        if key in st.session_state:
                            del st.session_state[key]

                    # 設置標記以顯示成功訊息
                    st.session_state.password_changed = True

                    # 跳轉到登入頁面
                    st.session_state.page = "login"
                    st.rerun()

def show_password_strength_indicator(password: str):
    """顯示密碼強度指示器"""
    if not password:
        return
    
    strength = 0
    feedback = []
    
    # 長度檢查
    if len(password) >= 6:
        strength += 1
    else:
        feedback.append("至少6個字符")
    
    if len(password) >= 8:
        strength += 1
    else:
        feedback.append("建議8個字符以上")
    
    # 複雜度檢查
    if any(c.isupper() for c in password):
        strength += 1
    else:
        feedback.append("包含大寫字母")
    
    if any(c.islower() for c in password):
        strength += 1
    else:
        feedback.append("包含小寫字母")
    
    if any(c.isdigit() for c in password):
        strength += 1
    else:
        feedback.append("包含數字")
    
    if any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
        strength += 1
    else:
        feedback.append("包含特殊字符")
    
    # 顯示強度
    if strength <= 2:
        show_error_message(f"密碼強度：弱 - 建議改善：{', '.join(feedback[:2])}")
    elif strength <= 4:
        show_warning_message(f"密碼強度：中等 - 可改善：{', '.join(feedback[:1])}")
    else:
        show_success_message("密碼強度：強")
