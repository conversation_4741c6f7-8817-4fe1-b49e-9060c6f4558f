# =================== frontend/pages/utilities.py ===================
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, date
from calendar import monthrange
from api_client import api_client
from utils import (
    format_currency, format_date, get_payment_status_color,
    show_success_message, show_error_message, show_warning_message, show_info_message
)

def show_utilities_page():
    """顯示費用管理頁面"""
    st.title("💰 費用管理")
    
    # 頁籤設計
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "費率設定", "電表抄錄", "帳單管理", "付款管理", "費用統計"
    ])
    
    with tab1:
        show_utility_rates_management()
    
    with tab2:
        show_meter_reading_interface()
    
    with tab3:
        show_bills_management()
    
    with tab4:
        show_payment_management()
    
    with tab5:
        show_utility_statistics()

def show_utility_rates_management():
    """費率設定管理"""
    st.subheader("⚡ 費率設定")
    
    # 顯示當前費率
    show_current_rates()
    
    st.markdown("---")
    
    # 設定新費率
    show_rate_setting_form()

def show_current_rates():
    """顯示當前費率"""
    st.markdown("### 📊 當前費率")
    
    current_rate = api_client.get_current_utility_rate()
    
    if current_rate:
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                label="電費每度",
                value=f"${current_rate['electricity_rate']:.2f}",
                help="每度電費價格"
            )
        
        with col2:
            st.metric(
                label="月度水費",
                value=format_currency(current_rate['monthly_water_fee']),
                help="每月固定水費"
            )
        
        with col3:
            effective_date = format_date(current_rate['effective_date'])
            st.metric(
                label="生效日期",
                value=effective_date,
                help="費率開始生效的日期"
            )
        
        with col4:
            st.metric(
                label="設定狀態",
                value="🟢 啟用中",
                help="當前費率狀態"
            )
        
        # 顯示費率備註
        if current_rate.get('notes'):
            st.info(f"📝 備註: {current_rate['notes']}")
    else:
        show_warning_message("尚未設定費率，請先設定系統費率")

def show_rate_setting_form():
    """顯示費率設定表單"""
    st.markdown("### 🔧 設定新費率")
    
    with st.form("utility_rates_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            electricity_rate = st.number_input(
                "電費每度 (TWD)",
                min_value=0.0,
                value=5.5,
                step=0.1,
                help="每度電費價格，單位：新台幣"
            )
            
            effective_date = st.date_input(
                "生效日期",
                value=date.today(),
                help="新費率開始生效的日期"
            )
        
        with col2:
            monthly_water_fee = st.number_input(
                "月度水費 (TWD)",
                min_value=0,
                value=200,
                step=10,
                help="每月固定水費，單位：新台幣"
            )
            
            notes = st.text_area(
                "費率備註",
                placeholder="請輸入費率調整原因或相關說明",
                help="費率調整的原因說明"
            )
        
        # 費率試算
        st.markdown("#### 💡 費率試算")
        col3, col4 = st.columns(2)
        
        with col3:
            sample_usage = st.number_input("試算用電量 (度)", value=100, min_value=0)
        
        with col4:
            sample_occupants = st.number_input("試算住戶數", value=1, min_value=1, max_value=2)
        
        # 計算試算結果
        if sample_usage > 0 and sample_occupants > 0:
            sample_electricity_cost = sample_usage * electricity_rate
            sample_water_cost = monthly_water_fee / sample_occupants
            sample_total = sample_electricity_cost + sample_water_cost
            
            st.info(f"""
            **試算結果：**
            - 電費：{format_currency(sample_electricity_cost)} ({sample_usage}度 × ${electricity_rate:.2f})
            - 水費：{format_currency(sample_water_cost)} (${monthly_water_fee} ÷ {sample_occupants}人)
            - 總計：{format_currency(sample_total)}
            """)
        
        submitted = st.form_submit_button("💾 設定新費率", use_container_width=True)
        
        if submitted:
            if electricity_rate <= 0 or monthly_water_fee <= 0:
                show_error_message("費率必須大於0")
            elif effective_date < date.today():
                show_warning_message("生效日期建議不要早於今天")
            else:
                rate_data = {
                    "electricity_rate": electricity_rate,
                    "monthly_water_fee": monthly_water_fee,
                    "effective_date": effective_date.isoformat(),
                    "notes": notes if notes else None
                }
                
                with st.spinner("正在設定費率..."):
                    result = api_client.create_utility_rate(rate_data)
                    
                    if result:
                        show_success_message("費率設定成功！")
                        st.rerun()

def show_meter_reading_interface():
    """電表抄錄介面"""
    st.subheader("📊 電表抄錄")
    
    # 選擇抄錄月份
    col1, col2 = st.columns(2)
    
    with col1:
        reading_year = st.selectbox(
            "抄錄年份",
            options=range(2020, 2030),
            index=datetime.now().year - 2020
        )
    
    with col2:
        reading_month = st.selectbox(
            "抄錄月份", 
            options=range(1, 13),
            index=datetime.now().month - 1
        )
    
    # 獲取房間列表
    rooms = api_client.get_rooms()
    
    if not rooms:
        show_warning_message("沒有房間資料")
        return
    
    # 顯示抄錄介面
    show_batch_meter_reading(rooms, reading_year, reading_month)

def show_batch_meter_reading(rooms, year, month):
    """批量電表抄錄介面"""
    st.markdown("### 📋 批量電表抄錄")
    
    # 篩選有住戶的房間
    occupied_rooms = [room for room in rooms if room['current_occupants'] > 0]
    
    if not occupied_rooms:
        show_info_message("目前沒有有住戶的房間需要抄錄")
        return
    
    # 檢查已抄錄的房間
    existing_bills = api_client.get_utility_bills(year, month)
    recorded_room_ids = [bill['room_id'] for bill in existing_bills]
    
    st.info(f"📅 抄錄月份：{year}年{month}月")
    st.info(f"🏠 需抄錄房間：{len(occupied_rooms)}間，已抄錄：{len(recorded_room_ids)}間")
    
    # 顯示抄錄表單
    with st.form("batch_meter_reading_form"):
        readings_data = []
        
        for room in occupied_rooms:
            if room['id'] in recorded_room_ids:
                continue  # 跳過已抄錄的房間
            
            st.markdown(f"#### 🏠 房間 {room['room_number']}")
            
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.text(f"住戶：{room['current_occupants']}人")
                st.text(f"狀態：{room['status']}")
            
            with col2:
                # 獲取上月讀數
                previous_reading = get_previous_reading(room['id'], year, month)
                st.text(f"上月讀數：{previous_reading:.0f}")
            
            with col3:
                current_reading = st.number_input(
                    "本月讀數",
                    min_value=previous_reading,
                    value=previous_reading,
                    step=1,
                    key=f"reading_{room['id']}",
                    help="請輸入本月電表讀數"
                )
            
            with col4:
                usage = current_reading - previous_reading
                st.text(f"用電量：{usage:.0f} 度")
                
                # 計算預估費用
                current_rate = api_client.get_current_utility_rate()
                if current_rate:
                    est_cost = usage * current_rate['electricity_rate']
                    water_cost = current_rate['monthly_water_fee'] / room['current_occupants']
                    total_cost = est_cost + water_cost
                    st.text(f"預估總費用：{format_currency(total_cost)}")
            
            readings_data.append({
                "room_id": room['id'],
                "room_number": room['room_number'],
                "current_reading": current_reading,
                "usage": usage
            })
            
            st.divider()
        
        if readings_data:
            # 顯示抄錄摘要
            total_usage = sum(r['usage'] for r in readings_data)
            st.markdown(f"### 📊 抄錄摘要")
            st.info(f"本次抄錄房間：{len(readings_data)}間，總用電量：{total_usage:.0f}度")
            
            # 提交抄錄
            submitted = st.form_submit_button("📝 提交抄錄記錄", use_container_width=True)
            
            if submitted:
                success_count = 0
                
                with st.spinner("正在處理抄錄記錄..."):
                    for reading in readings_data:
                        reading_data = {
                            "room_id": reading['room_id'],
                            "billing_year": year,
                            "billing_month": month,
                            "current_electricity_reading": reading['current_reading']
                        }
                        
                        result = api_client.create_meter_reading(reading_data)
                        if result:
                            success_count += 1
                
                if success_count == len(readings_data):
                    show_success_message(f"成功提交 {success_count} 筆抄錄記錄！")
                    st.rerun()
                else:
                    show_warning_message(f"成功提交 {success_count}/{len(readings_data)} 筆記錄")
        else:
            show_info_message("所有房間本月已完成抄錄")

def get_previous_reading(room_id, year, month):
    """獲取上月電表讀數"""
    try:
        # 計算上月
        if month == 1:
            prev_year, prev_month = year - 1, 12
        else:
            prev_year, prev_month = year, month - 1
        
        # 獲取上月帳單
        prev_bills = api_client.get_utility_bills(prev_year, prev_month)
        
        for bill in prev_bills:
            if bill['room_id'] == room_id:
                return bill['current_electricity_reading']
        
        return 0.0
    except:
        return 0.0

def show_bills_management():
    """帳單管理"""
    st.subheader("📋 帳單管理")
    
    # 選擇查看月份
    col1, col2, col3 = st.columns([1, 1, 1])
    
    with col1:
        bill_year = st.selectbox("年份", range(2020, 2030), index=datetime.now().year - 2020)
    
    with col2:
        bill_month = st.selectbox("月份", range(1, 13), index=datetime.now().month - 1)
    
    with col3:
        if st.button("🔍 查詢帳單"):
            st.rerun()
    
    # 獲取帳單數據
    bills = api_client.get_utility_bills(bill_year, bill_month)
    
    if not bills:
        show_info_message(f"{bill_year}年{bill_month}月暫無帳單記錄")
        return
    
    # 顯示帳單摘要
    show_bills_summary(bills, bill_year, bill_month)
    
    # 顯示帳單詳細列表
    show_bills_detail_table(bills)

def show_bills_summary(bills, year, month):
    """顯示帳單摘要"""
    st.markdown(f"### 📊 {year}年{month}月帳單摘要")
    
    # 計算統計資料
    total_bills = len(bills)
    total_amount = sum(bill['total_amount'] for bill in bills)
    paid_bills = [bill for bill in bills if bill['payment_status'] == 'paid']
    pending_bills = [bill for bill in bills if bill['payment_status'] == 'pending']
    overdue_bills = [bill for bill in bills if bill['payment_status'] == 'overdue']
    
    paid_amount = sum(bill['total_amount'] for bill in paid_bills)
    pending_amount = sum(bill['total_amount'] for bill in pending_bills)
    overdue_amount = sum(bill['total_amount'] for bill in overdue_bills)
    
    # 顯示統計指標
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "總帳單數",
            total_bills,
            help="本月總帳單數量"
        )
    
    with col2:
        st.metric(
            "總金額",
            format_currency(total_amount),
            help="本月總應收金額"
        )
    
    with col3:
        collection_rate = (paid_amount / total_amount * 100) if total_amount > 0 else 0
        st.metric(
            "收款率",
            f"{collection_rate:.1f}%",
            help="已收款金額占比"
        )
    
    with col4:
        st.metric(
            "待收款",
            format_currency(pending_amount + overdue_amount),
            help="尚未收款金額"
        )
    
    # 付款狀態分布
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### 📊 付款狀態分布")
        status_data = {
            "狀態": ["已付款", "待付款", "逾期"],
            "數量": [len(paid_bills), len(pending_bills), len(overdue_bills)]
        }
        
        fig = px.pie(
            status_data,
            values="數量",
            names="狀態",
            color_discrete_map={
                "已付款": "#28a745",
                "待付款": "#ffc107", 
                "逾期": "#dc3545"
            }
        )
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        st.markdown("#### 💰 收款金額分布")
        amount_data = {
            "狀態": ["已收款", "待收款", "逾期款"],
            "金額": [paid_amount, pending_amount, overdue_amount]
        }
        
        fig = px.bar(
            amount_data,
            x="狀態",
            y="金額",
            color="狀態",
            color_discrete_map={
                "已收款": "#28a745",
                "待收款": "#ffc107",
                "逾期款": "#dc3545"
            }
        )
        st.plotly_chart(fig, use_container_width=True)

def show_bills_detail_table(bills):
    """顯示帳單詳細表格"""
    st.markdown("### 📋 帳單詳細列表")
    
    # 準備表格數據
    display_data = []
    for bill in bills:
        room_info = bill['room'] if bill['room'] else {}
        
        display_data.append({
            "房間號": room_info.get('room_number', 'N/A'),
            "電表讀數": f"{bill['previous_electricity_reading']:.0f} → {bill['current_electricity_reading']:.0f}",
            "用電量": f"{bill['electricity_usage']:.0f} 度",
            "電費": format_currency(bill['electricity_cost']),
            "水費": format_currency(bill['water_fee']),
            "總金額": format_currency(bill['total_amount']),
            "付款狀態": f"{get_payment_status_color(bill['payment_status'])} {bill['payment_status']}",
            "付款日期": format_date(bill['payment_date']) if bill['payment_date'] else 'N/A'
        })
    
    df = pd.DataFrame(display_data)
    
    # 設定表格樣式
    def highlight_status(val):
        if 'paid' in val.lower():
            return 'color: green'
        elif 'pending' in val.lower():
            return 'color: orange'
        elif 'overdue' in val.lower():
            return 'color: red'
        return ''
    
    styled_df = df.style.applymap(highlight_status, subset=['付款狀態'])
    st.dataframe(styled_df, use_container_width=True)
    
    # 匯出功能
    if st.button("📥 匯出帳單資料"):
        csv = df.to_csv(index=False, encoding='utf-8-sig')
        st.download_button(
            label="💾 下載CSV檔案",
            data=csv,
            file_name=f"bills_{bills[0]['billing_year']}-{bills[0]['billing_month']:02d}.csv",
            mime="text/csv"
        )

def show_payment_management():
    """付款管理"""
    st.subheader("💳 付款管理")
    
    # 選擇查看月份
    col1, col2 = st.columns(2)
    
    with col1:
        pay_year = st.selectbox("年份", range(2020, 2030), index=datetime.now().year - 2020, key="pay_year")
    
    with col2:
        pay_month = st.selectbox("月份", range(1, 13), index=datetime.now().month - 1, key="pay_month")
    
    # 獲取帳單
    bills = api_client.get_utility_bills(pay_year, pay_month)
    
    if not bills:
        show_info_message(f"{pay_year}年{pay_month}月暫無帳單記錄")
        return
    
    # 篩選未付款帳單
    unpaid_bills = [bill for bill in bills if bill['payment_status'] in ['pending', 'overdue']]
    
    if not unpaid_bills:
        show_success_message("本月所有帳單都已付款完成！")
        return
    
    # 顯示付款操作
    show_payment_operations(unpaid_bills)

def show_payment_operations(unpaid_bills):
    """顯示付款操作"""
    st.markdown("### 💰 待付款帳單")
    
    # 選擇帳單
    bill_options = [
        f"{bill['room']['room_number'] if bill['room'] else 'N/A'} - {format_currency(bill['total_amount'])}"
        for bill in unpaid_bills
    ]
    
    selected_bill_idx = st.selectbox(
        "選擇帳單",
        range(len(bill_options)),
        format_func=lambda x: bill_options[x]
    )
    
    if selected_bill_idx is not None:
        selected_bill = unpaid_bills[selected_bill_idx]
        
        # 顯示帳單詳情
        show_bill_details(selected_bill)
        
        # 付款操作
        show_payment_form(selected_bill)

def show_bill_details(bill):
    """顯示帳單詳情"""
    st.markdown("#### 📋 帳單詳情")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.text(f"房間：{bill['room']['room_number'] if bill['room'] else 'N/A'}")
        st.text(f"計費月份：{bill['billing_year']}-{bill['billing_month']:02d}")
        st.text(f"用電量：{bill['electricity_usage']:.0f} 度")
    
    with col2:
        st.text(f"電費：{format_currency(bill['electricity_cost'])}")
        st.text(f"水費：{format_currency(bill['water_fee'])}")
        st.text(f"總金額：{format_currency(bill['total_amount'])}")
    
    with col3:
        st.text(f"付款狀態：{bill['payment_status']}")
        st.text(f"建立日期：{format_date(bill['created_at'])}")
        
        # 計算逾期天數
        if bill['payment_status'] == 'overdue':
            created_date = datetime.fromisoformat(bill['created_at'].replace('Z', '+00:00'))
            overdue_days = (datetime.now() - created_date).days
            st.text(f"逾期天數：{overdue_days} 天")

def show_payment_form(bill):
    """顯示付款表單"""
    st.markdown("#### 💳 付款操作")
    
    with st.form("payment_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            payment_status = st.selectbox(
                "付款狀態",
                options=["paid", "pending", "overdue"],
                format_func=lambda x: {"paid": "已付款", "pending": "待付款", "overdue": "逾期"}[x],
                index=0
            )
        
        with col2:
            payment_date = st.date_input(
                "付款日期",
                value=date.today() if payment_status == "paid" else None,
                disabled=payment_status != "paid"
            )
        
        submitted = st.form_submit_button("💾 更新付款狀態", use_container_width=True)
        
        if submitted:
            payment_date_str = payment_date.isoformat() if payment_date else None
            
            with st.spinner("更新付款狀態..."):
                result = api_client.update_payment_status(
                    bill['id'],
                    payment_status,
                    payment_date_str
                )
                
                if result:
                    show_success_message("付款狀態更新成功！")
                    st.rerun()

def show_utility_statistics():
    """費用統計"""
    st.subheader("📊 費用統計")
    
    # 選擇統計期間
    col1, col2 = st.columns(2)
    
    with col1:
        stat_year = st.selectbox("統計年份", range(2020, 2030), index=datetime.now().year - 2020)
    
    with col2:
        stat_month = st.selectbox("統計月份", range(1, 13), index=datetime.now().month - 1)
    
    # 獲取統計數據
    income_summary = api_client.get_income_summary(stat_year, stat_month)
    
    if not income_summary:
        show_info_message("暫無統計資料")
        return
    
    # 顯示統計圖表
    show_income_statistics(income_summary)
    
    # 顯示趨勢分析
    show_trend_analysis(stat_year, stat_month)

def show_income_statistics(income_summary):
    """顯示收入統計"""
    st.markdown("### 💰 收入統計")
    
    # 基本指標
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "電費收入",
            format_currency(income_summary['electricity_income']),
            help="本月電費總收入"
        )
    
    with col2:
        st.metric(
            "水費收入",
            format_currency(income_summary['water_income']),
            help="本月水費總收入"
        )
    
    with col3:
        st.metric(
            "總收入",
            format_currency(income_summary['total_income']),
            help="本月總收入"
        )
    
    with col4:
        st.metric(
            "收款率",
            f"{income_summary['collection_rate']:.1f}%",
            help="已收款占總收入比例"
        )
    
    # 圖表展示
    col1, col2 = st.columns(2)
    
    with col1:
        # 收入構成
        income_data = {
            "類型": ["電費收入", "水費收入"],
            "金額": [income_summary['electricity_income'], income_summary['water_income']]
        }
        
        fig1 = px.pie(
            income_data,
            values="金額",
            names="類型",
            title="收入構成分析",
            color_discrete_map={"電費收入": "#4CAF50", "水費收入": "#2196F3"}
        )
        st.plotly_chart(fig1, use_container_width=True)
    
    with col2:
        # 收款狀況
        payment_data = {
            "狀態": ["已收款", "未收款"],
            "金額": [income_summary['paid_amount'], income_summary['unpaid_amount']]
        }
        
        fig2 = px.bar(
            payment_data,
            x="狀態",
            y="金額",
            title="收款狀況分析",
            color="狀態",
            color_discrete_map={"已收款": "#28a745", "未收款": "#dc3545"}
        )
        st.plotly_chart(fig2, use_container_width=True)

def show_trend_analysis(year, month):
    """顯示趨勢分析"""
    st.markdown("### 📈 趨勢分析")
    
    # 獲取最近6個月的數據
    months_data = []
    for i in range(6):
        target_month = month - i
        target_year = year
        
        if target_month <= 0:
            target_month += 12
            target_year -= 1
        
        try:
            monthly_data = api_client.get_income_summary(target_year, target_month)
            if monthly_data:
                months_data.append({
                    "年月": f"{target_year}-{target_month:02d}",
                    "總收入": monthly_data['total_income'],
                    "電費收入": monthly_data['electricity_income'],
                    "水費收入": monthly_data['water_income'],
                    "收款率": monthly_data['collection_rate']
                })
        except:
            continue
    
    if months_data:
        # 反轉數據順序（按時間正序）
        months_data.reverse()
        
        df = pd.DataFrame(months_data)
        
        # 收入趨勢圖
        fig = px.line(
            df,
            x="年月",
            y=["總收入", "電費收入", "水費收入"],
            title="收入趨勢圖",
            labels={"value": "金額 (TWD)", "variable": "收入類型"}
        )
        st.plotly_chart(fig, use_container_width=True)
        
        # 收款率趨勢
        fig2 = px.line(
            df,
            x="年月",
            y="收款率",
            title="收款率趨勢",
            labels={"收款率": "收款率 (%)"}
        )
        st.plotly_chart(fig2, use_container_width=True)
        
        # 數據表格
        st.markdown("#### 📊 歷史數據")
        st.dataframe(df, use_container_width=True)
    else:
        show_info_message("暫無足夠的歷史數據進行趨勢分析")