#!/usr/bin/env python3
"""測試綜合帳單功能"""

import requests
import sys
import os
from datetime import date, datetime, timedelta

def get_test_data():
    """獲取測試數據"""
    print("🏠 獲取測試數據...")
    
    base_url = 'http://localhost:8080'
    login_data = {
        'username': 'admin',
        'password': 'admin5813'
    }
    
    try:
        # 登入
        response = requests.post(f'{base_url}/auth/login', data=login_data)
        if response.status_code != 200:
            print(f'❌ 登入失敗: {response.status_code}')
            return None
            
        token = response.json()['access_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        # 獲取房間列表
        response = requests.get(f'{base_url}/rooms/', headers=headers)
        if response.status_code != 200:
            print("❌ 無法獲取房間列表")
            return None
            
        rooms = response.json()
        if not rooms:
            print("❌ 沒有房間")
            return None
        
        # 選擇第一個房間進行測試
        test_room = rooms[0]
        
        print(f"✅ 獲取測試數據成功")
        print(f"   測試房間: {test_room['room_number']}")
        print(f"   住戶數量: {test_room['current_occupants']}")
        print(f"   1人房租金: ${test_room['rent_single']:.2f}")
        print(f"   2人房租金: ${test_room['rent_double']:.2f}")
        
        return token, test_room
            
    except Exception as e:
        print(f"❌ 獲取測試數據錯誤: {e}")
        return None

def test_create_comprehensive_bill(token, room):
    """測試創建綜合帳單"""
    print(f"\n💰 測試創建綜合帳單...")
    
    base_url = 'http://localhost:8080'
    headers = {'Authorization': f'Bearer {token}'}
    
    try:
        # 創建綜合帳單
        now = datetime.now()
        bill_data = {
            "room_id": room['id'],
            "billing_year": now.year,
            "billing_month": now.month,
            "current_electricity_reading": 1000.0,
            "due_date": (date.today() + timedelta(days=30)).isoformat(),
            "notes": "測試綜合帳單"
        }
        
        response = requests.post(f'{base_url}/comprehensive-bills/', 
                               json=bill_data, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            bill = result['bill']
            
            print(f"   ✅ 綜合帳單創建成功")
            print(f"      帳單ID: {bill['id']}")
            print(f"      房間: {bill['room']['room_number']}")
            print(f"      計費期間: {bill['billing_year']}年{bill['billing_month']}月")
            print(f"      住戶數量: {bill['occupant_count']}人")
            print(f"      租金: ${bill['rent_amount']:.2f}")
            print(f"      水費: ${bill['water_fee']:.2f}")
            print(f"      電費: ${bill['electricity_cost']:.2f}")
            print(f"      總金額: ${bill['total_amount']:.2f}")
            
            # 驗證計算邏輯
            expected_rent = room['rent_double'] if room['current_occupants'] == 2 else room['rent_single']
            expected_water = 100.0
            
            if abs(bill['rent_amount'] - expected_rent) < 0.01:
                print(f"      ✅ 租金計算正確")
            else:
                print(f"      ❌ 租金計算錯誤: 期望{expected_rent}, 實際{bill['rent_amount']}")
                return None
            
            if abs(bill['water_fee'] - expected_water) < 0.01:
                print(f"      ✅ 水費計算正確（固定100元）")
            else:
                print(f"      ❌ 水費計算錯誤: 期望{expected_water}, 實際{bill['water_fee']}")
                return None
            
            return bill['id']
        else:
            print(f"   ❌ 創建綜合帳單失敗: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 創建綜合帳單測試錯誤: {e}")
        return None

def test_get_comprehensive_bills(token):
    """測試獲取綜合帳單列表"""
    print(f"\n📋 測試獲取綜合帳單列表...")
    
    base_url = 'http://localhost:8080'
    headers = {'Authorization': f'Bearer {token}'}
    
    try:
        # 獲取所有帳單
        response = requests.get(f'{base_url}/comprehensive-bills/', headers=headers)
        
        if response.status_code == 200:
            bills = response.json()
            print(f"   ✅ 獲取帳單列表成功，共 {len(bills)} 筆帳單")
            
            if bills:
                bill = bills[0]
                print(f"      第一筆帳單: {bill['room']['room_number']} - {bill['billing_year']}年{bill['billing_month']}月")
                print(f"      總金額: ${bill['total_amount']:.2f}")
                print(f"      付款狀態: {bill['payment_status']}")
            
            return True
        else:
            print(f"   ❌ 獲取帳單列表失敗: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 獲取帳單列表測試錯誤: {e}")
        return False

def test_update_payment_status(token, bill_id):
    """測試更新付款狀態"""
    print(f"\n💳 測試更新付款狀態...")
    
    if not bill_id:
        print("   ⚠️  沒有可用的帳單ID")
        return False
    
    base_url = 'http://localhost:8080'
    headers = {'Authorization': f'Bearer {token}'}
    
    try:
        # 更新為已付款
        payment_data = {
            "payment_status": "paid",
            "payment_date": date.today().isoformat()
        }
        
        response = requests.put(f'{base_url}/comprehensive-bills/{bill_id}/payment', 
                              json=payment_data, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            bill = result['bill']
            
            print(f"   ✅ 付款狀態更新成功")
            print(f"      帳單ID: {bill['id']}")
            print(f"      付款狀態: {bill['payment_status']}")
            print(f"      付款日期: {bill['payment_date']}")
            
            return True
        else:
            print(f"   ❌ 更新付款狀態失敗: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 更新付款狀態測試錯誤: {e}")
        return False

def test_monthly_summary(token):
    """測試月度摘要"""
    print(f"\n📊 測試月度摘要...")
    
    base_url = 'http://localhost:8080'
    headers = {'Authorization': f'Bearer {token}'}
    
    try:
        now = datetime.now()
        response = requests.get(f'{base_url}/comprehensive-bills/summary/{now.year}/{now.month}', 
                              headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            summary = result['summary']
            
            print(f"   ✅ 月度摘要獲取成功")
            print(f"      年月: {result['year']}年{result['month']}月")
            print(f"      總帳單數: {summary['total_bills']}")
            print(f"      總金額: ${summary['total_amount']:.2f}")
            print(f"      租金收入: ${summary['total_rent']:.2f}")
            print(f"      水費收入: ${summary['total_water_fee']:.2f}")
            print(f"      電費收入: ${summary['total_electricity_cost']:.2f}")
            print(f"      已付款帳單: {summary['paid_bills']}")
            print(f"      已收金額: ${summary['paid_amount']:.2f}")
            print(f"      未收金額: ${summary['unpaid_amount']:.2f}")
            
            return True
        else:
            print(f"   ❌ 獲取月度摘要失敗: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 月度摘要測試錯誤: {e}")
        return False

def test_frontend_integration():
    """測試前端整合"""
    print(f"\n🖥️ 測試前端整合...")
    
    try:
        # 檢查前端文件是否存在
        files_to_check = [
            'frontend/pages/comprehensive_bills.py',
            'frontend/app.py'
        ]
        
        for file_path in files_to_check:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                print(f"   檢查 {file_path}:")
                
                if file_path.endswith('comprehensive_bills.py'):
                    # 檢查綜合帳單頁面功能
                    features_to_check = [
                        'show_comprehensive_bills_page',
                        'show_bills_list',
                        'show_create_bill_form',
                        'show_monthly_summary',
                        'create_comprehensive_bill',
                        'get_comprehensive_bills'
                    ]
                    
                    for feature in features_to_check:
                        if feature in content:
                            print(f"      ✅ {feature} 功能已實作")
                        else:
                            print(f"      ❌ {feature} 功能缺失")
                            return False
                
                elif file_path.endswith('app.py'):
                    # 檢查主應用整合
                    if 'comprehensive_bills' in content and 'show_comprehensive_bills_page' in content:
                        print(f"      ✅ 綜合帳單頁面已整合到主應用")
                    else:
                        print(f"      ❌ 綜合帳單頁面未正確整合")
                        return False
            else:
                print(f"   ❌ {file_path} 不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 前端整合測試錯誤: {e}")
        return False

if __name__ == "__main__":
    print("🧪 綜合帳單功能測試")
    print("=" * 60)
    
    # 1. 獲取測試數據
    test_data = get_test_data()
    
    if test_data:
        token, test_room = test_data
        
        # 2. 測試創建綜合帳單
        bill_id = test_create_comprehensive_bill(token, test_room)
        
        # 3. 測試獲取帳單列表
        list_test = test_get_comprehensive_bills(token)
        
        # 4. 測試更新付款狀態
        payment_test = test_update_payment_status(token, bill_id)
        
        # 5. 測試月度摘要
        summary_test = test_monthly_summary(token)
        
    else:
        bill_id = None
        list_test = False
        payment_test = False
        summary_test = False
    
    # 6. 測試前端整合
    frontend_test = test_frontend_integration()
    
    print("\n" + "=" * 60)
    print("📝 測試結果總結:")
    print("1. ✅ 創建綜合帳單" if bill_id else "1. ❌ 創建綜合帳單")
    print("2. ✅ 獲取帳單列表" if list_test else "2. ❌ 獲取帳單列表")
    print("3. ✅ 更新付款狀態" if payment_test else "3. ❌ 更新付款狀態")
    print("4. ✅ 月度摘要" if summary_test else "4. ❌ 月度摘要")
    print("5. ✅ 前端整合" if frontend_test else "5. ❌ 前端整合")
    
    all_passed = all([
        bill_id is not None,
        list_test,
        payment_test,
        summary_test,
        frontend_test
    ])
    
    print("\n💡 功能特點:")
    print("💰 綜合帳單功能:")
    print("   - ✅ 整合租金、水費、電費計算")
    print("   - ✅ 租金根據住戶數量自動計算")
    print("   - ✅ 水費固定100元（不論住戶人數）")
    print("   - ✅ 電費根據用電量和費率計算")
    print("   - ✅ 支援付款狀態管理")
    print("   - ✅ 提供月度摘要統計")
    
    print("\n🎯 計算邏輯:")
    print("   - 租金 = 1人房使用rent_single，2人房使用rent_double")
    print("   - 水費 = 固定100元")
    print("   - 電費 = 用電量 × 電費費率")
    print("   - 總金額 = 租金 + 水費 + 電費")
    
    print("\n🖥️ 前端功能:")
    print("   - 📋 綜合帳單列表（支援篩選）")
    print("   - 📝 創建帳單表單（含費用預覽）")
    print("   - 📊 月度摘要統計")
    print("   - 💳 付款狀態管理")
    print("   - 📄 帳單匯出功能（開發中）")
    
    print(f"\n🏁 測試完成 - {'全部通過' if all_passed else '部分失敗'}")
    
    if all_passed:
        print("\n🎉 綜合帳單功能已成功實作並測試通過！")
        print("   現在可以在系統中使用完整的綜合帳單管理功能。")
