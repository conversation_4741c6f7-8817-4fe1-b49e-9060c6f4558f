import streamlit as st
import pandas as pd
from api_client import api_client
from utils import format_currency, get_room_status_color, show_success_message, show_error_message, show_info_message

def show_rooms_page():
    """顯示房間管理頁面"""
    st.title("🏠 房間管理")

    # 檢查是否要顯示刪除確認界面
    if st.session_state.get('show_delete_confirm', False) and st.session_state.get('room_to_delete'):
        show_delete_room_confirmation(st.session_state.room_to_delete)
        return

    # 頁籤設計
    tab1, tab2, tab3 = st.tabs(["房間列表", "新增房間", "房間統計"])

    with tab1:
        show_rooms_list()

    with tab2:
        show_create_room_form()

    with tab3:
        show_rooms_statistics()

def show_rooms_list():
    """顯示房間列表"""
    st.subheader("房間列表")
    
    # 搜尋和篩選
    col1, col2, col3 = st.columns([2, 1, 1])
    
    with col1:
        search_term = st.text_input("🔍 搜尋房間號", placeholder="輸入房間號")
    
    with col2:
        status_filter = st.selectbox("狀態篩選", ["全部", "可用", "已住", "部分住", "維護中"])
    
    with col3:
        if st.button("🔄 刷新數據", key="rooms_refresh"):
            st.rerun()
    
    # 獲取房間數據
    rooms = api_client.get_rooms()
    
    if not rooms:
        show_info_message("暫無房間資料")
        return
    
    # 數據篩選
    filtered_rooms = rooms
    
    if search_term:
        filtered_rooms = [r for r in filtered_rooms if search_term.lower() in r['room_number'].lower()]
    
    if status_filter != "全部":
        status_map = {
            "可用": "available",
            "已住": "occupied", 
            "部分住": "partial",
            "維護中": "maintenance"
        }
        filtered_rooms = [r for r in filtered_rooms if r['status'] == status_map[status_filter]]
    
    # 數據表格
    if filtered_rooms:
        # 準備顯示數據
        display_data = []
        for room in filtered_rooms:
            display_data.append({
                "房間號": room['room_number'],
                "樓層": room['floor'] or 'N/A',
                "坪數": room['area'] or 'N/A',
                "單人租金": format_currency(room['rent_single']),
                "雙人租金": format_currency(room['rent_double']),
                "目前住戶": f"{room['current_occupants']}/{room['max_occupants']}",
                "狀態": f"{get_room_status_color(room['status'])} {room['status']}",
                "現在租金": format_currency(room['current_rent'])
            })
        
        df = pd.DataFrame(display_data)
        st.dataframe(df, use_container_width=True)
        
        # 房間操作
        st.subheader("房間操作")
        
        selected_room_idx = st.selectbox(
            "選擇房間",
            options=range(len(filtered_rooms)),
            format_func=lambda x: f"{x+1}. {filtered_rooms[x]['room_number']}"
        )
        
        if selected_room_idx is not None:
            selected_room = filtered_rooms[selected_room_idx]
            
            col1, col2 = st.columns(2)
            
            with col1:
                if st.button("📋 查看詳情", key="rooms_view_details", use_container_width=True):
                    show_room_details(selected_room)

            with col2:
                if st.button("✏️ 編輯房間", key="rooms_edit", use_container_width=True):
                    st.session_state.editing_room = selected_room
                    st.session_state.show_edit_room_form = True
                    st.rerun()
    else:
        show_info_message("沒有符合條件的房間")

    # 顯示編輯表單
    if st.session_state.get('show_edit_room_form', False) and st.session_state.get('editing_room'):
        st.markdown("---")
        show_edit_room_form(st.session_state.editing_room)

def show_create_room_form():
    """顯示新增房間表單"""
    st.subheader("新增房間")
    
    with st.form("create_room_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            room_number = st.text_input("房間號*", placeholder="例如: A101")
            floor = st.number_input("樓層", min_value=1, max_value=50, value=1)
            area = st.number_input("坪數", value=0.0, step=0.1)

            # 面積驗證和警告
            if area is not None and area < 0:
                st.warning("⚠️ 面積不能為負數")
        
        with col2:
            rent_single = st.number_input("單人租金*", min_value=0, value=10000, step=100)
            rent_double = st.number_input("雙人租金*", min_value=0, value=15000, step=100)
        
        description = st.text_area("房間描述", placeholder="房間設施、特色等")
        
        submitted = st.form_submit_button("🏠 創建房間", use_container_width=True)
        
        if submitted:
            # 驗證輸入
            if not room_number:
                show_error_message("請輸入房間號")
            elif rent_single <= 0 or rent_double <= 0:
                show_error_message("租金必須大於0")
            elif rent_double < rent_single:
                show_error_message("雙人租金應該大於等於單人租金")
            elif area is not None and area < 0:
                show_error_message("面積不能為負數，請輸入 0 或正數")
            else:
                room_data = {
                    "room_number": room_number,
                    "floor": floor,
                    "area": area,  # 允許 0，但在上面已驗證不為負數
                    "rent_single": rent_single,
                    "rent_double": rent_double,
                    "description": description if description else None
                }
                
                with st.spinner("創建中..."):
                    result = api_client.create_room(room_data)
                    
                    if result:
                        show_success_message(f"房間 {room_number} 創建成功！")
                        st.rerun()

def show_room_details(room_data):
    """顯示房間詳情"""
    st.subheader(f"房間 {room_data['room_number']} 詳情")

    col1, col2 = st.columns(2)

    with col1:
        st.metric("目前住戶", f"{room_data['current_occupants']}/{room_data['max_occupants']}")
        st.metric("樓層", room_data['floor'] or 'N/A')
        st.metric("坪數", room_data['area'] or 'N/A')

    with col2:
        st.metric("單人租金", format_currency(room_data['rent_single']))
        st.metric("雙人租金", format_currency(room_data['rent_double']))
        st.metric("現在租金", format_currency(room_data['current_rent']))

    if room_data.get('description'):
        st.text_area("房間描述", value=room_data['description'], disabled=True)

    # 操作按鈕
    st.divider()
    col1, col2, col3 = st.columns([1, 1, 2])

    with col1:
        if st.button("✏️ 編輯房間", key=f"edit_room_{room_data['id']}", use_container_width=True):
            st.session_state.editing_room = room_data
            st.rerun()

    with col2:
        if st.button("🗑️ 刪除房間", key=f"delete_room_{room_data['id']}", type="secondary", use_container_width=True):
            st.session_state.show_delete_confirm = True
            st.session_state.room_to_delete = room_data
            st.rerun()

def show_rooms_statistics():
    """顯示房間統計"""
    st.subheader("房間統計")
    
    rooms = api_client.get_rooms()
    
    if not rooms:
        show_info_message("暫無統計資料")
        return
    
    # 統計指標
    total_rooms = len(rooms)
    available_rooms = len([r for r in rooms if r['status'] == 'available'])
    occupied_rooms = len([r for r in rooms if r['status'] == 'occupied'])
    partial_rooms = len([r for r in rooms if r['status'] == 'partial'])
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("總房間數", total_rooms)
    
    with col2:
        st.metric("可用房間", available_rooms)
    
    with col3:
        st.metric("已滿房間", occupied_rooms)
    
    with col4:
        st.metric("部分住宿", partial_rooms)
    
    # 圖表展示
    import plotly.express as px
    
    # 房間狀態分布
    status_counts = {}
    for room in rooms:
        status = room['status']
        status_counts[status] = status_counts.get(status, 0) + 1
    
    if status_counts:
        fig = px.pie(
            values=list(status_counts.values()),
            names=list(status_counts.keys()),
            title="房間狀態分布"
        )
        st.plotly_chart(fig, use_container_width=True)
    
    # 租金統計
    rent_data = []
    for room in rooms:
        rent_data.append({
            "房間號": room['room_number'],
            "單人租金": room['rent_single'],
            "雙人租金": room['rent_double']
        })
    
    if rent_data:
        df = pd.DataFrame(rent_data)
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.metric("平均單人租金", format_currency(df['單人租金'].mean()))
            st.metric("最高單人租金", format_currency(df['單人租金'].max()))
        
        with col2:
            st.metric("平均雙人租金", format_currency(df['雙人租金'].mean()))
            st.metric("最高雙人租金", format_currency(df['雙人租金'].max()))

def show_edit_room_form(room_data):
    """顯示編輯房間表單"""
    st.subheader(f"編輯房間 - {room_data['room_number']}")

    with st.form("edit_room_form"):
        col1, col2 = st.columns(2)

        with col1:
            floor = st.number_input("樓層", value=room_data.get('floor', 1), min_value=1, max_value=20)
            # 確保面積值不為 None，避免比較錯誤
            area_value = room_data.get('area')
            if area_value is None:
                area_value = 0.0
            area = st.number_input("面積 (坪)", value=area_value, step=0.5)

            # 面積驗證和警告
            if area is not None and area < 0:
                st.warning("⚠️ 面積不應為負數，建議修正為 0 或正數")
            elif area == 0:
                st.info("ℹ️ 面積為 0，這是允許的")

            rent_single = st.number_input("單人租金*", value=room_data['rent_single'], min_value=0.0, step=100.0)

        with col2:
            rent_double = st.number_input("雙人租金*", value=room_data['rent_double'], min_value=0.0, step=100.0)
            max_occupants = st.number_input("最大住戶數", value=room_data.get('max_occupants', 2), min_value=1, max_value=4)
            status = st.selectbox("房間狀態",
                                ["available", "occupied", "partial", "maintenance"],
                                index=["available", "occupied", "partial", "maintenance"].index(room_data.get('status', 'available')))

        description = st.text_area("房間描述", value=room_data.get('description', ''))

        col1, col2, col3 = st.columns(3)

        with col1:
            submitted = st.form_submit_button("💾 更新房間", use_container_width=True)

        with col2:
            if st.form_submit_button("🗑️ 刪除房間", use_container_width=True):
                if room_data['current_occupants'] > 0:
                    show_error_message("房間內有住戶，無法刪除")
                else:
                    result = api_client.delete_room(room_data['id'])
                    if result and result.get('success'):
                        # 清除編輯狀態
                        st.session_state.show_edit_room_form = False
                        st.session_state.editing_room = None
                        st.rerun()

        with col3:
            if st.form_submit_button("❌ 取消", use_container_width=True):
                st.session_state.show_edit_room_form = False
                st.session_state.editing_room = None
                st.rerun()

        if submitted:
            # 驗證輸入
            if rent_single <= 0 or rent_double <= 0:
                show_error_message("租金必須大於0")
            elif rent_double < rent_single:
                show_error_message("雙人租金應該大於等於單人租金")
            elif area is not None and area < 0:
                show_error_message("面積不能為負數，請修正為 0 或正數")
            else:
                update_data = {
                    "floor": floor if floor and floor > 0 else None,
                    "area": area,  # 允許 0，但在上面已驗證不為負數
                    "rent_single": rent_single,
                    "rent_double": rent_double,
                    "max_occupants": max_occupants,
                    "status": status,
                    "description": description if description else None
                }

                with st.spinner("更新中..."):
                    result = api_client.update_room(room_data['id'], update_data)

                    if result:
                        show_success_message(f"房間 {room_data['room_number']} 更新成功！")
                        st.session_state.show_edit_room_form = False
                        st.session_state.editing_room = None
                        st.rerun()

def show_delete_room_confirmation(room_data):
    """顯示刪除房間確認界面"""
    st.subheader("🗑️ 確認刪除房間")
    st.warning("⚠️ 您即將刪除房間，請確認以下資訊：")

    # 顯示房間基本資訊
    with st.container():
        col1, col2 = st.columns(2)

        with col1:
            st.write(f"**房間號碼：** {room_data['room_number']}")
            st.write(f"**樓層：** {room_data['floor'] or 'N/A'}")
            st.write(f"**坪數：** {room_data['area'] or 'N/A'}")

        with col2:
            st.write(f"**目前住戶：** {room_data['current_occupants']}/{room_data['max_occupants']}")
            st.write(f"**單人租金：** {format_currency(room_data['rent_single'])}")
            st.write(f"**雙人租金：** {format_currency(room_data['rent_double'])}")

        if room_data.get('description'):
            st.write(f"**描述：** {room_data['description']}")

    st.divider()

    # 警告文字
    st.error("🚨 **重要提醒：**")
    st.write("• 此操作將刪除房間，但保留歷史資料")
    st.write("• 如果房間內有住戶，將無法刪除")
    st.write("• 刪除後房間將不再顯示在房間列表中")
    st.write("• 此操作無法復原，請謹慎操作")

    st.divider()

    # 確認按鈕
    col1, col2, col3 = st.columns([1, 1, 1])

    with col1:
        if st.button("❌ 取消", key="cancel_delete", use_container_width=True):
            st.session_state.show_delete_confirm = False
            st.session_state.room_to_delete = None
            st.rerun()

    with col3:
        if st.button("🗑️ 確認刪除", key="confirm_delete", type="primary", use_container_width=True):
            # 執行刪除操作
            with st.spinner("正在刪除房間..."):
                result = api_client.delete_room(room_data['id'])

                if result and result.get('success'):
                    st.success(result['message'])
                    st.balloons()
                    # 清除所有相關的 session state
                    st.session_state.show_delete_confirm = False
                    st.session_state.room_to_delete = None
                    # 清除編輯狀態
                    if 'show_edit_room_form' in st.session_state:
                        st.session_state.show_edit_room_form = False
                    if 'editing_room' in st.session_state:
                        st.session_state.editing_room = None
                    # 延遲一下讓用戶看到成功訊息
                    import time
                    time.sleep(1)
                    st.rerun()
                else:
                    # API客戶端會自動顯示錯誤訊息
                    # 保持確認界面開啟，讓用戶可以重試或取消
                    pass
