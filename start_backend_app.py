#!/usr/bin/env python3
"""
啟動 backend/app 版本的後端服務
"""

import sys
import os
import uvicorn

# 添加 backend/app 到 Python 路徑
backend_app_path = os.path.join(os.path.dirname(__file__), 'backend', 'app')
sys.path.insert(0, backend_app_path)

if __name__ == "__main__":
    # 啟動 uvicorn 服務器
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8080,
        reload=True,
        reload_dirs=[backend_app_path]
    )
